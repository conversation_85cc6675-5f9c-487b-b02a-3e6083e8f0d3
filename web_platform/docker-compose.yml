version: "3"

networks:
  backend:
    driver: bridge

services:
  # nginx
  nginx:
    build:
      context: ./dockers/nginx
      args:
        - DOCKER_UID=${DOCKER_UID}
        - FRONTEND_HOST=${FRONTEND_HOST-localhost}
    volumes:
      - ./src/backend/:/etc/dist/backend/
      - ./logs/nginx/:/var/log/nginx/
    networks:
      - backend
    depends_on:
      - backend
    ports:
      - $HOST_NGINX_PORT:80

  # backend
  backend:
    build:
      context: ./dockers/backend
      args:
        - DOCKER_UID=${DOCKER_UID}
        - DOCKER_GID=${DOCKER_GID}
        - INSTALL_XDEBUG=${INSTALL_XDEBUG}
        - XDEBUG_REMOTE_HOST=${XDEBUG_REMOTE_HOST}
        - XDEBUG_REMOTE_CONNECT_BACK=${XDEBUG_REMOTE_CONNECT_BACK}
        - XDEBUG_REMOTE_PORT=${XDEBUG_REMOTE_PORT}
        - XDEBUG_MODE=${XDEBUG_MODE}
        - PHAR_READONLY=Off
    volumes:
      - ./src/backend/:/etc/dist/backend/
      - ./logs/php/:/var/log/php/
      - ./dockers/backend/iot-platform-supervisord-local.conf:/etc/supervisor/conf.d/iot-platform-supervisord.conf
    entrypoint: /etc/entrypoint/entrypoint.sh
    networks:
      - backend
    extra_hosts:
      - host.docker.internal:host-gateway
    depends_on:
      - database
      - redis
      - echo
    expose:
      - "9000" # PHP

  # database
  database:
    image: timescale/timescaledb-postgis:latest-pg11
    volumes:
      - ./database-data:/var/lib/postgresql/data
      # - ./timescale/postgresql.conf:/var/lib/postgresql/data/postgresql.conf
      # - ./timescale/backups:/backups
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
      POSTGRES_INITDB_WALDIR: /var/lib/postgresql/data/pg_wal
      PGDATA: /var/lib/postgresql/data/pg_data
    networks:
      - backend
    ports:
      - $HOST_DATABASE_PORT:5432

  # Redis
  redis:
    image: redis:6.0.5-alpine
    volumes:
      - ./dockers/redis/redis.conf:/usr/local/etc/redis/redis.conf
      - ./dockers/redis/data/:/data/
      - ./logs/redis:/var/log/redis
    entrypoint: ["redis-server", "/usr/local/etc/redis/redis.conf"]
    networks:
      - backend
    expose:
      - "6379"
    ports:
      - $HOST_REDIS_PORT:6379

  # Laravel-echo-server
  echo:
    image: oanhnn/laravel-echo-server
    volumes:
      - ./dockers/laravel-echo-server/laravel-echo-server.json:/app/laravel-echo-server.json
    environment:
      LARAVEL_ECHO_SERVER_AUTH_HOST: $LARAVEL_ECHO_SERVER_AUTH_HOST
      LARAVEL_ECHO_SERVER_HOST: $LARAVEL_ECHO_SERVER_HOST
      LARAVEL_ECHO_SERVER_PORT: $LARAVEL_ECHO_SERVER_PORT
      LARAVEL_ECHO_SERVER_DEBUG: $LARAVEL_ECHO_SERVER_DEBUG
      LARAVEL_ECHO_SERVER_DB: $LARAVEL_ECHO_SERVER_DB
      LARAVEL_ECHO_SERVER_REDIS_HOST: $LARAVEL_ECHO_SERVER_REDIS_HOST
      LARAVEL_ECHO_SERVER_REDIS_PORT: $LARAVEL_ECHO_SERVER_REDIS_PORT
      LARAVEL_ECHO_SERVER_REDIS_PASSWORD: $LARAVEL_ECHO_SERVER_REDIS_PASSWORD
      LARAVEL_ECHO_SERVER_REDIS_DB_BACKEND: $LARAVEL_ECHO_SERVER_REDIS_DB_BACKEND
    networks:
      - backend
    depends_on:
      - redis
    expose:
      - $LARAVEL_ECHO_SERVER_PORT
    ports:
      - $HOST_LARAVEL_ECHO_SERVER_PORT:$LARAVEL_ECHO_SERVER_PORT

volumes:
  database-data:
