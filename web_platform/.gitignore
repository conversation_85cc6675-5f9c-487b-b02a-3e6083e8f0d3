.cursor

## Front end
# See http://help.github.com/ignore-files/ for more about ignoring files.

# compiled output
src/frontend/dist
src/frontend/tmp
src/frontend/out-tsc
# Only exists if <PERSON><PERSON> was run
src/frontend/bazel-out

#config example
src/frontend/proxy.conf.js

# dependencies
src/frontend/node_modules

# compiled output
src/frontend_18/dist
src/frontend_18/tmp
src/frontend_18/out-tsc
# Only exists if <PERSON><PERSON> was run
src/frontend_18/bazel-out

#config example
src/frontend_18/proxy.conf.js

# dependencies
src/frontend_18/node_modules

# profiling files
chrome-profiler-events*.json
speed-measure-plugin*.json

# IDEs and editors
src/frontend/.idea
src/frontend_18/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# IDE - VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
.history/*

# misc
src/frontend/.sass-cache
src/frontend/connect.lock
src/frontend/coverage
src/frontend/libpeerconnection.log
src/frontend_18/.sass-cache
src/frontend_18/connect.lock
src/frontend_18/coverage
src/frontend_18/libpeerconnection.log
npm-debug.log
yarn-error.log
testem.log
src/frontend/typings
src/frontend_18/typings

# System Files
.DS_Store
Thumbs.db
.idea/
*.lock
/src/frontend/package-lock.json
/src/frontend_18/package-lock.json
.metals/
.env
!src/backend/storage/api-docs/api-docs.json
src/backend/storage/app
src/backend/storage/framework
src/backend/storage/logs
src/backend/storage/clockwork

dockers/redis/data
logs/nginx/
logs/php/
logs/redis.log
.vscode/

# dist
dist/
database-data/
.husky/_/husky.sh

#dockers
.dockerignore
.dockerignore.backend
.dockerignore.frontend
build_backend.sh
build_frontend.sh
build_frontend_live.sh
build_backend_live.sh

z_backup/
