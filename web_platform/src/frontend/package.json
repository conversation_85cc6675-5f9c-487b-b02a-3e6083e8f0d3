{"name": "drager-pin-code-management", "description": "Drager Pin Code Management", "author": "Drager", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "start:fast": "ng serve --configuration=development --poll=2000", "build:staging": "ng build -c staging", "build:prod": "ng build --configuration=production", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "18.2", "@angular/cdk": "18.2", "@angular/common": "18.2", "@angular/compiler": "18.2", "@angular/core": "18.2", "@angular/forms": "18.2", "@angular/material": "18.2", "@angular/platform-browser": "18.2", "@angular/platform-browser-dynamic": "18.2", "@angular/router": "18.2", "@ctrl/tinycolor": "^4.1.0", "@delon/abc": "^11.0.2", "@delon/acl": "^18.3.0", "@delon/auth": "^18.3.0", "@delon/theme": "^17.2.0", "@delon/util": "^18.3.0", "@ngx-translate/core": "^15.0.0", "@ngx-translate/http-loader": "^8.0.0", "@odx/angular": "^12.15.0", "@odx/icons": "^3.64.0", "@odx/ui": "^5.2.0", "crypto-js": "^4.1.1", "drag-map": "^2.3.6", "echarts": "^5.4.2", "file-saver": "^2.0.5", "flv.js": "^1.6.2", "hls.js": "^1.4.14", "laravel-echo": "^1.16.1", "moment": "^2.30.1", "moment-timezone": "^0.5.45", "ng-zorro-antd": "^18.2.1", "ngx-echarts": "^18.0.0", "rxjs": "7.8", "screenfull": "^6.0.2", "socket.io-client": "^2.5.0", "tslib": "2.6", "zone.js": "^0.14.10"}, "devDependencies": {"@angular-devkit/build-angular": "^18.2.20", "@angular/cli": "18.2", "@angular/compiler-cli": "18.2", "@types/crypto-js": "4.2", "@types/file-saver": "2.0", "@types/jasmine": "^5.1.8", "@types/lodash": "4.17", "@types/socket.io-client": "^1.4.36", "jasmine-core": "^5.8.0", "karma": "6.4", "karma-chrome-launcher": "3.2", "karma-coverage": "2.2", "karma-jasmine": "5.1", "karma-jasmine-html-reporter": "2.1", "typescript": "5.5"}}