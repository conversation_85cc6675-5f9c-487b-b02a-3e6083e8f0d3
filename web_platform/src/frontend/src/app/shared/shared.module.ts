import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CoreModule } from '@odx/angular';
import { DelonACLModule } from '@delon/acl';
import { RouterModule } from '@angular/router';
import { UiKitModuleModule } from '../ui-kit-module/ui-kit-module.module';
import { TranslateModule } from '@ngx-translate/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CommonIconComponent } from './components/icon/icon.component';
import { IconComponent } from '@odx/angular/components/icon';
import { MatIconModule } from '@angular/material/icon';
import { NewContentHeaderComponent } from './components/new-content-header/new-content-header.component';
import { ImportActionComponent } from './components/import-action/import-action.component'
import { NgxEchartsModule } from 'ngx-echarts';
import { DeviceMapComponent } from './components/device-map/device-map.component';
import { OdxTablePaginationComponent } from './components/odx-table-pagination/odx-table-pagination.component';
//日期选择器
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
// ant design 的下拉选择
import { NzSelectModule } from 'ng-zorro-antd/select';
// 引入collapse
import { NzCollapseModule } from 'ng-zorro-antd/collapse';
// 引入微标
import { NzBadgeModule } from 'ng-zorro-antd/badge';
// 引入对话框
import { NzModalModule } from 'ng-zorro-antd/modal';
// 引入菜单
import { NzMenuModule } from 'ng-zorro-antd/menu';
// 引入tabs单
import { NzTabsModule } from 'ng-zorro-antd/tabs';

// 引入拖拽
import { DragDropModule } from '@angular/cdk/drag-drop';


import { RightPopUpComponent } from './components/right-pop-up/right-pop-up.component';
import { ViewTicPopUpComponent } from './components/view-tic-pop-up/view-tic-pop-up.component';
import { DeviceListComponent } from './components/device-list/device-list.component';
import { DetailChartComponent } from './components/detail-chart/detail-chart.component';
import { DeviceEditComponent } from './components/device-edit/device-edit.component'
import { EventListComponent } from './components/event-list/event-list.component'
import { TeamListComponent } from './components/team-list/team-list.component';
import { TeamEditComponent } from './components/team-edit/team-edit.component';
import { TeamMemberListComponent } from './components/team-member-list/team-member-list.component';
import { MemberEditComponent } from './components/member-edit/member-edit.component';
import { MemberListComponent } from './components/member-list/member-list.component';
import { StackLineComponent } from './components/stack-line/stack-line.component';
import { StackBarComponent } from './components/stack-bar/stack-bar.component';
import { AreaPiecesComponent } from './components/area-pieces/area-pieces.component';
import { DashboardPageComponent } from './components/dashboard-page/dashboard-page.component';
import { DeleteConfirmComponent } from './components/delete-confirm/delete-confirm.component';
import { AlarmPositionComponent } from './components/alarm-position/alarm-position.component';
import { MapItemListComponent } from './components/map-item-list/map-item-list.component';
import { VideoListComponent } from './components/video-list/video-list.component'
import { AddMemberToTeamComponent } from './components/add-member-to-team/add-member-to-team.component';
@NgModule({
  declarations: [
    CommonIconComponent,
    NewContentHeaderComponent,
    ImportActionComponent,
    DeviceMapComponent,
    OdxTablePaginationComponent,
    RightPopUpComponent,
    ViewTicPopUpComponent,
    DetailChartComponent,
    DeviceListComponent,
    DeviceEditComponent,
    TeamListComponent,
    TeamMemberListComponent,
    MemberListComponent,
    TeamEditComponent,
    MemberEditComponent,
    StackLineComponent,
    StackBarComponent,
    AreaPiecesComponent,
    DashboardPageComponent,
    DeleteConfirmComponent,
    AlarmPositionComponent,
    MapItemListComponent,
    VideoListComponent,
    EventListComponent,
    AddMemberToTeamComponent,
  ],
  imports: [
    CommonModule,
    DelonACLModule,
    RouterModule,
    TranslateModule,
    FormsModule,
    ReactiveFormsModule,
    IconComponent,
    MatIconModule,
    UiKitModuleModule,
    NgxEchartsModule,
    NzDatePickerModule,
    NzSelectModule,
    CoreModule,
    NzCollapseModule,
    NzModalModule,
    DragDropModule,
    NzMenuModule,
    NzTabsModule
  ],
  exports: [
    DelonACLModule,
    RouterModule,
    UiKitModuleModule,
    TranslateModule,
    FormsModule,
    ReactiveFormsModule,
    CommonIconComponent,
    NewContentHeaderComponent,
    DeviceMapComponent,
    OdxTablePaginationComponent,
    RightPopUpComponent,
    AddMemberToTeamComponent,
    ViewTicPopUpComponent,
    NgxEchartsModule,
    DetailChartComponent,
    DeviceListComponent,
    DeviceEditComponent,
    TeamListComponent,
    MemberListComponent,
    EventListComponent,
    TeamMemberListComponent,
    TeamEditComponent,
    MemberEditComponent,
    VideoListComponent,
    StackLineComponent,
    StackBarComponent,
    AreaPiecesComponent,
    NzDatePickerModule,
    NzSelectModule,
    DashboardPageComponent,
    CoreModule,
    NzCollapseModule,
    NzBadgeModule,
    ImportActionComponent,
    NzModalModule,
    NzMenuModule,
    NzTabsModule
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class SharedModule { }
