import { Component, Input, SimpleChanges } from '@angular/core';
import { ThemingService, Theme } from '@odx/angular/theming';
import { EChartsOption, LineSeriesOption } from 'echarts';
import * as _ from 'lodash';
@Component({
  selector: 'app-detail-chart',
  templateUrl: './detail-chart.component.html',
  styleUrls: ['./detail-chart.component.scss']
})
export class DetailChartComponent {
  public readonly chartLineColors = {
    light: {
      alarm: { lineColor: '#fff', labelColor: '#fff', splitLine: "002766" },
      warning: { lineColor: '#002766', labelColor: '#002766', splitLine: "002766" },
      normal: { lineColor: '#00DD31', labelColor: '#54595C', splitLine: "002766" },
      fault: { lineColor: '#002766', labelColor: '#002766', splitLine: "002766" },
    },
    dark: {
      alarm: { lineColor: '#fff', labelColor: '#fff', splitLine: "002766" },
      warning: { lineColor: '#002766', labelColor: '#002766', splitLine: "002766" },
      normal: { lineColor: '#00DD31', labelColor: '#9EB0C2', splitLine: "002766" },
      fault: { lineColor: '#002766', labelColor: '#002766', splitLine: "002766" },
    }
  };
  theme: Theme = Theme.LIGHT;
  @Input() chartHeight: number = 50;
  @Input() data!: number[];
  @Input() keys!: string[];
  @Input() set status(status: 'warning' | 'fault' | 'normal' | 'alarm') {
    this._status = status;
    console.log(status)
    this.updateChartLineColors();
  };
  @Input() min?: number;
  @Input() max?: number;
  _status: 'warning' | 'fault' | 'normal' | 'alarm' = 'normal'
  options!: EChartsOption;
  echartsInstance: any;
  colors!: { lineColor: string, labelColor: string, splitLine: string };
  constructor(
    private themingService: ThemingService,
  ) {
    this.themingService.theme$.subscribe((theme: Theme) => {
      this.theme = theme;
      this.updateChartLineColors();
    });
  }
  private updateChartLineColors() {
    this.colors = this.chartLineColors[this.theme][this._status];
    this.updateData();
  }
  updateData() {
    // 验证 keys 数据
    if (!this.keys || !Array.isArray(this.keys) || this.keys.length === 0) {
      console.warn('⚠️ keys 数据无效，将使用默认值:', this.keys);
    }

    let itemData = {
      type: 'line',
      // smooth: true,
      showSymbol: false,
      data: this.data,
      lineStyle: {
        color: this.colors.lineColor,
        type: this._status === 'warning' ? 'dashed' : 'solid',
        width: 2
      }
    } as LineSeriesOption;

    const series = [itemData];
    const min = this.min || 0;
    const max = Math.ceil(_.cloneDeep(this.data)?.sort((a, b) => b - a)?.[0]);
    const split = Math.ceil(_.cloneDeep(this.data)?.reduce((prev, current) => prev + current, 0) / this.data?.length);
    // echarts 配置
    this.options = {
      animation: false,
      tooltip: {
        trigger: 'axis',
        show: false,
      },
      grid: {
        top: 10,
        bottom: 20,
        left: 15,
        right: 10
      },
      xAxis: {
        type: 'category',
        show: true,
        data: this.keys || [],
        axisLabel: {
          show: true,
          color:  this.colors.labelColor,
          fontSize: 10,
          formatter: (value: string) => {
            // console.log('🏷️ xAxis label formatter:', value);
            return value || '--';
          }
        },
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        }
      },
      yAxis: {
        type: 'value',
        axisLine: {
          show: true,
          lineStyle: {
            color: this.colors.splitLine,
            opacity: 0.2
          }
        },
        axisLabel: {
          show: true,
          color: this.colors.labelColor,
          opacity: 0.8,
          fontSize: 10,
          inside: true,
          verticalAlign: "bottom",
        },
        splitLine: {
          lineStyle: {
            color: this.colors.splitLine,
            opacity: 0.2,
            width: 1
          },
        },
        splitNumber: 1,
        minInterval: 0.1,
        min,
        max: this.max && this.max > max ? this.max : max
      },
      series: series,
    };
  }

  //data更新时，更新数据
  onChartInit(echart: any) {
    this.echartsInstance = echart;
  }
  // 传入的数据变化，更新图表
  ngOnChanges(changes: SimpleChanges) {
    console.log('📊 DetailChart ngOnChanges:', changes);

    // 检查 data 或 keys 是否发生变化
    const dataChanged = changes['data'];
    const keysChanged = changes['keys'];

    // 如果 data 为空且不是首次变化，则返回
    if (dataChanged && !dataChanged.isFirstChange() && dataChanged.currentValue?.length === 0) {
      console.log('⚠️ Data 为空，跳过更新');
      return;
    }

    // 如果 data 或 keys 发生了变化，更新图表
    if (dataChanged || keysChanged) {
      // console.log('🔄 更新图表数据:', {
      //   data: this.data,
      //   keys: this.keys,
      //   dataLength: this.data?.length,
      //   keysLength: this.keys?.length
      // });

      this.updateData();
      this.echartsInstance?.setOption(this.options);
    }
  }
}
interface Data {
  date: string[];
  items: Number[];
}
