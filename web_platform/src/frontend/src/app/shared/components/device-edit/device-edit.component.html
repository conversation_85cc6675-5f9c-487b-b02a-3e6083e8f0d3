<div class="device-edit-container">
  <div class="header-container">
    <odx-modal-header>
      <odx-area-header class="header-title">
        {{ title |translate}}
      </odx-area-header>
    </odx-modal-header>
  </div>
    <div class="modal-content">
      <odx-modal-content>
        <div class="content-item">
          <div class="left-item" *ngIf="deviceIds?.length">
            <p class="title-item">{{'device.deviceList' | translate}}</p>
            <div (click)="changeDevice(index)" [ngClass]="{'device-item': true, 'selected-item': item.selected}" *ngFor="let item of deviceList; let index = index">
              <span>{{ item.name }}</span>
              <app-icon *ngIf="item.selected" type="built-in" name="close"></app-icon>
            </div>
          </div>
          <div class="right-item" [ngClass]="deviceIds?.length ? '' : 'full-width'">
            <p  class="title-item">{{'device.info'|translate}}</p>
            <form class="form-item" odxForm [formGroup]="deviceForm">
              <odx-form-field odxLayout="12">
                <odx-form-field-label>{{
                  "device.name" | translate
                }}</odx-form-field-label>
                <input
                  required
                  formControlName="name"
                  [placeholder]="'app.input-placeholder' | translate"
                  odxFormFieldControl
                  [maxlength]="20"
                  [readonly]="currentDevice"
                />
              </odx-form-field>
              <odx-form-field odxLayout="12">
                <odx-form-field-label>{{
                  "device.identifier" | translate
                }}</odx-form-field-label>
                <input
                  required
                  formControlName="identifier"
                  [placeholder]="'app.input-placeholder' | translate"
                  odxFormFieldControl
                  [maxlength]="20"
                />
              </odx-form-field>
              <odx-form-field *ngIf="deviceType == 'tic'" odxLayout="12">
                <odx-form-field-label>{{
                  "device.DateOfManufacture" | translate
                }}</odx-form-field-label>
               <odx-datepicker 
                    odxFormFieldControl
                    [required]="manufactureDateRequired"
                    formControlName="manufacture_date"
                    [clearable]="true"
                    [maxDate]="today"
                  >
                    <input odxDatepickerControl />
                  </odx-datepicker>
              </odx-form-field>
              <odx-form-field  *ngIf="deviceType == 'tic'" odxLayout="12">
                <odx-form-field-label>{{
                  "device.SIMExpired" | translate
                }}</odx-form-field-label>
                <odx-datepicker 
                    odxFormFieldControl
                    [required]="simExpiredDateRequired"
                    formControlName="sim_expired_date"
                    [clearable]="true"
                    [minDate]="today"
                  >
                    <input odxDatepickerControl />
                  </odx-datepicker>
              </odx-form-field>
              <odx-form-field odxLayout="12" *ngIf="!currentDevice || (currentDevice && !currentDevice['associated_members'].length)" >
                <odx-form-field-label>{{
                  "device.lastMember" | translate
                }}</odx-form-field-label>
                <input
                  formControlName="last_members"
                  [placeholder]="'app.input-placeholder' | translate"
                  odxFormFieldControl
                  [maxlength]="20"
                />
              </odx-form-field>
              <odx-form-field odxLayout="12">
                <odx-form-field-label>{{
                  "device.remark" | translate
                }}</odx-form-field-label>
                <textarea
                  formControlName="remark"
                  [placeholder]="'app.input-placeholder' | translate"
                  odxFormFieldControl
                  [maxlength]="20"
                ></textarea>
              </odx-form-field>
               <p *ngIf="deviceType != 'tic'" class="title-item">{{'device.measurementInfo'|translate}}</p>
               <odx-form-field odxLayout="12" *ngIf="deviceType != 'tic'">
                <odx-form-field-label>{{
                  "device.lastCalibrationTime" | translate
                }}</odx-form-field-label>
                <odx-datepicker
                    odxFormFieldControl
                    formControlName="last_calibration_time"
                    readonly
                  >
                    <input odxDatepickerControl />
                  </odx-datepicker>
              </odx-form-field>
               <odx-form-field odxLayout="12" *ngIf="deviceType != 'tic'">
                <odx-form-field-label>{{
                  "device.nextCalibrationTime" | translate
                }}</odx-form-field-label>
                  <odx-datepicker 
                  odxFormFieldControl
                  formControlName="next_calibration_time"
                  [clearable]="true"
                  [minDate]="today"
                >
                  <input odxDatepickerControl />
                </odx-datepicker>
              </odx-form-field>
            </form>
          </div>

        </div>
      </odx-modal-content>
    </div>
    <odx-modal-footer
      odxLayout="flex "
      class="justify-end"
    >
      <button odxButton (click)="closeModalWithCancel()">
        {{ "cancel" | translate }}
      </button>
      <button
        [odxLoadingSpinner]="loading"
        odxButton
        variant="primary"
        (click)="submit()"
      >
        {{ "confirm" | translate }}
      </button>
    </odx-modal-footer>
</div>
