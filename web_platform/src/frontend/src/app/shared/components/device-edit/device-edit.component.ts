import { Component, ViewChild, ElementRef } from '@angular/core';
import { ModalRef } from '@odx/angular/components/modal';
import { UtilService } from '@services/util.service';
import {
  FormGroup,
  FormControl,
  Validators,
} from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { forkJoin } from 'rxjs';
import { OdxToastCustomService } from '@services/odx-toast-custom.service';
import { selectOption } from '@services/context.service'
import { Device } from '../device-list/device-list.component'
import { IndexQuerySchema, SystemDevice, SystemTenantService, SystemTenantAdminComplex, SystemDeviceService } from 'src/app/api/backend';
import { SettingsService, User } from '@services/settings.service';
import moment from 'moment';
import { LocalStorageService } from '@services/local-storage.service';
import { DatepickerInputDateOrder, provideDatepickerConfig } from '@odx/angular/components/datepicker';

@Component({
  selector: 'app-device-edit',
  templateUrl: './device-edit.component.html',
  styleUrls: ['./device-edit.component.scss'],
  providers: [
    // provide custom date config
    provideDatepickerConfig({
      inputDateOrder: DatepickerInputDateOrder.MDY,
      inputDateSeparator: '/',
    }),
  ],
})
export class DeviceEditComponent {
  title: string = 'device.addDevice';
  loading: boolean = false;
  deviceType?: string;
  deviceIds?: number[];
  modalData: any;
  moldOptions: selectOption[] = [];
  deviceList: Device[] = [];
  deviceForm: FormGroup;
  currentDevice: Device | null = null;
  manufactureDateRequired: boolean = false;
  simExpiredDateRequired: boolean = false;  
  currentUser: User;
  tenantId?: number;
  deviceMoldId: number | String = 0;
  today = new Date();
  constructor(
    private modalRef:ModalRef<any, boolean>,
    private utilService: UtilService,
    private translateService: TranslateService,
    private msgSrv: OdxToastCustomService,
    private systemDeviceService: SystemDeviceService,
    private systemTenantService: SystemTenantService,
    private settingsService: SettingsService

  ) {
    this.currentUser = this.settingsService.User;
    this.tenantId = (this.currentUser.accountable as SystemTenantAdminComplex)?.tenant_id as number ?? 1;
    this.modalData = modalRef.data;
    this.deviceType = this.modalData?.deviceType;
    this.deviceIds = this.modalData?.deviceIds;
    let deviceMoldList: selectOption[] = LocalStorageService.getItem('deviceMolds')
    console.log(deviceMoldList)
    if (this.deviceType == 'tic') {
      this.manufactureDateRequired = true
      this.simExpiredDateRequired = true
      if (deviceMoldList) {
        deviceMoldList.forEach(mold => {
          if (mold.label == 'tic') {
           this.deviceMoldId = mold.value
          }
        });
      }
    } else if (this.deviceType == 'ttt') {
      if (deviceMoldList) {
        deviceMoldList.forEach(mold => {
          if (mold.label == 'smart_base_station') {
           this.deviceMoldId = mold.value
          }
        });
      }
    }
    this.deviceForm = new FormGroup({
      name: new FormControl(null, [ Validators.required]),
      manufacture_date: new FormControl('', this.manufactureDateRequired ? [Validators.required] : [ ]),
      last_members: new FormControl(null, []),
      sim_expired_date: new FormControl(null, this.simExpiredDateRequired ? [Validators.required] : []),
      remark: new FormControl(null, []),
      identifier: new FormControl(null, [Validators.required]),
      last_calibration_time: new FormControl(null, []),
      next_calibration_time: new FormControl(null, [])
    });
    if (this.deviceIds?.length) {
      this.title = 'device.edit';
      this.getDeviceList();
    }
  }
  public getDeviceList() {
    if (this.deviceIds?.length) {
      let filter = []
      filter.push(`id:in:${this.deviceIds.join(',')}`);
      const query: IndexQuerySchema = {
        page: 1,
        size: 99999,
        filter: filter,
        sort: ['id'],
        _with: ['deviceMold'],
      }
      this.systemTenantService.tenantControllerIndexDevice(this.tenantId ?? 1, false, undefined, false, true, query).subscribe(data => {
        this.loading = false;
        this.deviceList = data.data.map((item: any) => {
          return {
            ...item,
            selected: false
          }
        })
        console.log( this.deviceList)
        this.changeDevice(0)
      });
    }
  }
  public changeDevice(index: number) {
     this.deviceList.forEach(item => {
      item.selected = false
    })
    this.deviceList[index].selected = true
    this.currentDevice = this.deviceList[index];
    console.log( this.currentDevice)
    this.deviceForm.reset({
      name: this.deviceList[index].name,
      remark: this.deviceList[index].remark,
      identifier: this.deviceList[index].identifier,
      last_calibration_time: this.deviceList[index].last_calibration_time,
      next_calibration_time: this.deviceList[index].next_calibration_time,
      sim_expired_date: this.deviceList[index].sim_expired_date,
      last_members: this.deviceList[index].last_members,
      manufacture_date: this.deviceList[index].manufacture_date
    })
  }public submit(): void {
    const formValue = this.deviceForm.value;
    let request = [];
    this.deviceForm.markAllAsTouched()
    if (!this.deviceForm.valid) {
      return;
    }
    const device: SystemDevice = {
      name: formValue.name,
      identifier: formValue.identifier,
      remark: formValue.remark,
      last_members: formValue.last_members,
      manufacture_date: formValue.manufacture_date ? moment(formValue.manufacture_date).format('YYYY-MM-DD HH:mm:ss') : undefined,
      sim_expired_date: formValue.sim_expired_date ? moment(formValue.sim_expired_date).format('YYYY-MM-DD HH:mm:ss') : undefined,
      next_calibration_time: formValue.next_calibration_time ? moment(formValue.next_calibration_time).format('YYYY-MM-DD HH:mm:ss') : undefined
    };
    if (this.deviceIds?.length) {
      request.push(this.systemDeviceService.deviceControllerUpdate(this.currentDevice?.id ?? 0, device))
    } else {
      device.device_mold_id = Number(this.deviceMoldId)
      request.push(this.systemTenantService.tenantControllerStoreDevice(this.tenantId?? 1, device))
    }
    this.loading = true;
    // 保存成功提示
    forkJoin(request).subscribe(
      (_) => {
        this.loading = false;
        this.translateService.get('success.save').subscribe((msg) => {
          this.msgSrv.success(msg);
        });
      },
      (_) => {
        this.loading = false;
        this.translateService.get('failed.save').subscribe((failMsg) => {
          this.msgSrv.warning(failMsg);
        });
      }
    );
  }
  closeModalWithCancel () {
    this.modalRef.close(this.deviceIds?.length ? true : false);
  }
}
