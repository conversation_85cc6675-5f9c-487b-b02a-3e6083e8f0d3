@import "../../../../styles.scss";

.odx-modal__content
{
  padding-top: 0;
}

.device-edit-container {
  .content-item {
    display: flex;
    justify-content: space-between;
    .left-item {
      width: calc($marginBase * 180 / 24);
      .device-item {
        cursor: pointer;
        margin: calc($marginBase * 8 / 24);
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: calc($baseFont * 13 / 24);
        font-family: "DraegerPangeaText-Regular";
        padding: calc($marginBase * 5 / 24);
        border-radius: calc($marginBase * 4 / 24);
      }
      .selected-item {
        background: var(--odx-c-secondary);
        ::ng-deep .odx-icon {
          color: #F30303;
        }
      }
    }
    .right-item {
      width: calc(100% - $marginBase * 180 / 24);
      padding-left: calc($marginBase * 12 / 24);
      border-left: 1px solid #B2C1D2;
    }
    .full-width {
      width: 100%;
      border-left: none;
      padding-left: 0;
    }
    .title-item {
      font-size: calc($baseFont * 16 / 24);
      font-family: var(--semiBoldFont);
      line-height: calc($marginBase * 48/ 24);
    }
  }
}
