<div [odxLoadingSpinner]="initLoading" [ngClass]="{'video-content': true, 'no-right-item': rightHide}">
  <div class="left-item">
    <div (click)="selectDevice(device)" *ngFor="let device of devices"
      [ngClass]="{'video-item': true, 'selected-item': device.selected, 'alarm-item': device.status == 'alarm', 'offline-item': device.status == 'normal'}">
      <div class="name-info">
        <app-icon type="built-in" iconSet="safety" name="ucf-6000-9000"></app-icon>
        <div class="text-info">
          <p class="name">{{device.name}}</p>
          <p class="sn">{{device.sn}}</p>
        </div>
      </div>
      <span class="value-info">
        {{device.value}} {{device.unit}}
      </span>
    </div>
  </div>
  <div class="center-item">
    <p class="value-info">
      <span>{{activeDevice?.unitName}}</span><span class="value-item">{{activeDevice?.value}}
        {{activeDevice?.unit}}</span>
    </p>
    <div *ngIf="url" class="video">
      <video #videoElement controls muted autoplay playsinline width="100%" height="400px" [style.background-color]="'#000'">
        Your browser does not support the video tag.
      </video>
    </div>
    <div *ngIf="!url" class="no-video">
      <div *ngIf="activeDevice">
        <p class="loading-item" [odxLoadingSpinner]="activeDevice.loading" [odxLoadingSpinnerMinHeight]="minHeight"></p>
        <p [ngClass]="{'desc-item': true, 'failed-flag': activeDevice.failedFlag}" class="desc-item">{{activeDevice.desc
          }}</p>
        <button *ngIf="!activeDevice.loading" (click)="requestLiveFeed(activeDevice)" odxButton>Request</button>
      </div>
    </div>
    <span class="exit-icon">
      <app-icon type="built-in" iconSet="safety" name="evacuation"></app-icon>
    </span>
  </div>
  <div *ngIf="!rightHide" class="right-item">
    <div class="right-header">
      <app-icon class="member-icon" type="svg" name="member"></app-icon>
      {{activeDevice?.member}}
      <odx-chip size="small">
        {{activeDevice?.team}}
      </odx-chip>
    </div>
    <div class="right-content">
      <p class="value-item">
        <span> {{activeDevice?.unitName}}</span>
        <span> {{activeDevice?.value}}{{activeDevice?.unit}}</span>
      </p>
      <div class="img-list">
        <div class="img-item">
          <img src="./assets/img/building_floor_no_page.svg" />
          13:44:32
        </div>

        <div class="img-item">
          <img src="./assets/img/building_floor_no_page.svg" />
          13:44:32
        </div>

        <div class="img-item">
          <img src="./assets/img/building_floor_no_page.svg" />
          13:44:32
        </div>
        <div class="img-item">
          <img src="./assets/img/building_floor_no_page.svg" />
          13:44:32
        </div>
        <div class="img-item">
          <img src="./assets/img/building_floor_no_page.svg" />
          13:44:32
        </div>
        <div class="img-item">
          <img src="./assets/img/building_floor_no_page.svg" />
          13:44:32
        </div>
      </div>
      <div class="device-info-item" odxLayout="flex vertical-center">
        <span class="item-label">
          {{ 'device.name' | translate}}
        </span>
        <span class="item-value">
          {{ activeDevice?.name }}
        </span>
      </div>
      <div class="device-info-item" odxLayout="flex vertical-center">
        <span class="item-label">
          {{ 'device.sn' | translate}}
        </span>
        <span class="item-value">
          {{ activeDevice?.sn }}
        </span>
      </div>
      <div class="device-info-item" odxLayout="flex vertical-center">
        <span class="item-label">
          {{ 'device.lastUsageTime' | translate}}
        </span>
        <span class="item-value">
          {{ activeDevice?.lastUsageTime }}
        </span>
      </div>
      <div class="device-info-item" odxLayout="flex vertical-center">
        <span class="item-label">
          {{ 'device.lastMember' | translate}}
        </span>
        <span class="item-value">
          {{ activeDevice?.member }}
        </span>
      </div>
      <div class="device-info-item" odxLayout="flex vertical-center">
        <span class="item-label">
          {{ 'device.team' | translate}}
        </span>
        <span class="item-value">
          {{ activeDevice?.team }}
        </span>
      </div>
      <div class="device-info-item" odxLayout="flex vertical-center">
        <span class="item-label">
          {{ 'device.remarks' | translate}}
        </span>
        <span class="item-value">
          {{ activeDevice?.remarks }}
        </span>
      </div>
    </div>
  </div>
</div>