@import "../../../../styles.scss";


.video-content {
  display: flex;
  align-items: center;
  height: calc(100vh - $marginBase * 48 / 24);
  cursor: pointer;

  .left-item {
    width: calc($marginBase * 300 / 24);
    height: 100%;
    overflow-x: scroll;

    .video-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: calc($marginBase * 12 / 24);
      border-bottom: 1px solid rgba(0, 39, 102, 0.12);
      font-family: "DraegerPangea-Medium";

      .name-info {
        display: flex;
        align-items: center;
        font-size: calc($baseFont * 16 / 24);
        color: rgba(0, 39, 102, 1);
        min-width: 70%;

        .sn {
          font-size: calc($baseFont * 12 / 24);
          font-weight: 400;
          font-family: "DraegerPangeaText-Regular";
        }

        ::ng-deep {
          .odx-icon {
            font-size: calc($baseFont * 36 / 24);
          }
        }
      }

      .value-info {
        font-size: calc($baseFont * 14 / 24);
      }
    }

    .selected-item {
      background: #E9EEF4;
    }
    .alarm-item {
      background: #FFE300;
    }
    .offline-item {
      background: #D9D9D9;
      opacity: 0.65;
    }
  }

  .center-item {
    height: 100%;
    background: #000;
    text-align: center;
    width: 100%;
    position: relative;

    .value-info {
      font-size: calc($baseFont * 16 / 24);
      color: #fff;
      margin-top: calc($marginBase * 20 / 24);
      margin-bottom: calc($marginBase * 10 / 24);
      display: flex;
      align-items: center;
      justify-content: center;

      .value-item {
        font-weight: 600;
        font-size: calc($baseFont * 28 / 24);
        display: inline-block;
        padding-left: calc($marginBase * 4 / 24);
      }
    }

    .exit-icon {
      position: absolute;
      top: 50%;
      right: calc($marginBase * 16 / 24);
      transform: translateY(-50%);
      cursor: pointer;
      padding: calc($marginBase * 8 / 24);

      ::ng-deep .odx-icon {
        font-size: calc($baseFont * 30 / 24);
        color: #fff;
      }

      background: rgba(0, 145, 247, 1);
      border-radius: calc($baseFont * 23 / 24);
    }

    .no-video {
      background: rgba(158, 176, 194, 1);
      height: calc(100% - $marginBase * 100 / 24);
      display: flex;
      align-items: center;
      justify-content: center;
      color: #fff;

      .desc-item {
        margin-bottom: calc($marginBase * 14 / 24);
      }

      .failed-flag {
        color: rgba(188, 0, 0, 1);
      }

      .loading-item {
        height: calc($baseFont * 60 / 24);
        margin-bottom: calc($marginBase * 14 / 24);
      }

      ::ng-deep {
        .odx-loading-spinner .odx-circular-progress {
          width: calc($baseFont * 50 / 24);
          height: calc($baseFont * 50 / 24);
        }

        .odx-loading-spinner .odx-circular-progress__indicator {
          stroke: rgba(213, 226, 246, 1);
        }

        .odx-loading-spinner .odx-circular-progress__track {
          stroke: rgba(213, 226, 246, 0.3)
        }
      }
    }
  }

  .right-item {
    width: calc($marginBase * 332 / 24);
    height: 100%;

    .right-header {
      border-bottom: 1px solid rgba(0, 39, 102, 0.12);
      display: flex;
      align-items: center;
      font-size: calc($baseFont * 20 / 24);
      font-family: var(--semiBoldFont);
      font-weight: 600;
      padding: calc($marginBase * 22 /24) calc($marginBase * 16 /24);

      .member-icon {
        margin-right: calc($marginBase * 8 / 24);
      }

      ::ng-deep .odx-icon {
        font-size: calc($baseFont * 28 / 24) !important;
      }

      ::ng-deep .odx-chip {
        font-size: calc($baseFont * 12 / 24);
        font-family: var(--mediumFont);
        font-weight: 500;
        margin-left: calc($marginBase * 8 / 24);
      }
    }

    .right-content {
      padding: calc($marginBase * 12 / 24);

      .value-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        border: calc($baseFont * 1 / 24) solid #E0E5ED;
        border-radius: calc($baseFont * 5 / 24);
        font-size: calc($baseFont * 20 / 24);
        font-family: var(--semiBoldFont);
        font-weight: 600;
        padding: calc($marginBase * 14 /24) calc($marginBase * 12 /24);
      }

      .img-list {
        .img-item {
          width: 33.3%;
          display: inline-block;
          text-align: center;
          padding: calc($marginBase * 12 / 24) 0;
          border-bottom: 1px solid #DDE5EE;
          font-family: var(--odx-typography-font-family);
          font-size: calc($baseFont * 12 / 24);
          font-weight: 400;

          img {
            width: 100%;
          }
        }
      }

      .device-info-item {
        justify-content: space-between;
        border-bottom: calc($baseFont * 1 / 24) solid #E0E5ED;
        font-family: var(--odx-typography-font-family);
        font-size: calc($baseFont * 13 / 24);
        padding: calc($marginBase * 12 / 24);
      }
    }
  }
}

.no-right-item {
  margin: calc($marginBase * 20 / 24);
  height: calc($marginBase * 520 / 24);
  border-radius: calc($marginBase * 6 / 24);
  border: 1px solid rgba(0, 39, 102, 0.12);
}