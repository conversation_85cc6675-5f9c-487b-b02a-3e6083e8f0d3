import { Component, Input, ChangeDetectorRef, OnInit, OnDestroy, ViewChild, ElementRef, Output, EventEmitter, OnChanges, SimpleChanges } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import flvjs from 'flv.js';
import Hls from 'hls.js';
import {
  SystemDevice,
  SystemDeviceService,
  SystemMemberDataService,
  SystemTenantAdminComplex
} from 'src/app/api/backend';
import { SettingsService, User } from '@services/settings.service';

import moment from 'moment';

@Component({
  selector: 'app-video-list',
  templateUrl: './video-list.component.html',
  styleUrls: ['./video-list.component.scss']
})

export class VideoListComponent implements OnInit, OnDestroy {
  @ViewChild('videoElement') videoElement!: ElementRef<HTMLVideoElement>;

  private destroy$ = new Subject<void>();
  private flvPlayer: flvjs.Player | null = null;
  private hlsPlayer: Hls | null = null;
  private currentPlayerType: 'flv' | 'hls' | null = null;
  private mediaSourceRetryCount: number = 0;
  public chartIndent = 15;
  public devices: DeviceData[] = [];
  public activeDevice?: DeviceData;
  public url?: string;
  public initLoading: boolean = false;
  public failedFlag: boolean = false;
  public desc: string = '';
  public tenantId: number;
  currentUser: User;

  @Input() rightHide?: boolean = false;
  @Input() sn?: string;
  @Output() selectDeviceChanged: EventEmitter<SelectDevice> = new EventEmitter();

  constructor(
    private translateService: TranslateService,
    private cdRef: ChangeDetectorRef,
    private systemMemberDataService: SystemMemberDataService,
    private settingsService: SettingsService,
    private systemDeviceService: SystemDeviceService

  ) {

    this.currentUser = this.settingsService.User;

    this.tenantId = (this.currentUser.accountable as SystemTenantAdminComplex)?.tenant_id as number ?? 1;

    // 移除构造函数中的 loadDevices 调用，改为在 ngOnInit 中调用
  }

  ngOnInit(): void {
    this.loadDevices();
    this.setupPageVisibilityHandling();
  }

  ngOnDestroy(): void {
    this.destroyPlayers();
    this.removePageVisibilityHandling();
    this.destroy$.next();
    this.destroy$.complete();
  }

  private loadDevices(): void {
    // 防止重复加载
    if (this.initLoading) {
      return;
    }

    // 加载设备数据
    console.log('init tic devices')
    this.initLoading = true;
    this.devices = []; // 清空现有数据，防止重复添加
    const endTime = moment().format('YYYY-MM-DD HH:mm:ss');
    const startTime = moment()
      .subtract(this.chartIndent, 'minutes')
      .format('YYYY-MM-DD HH:mm:ss');
    this.systemMemberDataService.memberDataControllerGetAllMembersTelemetryData(this.tenantId, startTime, endTime)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res) => {
          console.log('API 响应:', res);

          // 处理设备数据
          res.data.members?.forEach(member => {
            if (member.device_data?.tic) {
              let lastData = member.device_data?.tic.length ? member.device_data?.tic[member.device_data?.tic.length - 1] : null
              let status = 'normal'
              if (lastData) {
                let telemetry = lastData.telemetry?.length ? lastData.telemetry[lastData.telemetry?.length - 1] : null
                this.devices.push({
                  id: member.member_info?.id ?? '',
                  name: lastData.device_identifier ?? '',
                  sn: lastData.device_identifier,
                  unitName: 'Temp',
                  value: telemetry ? (telemetry as any).value : '',
                  unit: telemetry ? (telemetry as any).unit : '',
                  selected: false,
                  status: telemetry ? ((telemetry as any).value > 1 ? 'alarm' : 'normal'): 'normal'
                })
              }
            }
          });
          console.log(this.sn)

          // 设置设备描述和选中状态（移到 forEach 外部，只执行一次）

          this.devices.forEach((item, index) => {
            item.desc = this.translateService.instant("common.request") + ' ' + item.name + ' ' + this.translateService.instant("common.liveFeed");
            item.failedFlag = false;
            item.loading = false;
            item.selected = this.sn ? (this.sn == item.sn  ? true : false ): index === 0; // 第一个设备默认选中
          });
          // 设置活动设备
          this.activeDevice = this.devices.find(device => device.selected);
          console.log('加载的设备:', this.devices);

          // 设置加载完成状态并触发变更检测
          this.initLoading = false;
          this.getDeviceDetail();
          this.cdRef.detectChanges();
          console.log('initLoading 设置为:', this.initLoading);
        },
        error: (error) => {
          console.error('❌ API 请求失败:', error);
          this.initLoading = false;
          this.cdRef.detectChanges();
        }
      });
  }

  selectDevice(device: DeviceData) {
    // 停止当前播放器
    this.destroyPlayers();
    this.url = '';
    this.failedFlag = false;
    this.mediaSourceRetryCount = 0; // 重置重试计数器

    this.devices.forEach(item => {
      if (item.id == device.id) {
        item.selected = true;
        this.activeDevice = item;
      } else {
        item.selected = false;
      }
    });
    this.getDeviceDetail()
  }

  getDeviceDetail () {
    if (this.activeDevice) {
      this.activeDevice.failedFlag = false;
      this.activeDevice.loading = true;
      this.activeDevice.desc = this.activeDevice?.name + ' ' + this.translateService.instant("common.requestInProgress");
      this.systemDeviceService.deviceControllerShowByIdentifier(this.activeDevice.sn ?? '', [], true)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res) => {
          const current = this.activeDevice;
          const that = this
          if (!current) return;
          current.loading = false;
          this.url = res.data.streaming_url ?? '';
          // 首次拿到地址时发射
          that.selectDeviceChanged.emit({ sn: current.sn, url:  res.data.streaming_url});

          if (this.url) {
            current.desc = this.translateService.instant("common.streamReady");
            this.cdRef.detectChanges();
            requestAnimationFrame(() => {
              this.initPlayer(this.url ?? '');
            });
          } else {
            current.failedFlag = false;
            current.desc = this.translateService.instant("common.request") + ' ' + current.name + ' ' + this.translateService.instant("common.liveFeed");
            this.activeDevice = current
            this.cdRef.detectChanges();
          }
        }
      })
    }
  }

  requestLiveFeed(device: DeviceData) {
    device.failedFlag = false;
    device.loading = true;
    device.desc = this.activeDevice?.name + ' ' + this.translateService.instant("common.requestInProgress");

    this.devices.forEach(item => {
      if (item.id == device?.id) {
        item = device;
      }
    });

    // 模拟获取视频流 URL - 这里您需要替换为实际的 API 调用
    setTimeout(() => {
      device.loading = false;

      // 模拟成功获取视频流 URL
      // 在实际应用中，这里应该是从 API 获取的真实流地址
      const mockStreamUrl = 'http://d.osvlabs.com:30890/live/livestream.flv'; // 替换为实际的流地址

      if (mockStreamUrl) {
        this.url = mockStreamUrl;
        device.failedFlag = false;
        device.desc = this.translateService.instant("common.streamReady");

        // 延迟初始化播放器，确保 DOM 已更新
        setTimeout(() => {
          this.initPlayer(mockStreamUrl);
        }, 100);
      } else {
        device.failedFlag = true;
        device.desc = this.translateService.instant("common.requestFailed");
      }

      this.devices.forEach(item => {
        if (item.id == this.activeDevice?.id) {
          item = device;
        }
      });

      this.cdRef.markForCheck();
    }, 2000); // 减少等待时间到2秒
  }

  private setupPageVisibilityHandling(): void {
    // 处理页面可见性变化，确保直播在后台继续播放
    document.addEventListener('visibilitychange', this.handleVisibilityChange.bind(this));

    // 处理窗口焦点变化
    window.addEventListener('focus', this.handleWindowFocus.bind(this));
    window.addEventListener('blur', this.handleWindowBlur.bind(this));

    console.log('Page visibility handling enabled for live streaming');
  }

  private removePageVisibilityHandling(): void {
    document.removeEventListener('visibilitychange', this.handleVisibilityChange.bind(this));
    window.removeEventListener('focus', this.handleWindowFocus.bind(this));
    window.removeEventListener('blur', this.handleWindowBlur.bind(this));
  }

  private handleVisibilityChange(): void {
    const video = this.videoElement?.nativeElement;
    if (!video) return;

    if (document.hidden) {
      console.log('Page became hidden - maintaining live stream');

      // 记录播放状态
      const wasPlaying = !video.paused;

      // 尝试保持播放状态，即使页面隐藏
      if (wasPlaying) {
        // 使用 requestAnimationFrame 来确保视频继续播放
        this.maintainBackgroundPlayback();
      }
    } else {
      console.log('Page became visible - ensuring live stream continues');
      this.ensurePlaybackContinues();
    }
  }

  private handleWindowFocus(): void {
    console.log('Window focused - ensuring live stream is active');
    this.ensurePlaybackContinues();
  }

  private handleWindowBlur(): void {
    console.log('Window blurred - maintaining live stream in background');
    this.maintainBackgroundPlayback();
  }

  private maintainBackgroundPlayback(): void {
    const video = this.videoElement?.nativeElement;
    if (!video) return;

    // 强制保持播放状态
    if (video.paused) {
      video.play().catch(error => {
        console.log('Background play attempt:', error);
      });
    }

    // 设置定时器定期检查播放状态
    const maintainPlayback = () => {
      if (video.paused && this.currentPlayerType) {
        video.play().catch(() => {});
      }

      if (document.hidden && this.currentPlayerType) {
        setTimeout(maintainPlayback, 1000);
      }
    };

    setTimeout(maintainPlayback, 1000);
  }

  private ensurePlaybackContinues(): void {
    const video = this.videoElement?.nativeElement;
    if (!video) return;

    setTimeout(() => {
      if (video.paused && this.currentPlayerType) {
        console.log('Resuming playback after page visibility change');

        video.play().then(() => {
          console.log('Live stream resumed successfully');
          if (this.activeDevice) {
            this.activeDevice.desc = '正在播放直播流';
            this.cdRef.markForCheck();
          }
        }).catch(error => {
          console.error('Failed to resume playback:', error);

          // 如果恢复失败，尝试重新初始化播放器
          if (this.url) {
            console.log('Reinitializing player after resume failure');
            this.initPlayer(this.url);
          }
        });
      }

      // 如果延迟太高，跳转到直播边缘
      if (video.duration && video.currentTime < video.duration - 5) {
        console.log('Seeking to live edge after page return');
        video.currentTime = video.duration - 0.5;
      }
    }, 100);
  }

  private detectStreamType(url: string): 'flv' | 'hls' {
    if (url.includes('.m3u8') || url.includes('/hls/')) {
      return 'hls';
    }
    return 'flv'; // 默认使用FLV
  }

  private initPlayer(url: string): void {
    console.log('Initializing player for URL:', url);

    // 优先使用HLS - 这是用户的明确要求
    if (Hls.isSupported()) {
      console.log('HLS is supported, prioritizing HLS playback');

      // 尝试将FLV URL转换为HLS URL
      let hlsUrl = this.convertToHlsUrl(url);
      console.log('Converted HLS URL:', hlsUrl);

      // 如果转换失败，尝试直接使用原URL（可能本身就是HLS）
      if (!hlsUrl) {
        hlsUrl = url;
        console.log('Using original URL as HLS URL:', hlsUrl);
      }

      this.initHlsPlayer(hlsUrl, () => {
        console.log('Primary HLS attempt failed, trying alternative HLS URLs');
        this.tryAlternativeHlsUrls(url, () => {
          console.log('All HLS attempts failed, falling back to FLV as last resort');
          this.initFlvPlayer(url);
        });
      });
    } else {
      console.log('HLS not supported in this browser, using FLV');
      this.initFlvPlayer(url);
    }
  }

  private convertToHlsUrl(flvUrl: string): string | null {
    console.log('Converting FLV URL to HLS:', flvUrl);

    if (flvUrl.includes('.flv')) {
      try {
        // 使用URL对象正确解析，避免查询参数问题
        const url = new URL(flvUrl);

        // 策略1: 替换路径中的/live/为/hls/并替换扩展名
        if (url.pathname.includes('/live/')) {
          const hlsPath = url.pathname.replace('/live/', '/hls/').replace('.flv', '.m3u8');
          const hlsUrl = `${url.protocol}//${url.host}${hlsPath}${url.search}`;
          console.log('Primary HLS URL (live->hls):', hlsUrl);
          return hlsUrl;
        }

        // 策略2: 只替换扩展名
        const simplePath = url.pathname.replace('.flv', '.m3u8');
        const simpleUrl = `${url.protocol}//${url.host}${simplePath}${url.search}`;
        console.log('Simple HLS URL (extension only):', simpleUrl);
        return simpleUrl;
      } catch (error) {
        console.error('Failed to parse FLV URL:', error);
        // 回退到简单字符串替换（可能会有查询参数问题）
        console.log('Using fallback string replacement');
        return flvUrl.replace('.flv', '.m3u8');
      }
    }

    // 如果URL已经是HLS格式，直接返回
    if (flvUrl.includes('.m3u8') || flvUrl.includes('/hls/')) {
      console.log('URL is already HLS format:', flvUrl);
      return flvUrl;
    }

    console.log('Unable to convert URL to HLS format');
    return null;
  }

  private tryAlternativeHlsUrls(originalUrl: string, onAllFailed: () => void): void {
    console.log('Trying alternative HLS URL patterns for:', originalUrl);

    const alternatives: string[] = [];

    if (originalUrl.includes('.flv')) {
      try {
        // 正确解析URL，分离路径和查询参数
        const url = new URL(originalUrl);
        const pathParts = url.pathname.split('/');
        const filename = pathParts[pathParts.length - 1];
        const queryString = url.search;

        if (filename.includes('.flv')) {
          const baseName = filename.replace('.flv', '');
          const basePath = pathParts.slice(0, -1).join('/');

          // 备选方案1: 只替换扩展名
          alternatives.push(`${url.protocol}//${url.host}${url.pathname.replace('.flv', '.m3u8')}${queryString}`);

          // 备选方案2: 替换路径中的 /live/ 为 /hls/
          if (url.pathname.includes('/live/')) {
            const hlsPath = url.pathname.replace('/live/', '/hls/').replace('.flv', '.m3u8');
            alternatives.push(`${url.protocol}//${url.host}${hlsPath}${queryString}`);
          }

          // 备选方案3: 在当前路径下添加hls子目录
          alternatives.push(`${url.protocol}//${url.host}${basePath}/hls/${baseName}.m3u8${queryString}`);

          // 备选方案4: 尝试live-hls路径格式
          if (url.pathname.includes('/live/')) {
            const liveHlsPath = url.pathname.replace('/live/', '/live-hls/').replace('.flv', '');
            alternatives.push(`${url.protocol}//${url.host}${liveHlsPath}${queryString}.m3u8`);
          }

          // 备选方案5: 简化的HLS路径
          alternatives.push(`${url.protocol}//${url.host}/hls/${baseName}${queryString}.m3u8`);

          console.log('Generated HLS alternatives:', alternatives);
        }
      } catch (error) {
        console.error('Failed to parse URL for HLS alternatives:', error);
        // 回退到简单替换
        alternatives.push(originalUrl.replace('.flv', '.m3u8'));
      }
    }

    if (alternatives.length === 0) {
      console.log('No valid HLS alternatives could be generated');
      onAllFailed();
      return;
    }

    this.tryHlsUrlSequentially(alternatives, 0, onAllFailed);
  }

  private tryHlsUrlSequentially(urls: string[], index: number, onAllFailed: () => void): void {
    if (index >= urls.length) {
      console.log('All HLS URL alternatives exhausted');
      onAllFailed();
      return;
    }

    const currentUrl = urls[index];

    // 验证URL有效性，防止生成错误的URL
    try {
      const urlObj = new URL(currentUrl);
      if (!urlObj.pathname || urlObj.pathname === '/' || urlObj.pathname.includes('undefined')) {
        console.warn(`Invalid HLS URL detected: ${currentUrl}, skipping`);
        this.tryHlsUrlSequentially(urls, index + 1, onAllFailed);
        return;
      }
    } catch (error) {
      console.warn(`Malformed URL: ${currentUrl}, skipping`);
      this.tryHlsUrlSequentially(urls, index + 1, onAllFailed);
      return;
    }

    console.log(`Trying HLS URL alternative ${index + 1}:`, currentUrl);

    this.initHlsPlayer(currentUrl, () => {
      console.log(`HLS URL alternative ${index + 1} failed, trying next`);
      // 添加延迟避免过快重试
      setTimeout(() => {
        this.tryHlsUrlSequentially(urls, index + 1, onAllFailed);
      }, 500);
    });
  }

  private initHlsPlayer(url: string, onError?: () => void): void {
    console.log('Initializing HLS player with URL:', url);

    const video = this.videoElement?.nativeElement;
    if (!video) {
      console.error('Video element not found');
      onError?.();
      return;
    }

    // 暂时跳过原生HLS，直接使用HLS.js确保兼容性
    // if (video.canPlayType('application/vnd.apple.mpegurl')) {
    //   console.log('Using native HLS support');
    //   this.initNativeHlsPlayer(url, onError);
    //   return;
    // }

    if (!Hls.isSupported()) {
      console.error('HLS.js is not supported in this browser');
      onError?.();
      return;
    }

    try {
      this.destroyPlayers();

      // 恢复基本的HLS配置，确保能正常播放
      this.hlsPlayer = new Hls({
        debug: false,
        enableWorker: true,
        lowLatencyMode: true,

        // 极激进的实时直播配置
        liveSyncDurationCount: 1, // 最少同步片段
        liveMaxLatencyDurationCount: 2, // 最大延迟片段
        maxBufferLength: 3, // 极小缓冲区3秒
        maxMaxBufferLength: 6, // 极小最大缓冲区6秒
        backBufferLength: 5, // 极小后向缓冲区5秒

        // 添加基本加载配置
        manifestLoadingTimeOut: 10000,
        fragLoadingTimeOut: 20000,
      });

      this.currentPlayerType = 'hls';

      // 恢复必要的video元素设置
      video.preload = 'none';
      video.setAttribute('playsinline', 'true');
      video.setAttribute('controls', 'true');
      video.muted = true;

      this.hlsPlayer.loadSource(url);
      this.hlsPlayer.attachMedia(video);

      this.hlsPlayer.on(Hls.Events.MANIFEST_PARSED, () => {
        console.log('HLS.js manifest parsed, starting playback');

        // 确保视频能够播放
        setTimeout(() => {
          video.play().then(() => {
            console.log('HLS.js playback started successfully');
            if (this.activeDevice) {
              this.activeDevice.desc = '正在播放HLS直播流';
              this.cdRef.markForCheck();
            }
          }).catch(error => {
            console.error('HLS.js play failed:', error);
            // 如果播放失败，可能是autoplay限制，显示提示
            if (this.activeDevice) {
              this.activeDevice.desc = '点击播放按钮开始播放';
              this.cdRef.markForCheck();
            }
          });
        }, 100);
      });

      // 必要的事件监听
      this.hlsPlayer.on(Hls.Events.ERROR, (event, data) => {
        console.error('HLS.js Error:', event, data);
        if (data.fatal) {
          switch (data.type) {
            case Hls.ErrorTypes.NETWORK_ERROR:
              console.error('Network error - trying fallback');
              onError?.();
              break;
            case Hls.ErrorTypes.MEDIA_ERROR:
              console.error('Media error - attempting recovery');
              try {
                this.hlsPlayer?.recoverMediaError();
              } catch (error) {
                console.error('Recovery failed:', error);
                onError?.();
              }
              break;
            default:
              console.error('Fatal HLS.js error');
              onError?.();
              break;
          }
        }
      });

      // 添加完整的video事件监听
      video.addEventListener('loadstart', () => {
        console.log('Video loadstart');
      });

      video.addEventListener('loadedmetadata', () => {
        console.log('Video metadata loaded');
      });

      video.addEventListener('canplay', () => {
        console.log('Video can play');
      });

      let bufferingStartTime: number = 0;

      video.addEventListener('waiting', () => {
        bufferingStartTime = Date.now();
        console.log('Video buffering started...');

        // 立即检查延迟并跳转（不等待播放恢复）
        if (video.duration && isFinite(video.duration) && video.duration > 0) {
          const liveDelay = video.duration - video.currentTime;
          if (liveDelay > 2) { // 降低阈值到2秒
            const seekTime = video.duration - 0.3;
            if (isFinite(seekTime) && seekTime > 0) {
              console.log(`Immediate seeking during buffering - delay: ${liveDelay.toFixed(2)}s`);
              video.currentTime = seekTime; // 更贴近直播边缘
            }
          }
        }

        if (this.activeDevice) {
          this.activeDevice.desc = '缓冲中...';
          this.cdRef.markForCheck();
        }
      });

      video.addEventListener('playing', () => {
        console.log('Video playing - checking for delay compensation');

        // 缓冲恢复后，检查是否需要跳转到直播边缘
        if (bufferingStartTime > 0) {
          const bufferingDuration = Date.now() - bufferingStartTime;
          console.log(`Buffering lasted ${bufferingDuration}ms - checking live position`);

          setTimeout(() => {
            this.seekToLiveEdgeIfNeeded(video, bufferingDuration);
          }, 200); // 减少等待时间到200ms

          bufferingStartTime = 0;
        }

        // 每次播放时都检查延迟（防止累积）
        setTimeout(() => {
          if (video.duration && isFinite(video.duration) && video.duration > 0 && !video.paused) {
            const liveDelay = video.duration - video.currentTime;
            if (liveDelay > 2) {
              const seekTime = video.duration - 0.3;
              if (isFinite(seekTime) && seekTime > 0) {
                console.log(`Playing event delay check: ${liveDelay.toFixed(2)}s - seeking to live`);
                video.currentTime = seekTime;
              }
            }
          }
        }, 500);

        if (this.activeDevice) {
          this.activeDevice.desc = '正在播放HLS直播流';
          this.cdRef.markForCheck();
        }
      });

      video.addEventListener('error', (e) => {
        console.error('Video error:', e, video.error);
        if (this.activeDevice) {
          this.activeDevice.failedFlag = true;
          this.activeDevice.desc = '视频播放出错';
          this.cdRef.markForCheck();
        }
      });

      // 添加定期延迟检查
      this.setupLiveLatencyMonitoring(video);

      console.log('HLS.js Player initialized with live latency management');
    } catch (error) {
      console.error('Failed to initialize HLS.js player:', error);
      onError?.();
    }
  }

  private seekToLiveEdgeIfNeeded(video: HTMLVideoElement, bufferingDuration: number): void {
    if (!video.duration || !isFinite(video.duration) || video.duration <= 0) {
      console.log('Cannot seek - duration not available or invalid');
      return;
    }

    const currentTime = video.currentTime;
    const duration = video.duration;
    const liveDelay = duration - currentTime;

    console.log(`Live delay check: ${liveDelay.toFixed(2)}s behind live edge`);

    // 极激进的延迟检测：超过2秒延迟或缓冲超过1.5秒就跳转
    if (liveDelay > 2 || bufferingDuration > 1500) {
      const seekTime = duration - 0.5; // 跳到离直播边缘0.5秒的位置，极接近实时

      if (isFinite(seekTime) && seekTime > 0 && seekTime <= duration) {
        console.log(`Ultra-aggressive seeking to live edge: ${currentTime.toFixed(2)}s → ${seekTime.toFixed(2)}s (delay: ${liveDelay.toFixed(2)}s)`);
        video.currentTime = seekTime;
      } else {
        console.log('Invalid seek time calculated, skipping seek');
        return;
      }

      if (this.activeDevice) {
        this.activeDevice.desc = '已跳转到最新画面';
        this.cdRef.markForCheck();

        // 2秒后恢复正常显示
        setTimeout(() => {
          if (this.activeDevice) {
            this.activeDevice.desc = '正在播放HLS直播流';
            this.cdRef.markForCheck();
          }
        }, 2000);
      }
    }
  }

  private setupLiveLatencyMonitoring(video: HTMLVideoElement): void {
    // 每2秒检查一次直播延迟 - 更频繁监控
    const monitorLatency = () => {
      if (video.duration && isFinite(video.duration) && video.duration > 0 && !video.paused) {
        const liveDelay = video.duration - video.currentTime;

        // 超激进：如果延迟超过3秒就自动跳转
        if (liveDelay > 3) {
          const seekTime = video.duration - 0.3;
          if (isFinite(seekTime) && seekTime > 0 && seekTime <= video.duration) {
            console.log(`Latency detected: ${liveDelay.toFixed(2)}s - auto-seeking to live`);
            video.currentTime = seekTime; // 跳到极接近直播边缘
          } else {
            console.log('Invalid seek time in latency monitoring, skipping seek');
            return;
          }

          if (this.activeDevice) {
            this.activeDevice.desc = '自动同步到最新画面';
            this.cdRef.markForCheck();

            setTimeout(() => {
              if (this.activeDevice) {
                this.activeDevice.desc = '正在播放HLS直播流';
                this.cdRef.markForCheck();
              }
            }, 2000);
          }
        }
      }

      // 继续监控（只要播放器还存在）
      if (this.currentPlayerType === 'hls') {
        setTimeout(monitorLatency, 2000); // 每2秒检查
      }
    };

    // 1秒后就开始监控（快速响应）
    setTimeout(monitorLatency, 1000);
  }

  private initNativeHlsPlayer(url: string, onError?: () => void): void {
    console.log('Initializing Native HLS player with URL:', url);

    const video = this.videoElement?.nativeElement;
    if (!video) {
      onError?.();
      return;
    }

    try {
      this.destroyPlayers();
      this.currentPlayerType = 'hls';

      // 设置video元素
      video.preload = 'metadata';
      video.setAttribute('playsinline', 'true');
      video.muted = true;
      video.src = url;

      // 简单的事件监听
      video.addEventListener('loadedmetadata', () => {
        console.log('Native HLS metadata loaded');
        video.play().then(() => {
          console.log('Native HLS playback started successfully');
          if (this.activeDevice) {
            this.activeDevice.desc = '正在播放原生HLS直播流';
            this.cdRef.markForCheck();
          }
        }).catch(error => {
          console.error('Native HLS play failed:', error);
        });
      });

      video.addEventListener('error', () => {
        console.error('Native HLS error');
        onError?.();
      });

      console.log('Native HLS Player initialized');
    } catch (error) {
      console.error('Failed to initialize Native HLS player:', error);
      onError?.();
    }
  }

  private initFlvPlayer(url: string): void {
    console.log('Initializing FLV player with URL:', url);

    // 检查浏览器是否支持 flv.js
    if (!flvjs.isSupported()) {
      console.error('FLV.js is not supported in this browser');
      if (this.activeDevice) {
        this.activeDevice.failedFlag = true;
        this.activeDevice.desc = 'Browser does not support FLV playback';
        this.cdRef.markForCheck();
      }
      return;
    }

    // 检查编解码器支持
    const videoElement = this.videoElement.nativeElement;
    const h264Support = videoElement.canPlayType('video/mp4; codecs="avc1.4d001f"');
    const flvSupport = videoElement.canPlayType('video/x-flv');
    console.log('Codec support check:', {
      'H.264 (avc1.4d001f)': h264Support,
      'FLV': flvSupport,
      'MediaSource': window.MediaSource ? 'supported' : 'not supported'
    });

    // 检查 video 元素是否存在
    if (!this.videoElement || !this.videoElement.nativeElement) {
      console.error('Video element not found');
      if (this.activeDevice) {
        this.activeDevice.failedFlag = true;
        this.activeDevice.desc = 'Video element not ready';
        this.cdRef.markForCheck();
      }
      return;
    }

    try {
      // 销毁现有播放器
      this.destroyPlayers();

      this.currentPlayerType = 'flv';

      // 创建新的 FLV 播放器 - 针对直播流优化配置
      this.flvPlayer = flvjs.createPlayer({
        type: 'flv',
        url: url,
        isLive: true,
        cors: true,
        withCredentials: false,
        hasAudio: true,
        hasVideo: true
      }, {
        enableWorker: false,
        enableStashBuffer: false,
        stashInitialSize: 128,
        isLive: true,
        lazyLoad: false, // 改为false，立即加载
        autoCleanupSourceBuffer: true,
        autoCleanupMaxBackwardDuration: 3,
        autoCleanupMinBackwardDuration: 2,
        fixAudioTimestampGap: true,
        accurateSeek: false,
        seekType: 'range',
        seekParamStart: 'bstart',
        seekParamEnd: 'bend',
        rangeLoadZeroStart: false,
        lazyLoadMaxDuration: 3 * 60,
        lazyLoadRecoverDuration: 30,
        deferLoadAfterSourceOpen: false, // 立即加载
        reuseRedirectedURL: true
      });

      // 绑定到 video 元素
      this.flvPlayer.attachMediaElement(this.videoElement.nativeElement);

      // 添加事件监听器
      this.addFlvPlayerEventListeners();

      // 验证绑定状态
      console.log('Video element after attachment:', {
        src: this.videoElement.nativeElement.src,
        readyState: this.videoElement.nativeElement.readyState,
        networkState: this.videoElement.nativeElement.networkState
      });


      // 检查网络连接
      fetch(url, { method: 'HEAD' })
        .then(response => {
          console.log('Network test for stream URL:', {
            status: response.status,
            statusText: response.statusText,
            headers: response.headers,
            ok: response.ok
          });
        })
        .catch(error => {
          console.error('Network test failed for stream URL:', error);
        });

      // 加载并开始播放（播放由 canplay 事件触发，避免 AbortError）
      this.flvPlayer.load();

      console.log('FLV Player initialized for URL:', url);
      console.log('FLV Player configuration:', {
        type: 'flv',
        isLive: true,
        hasAudio: true,
        hasVideo: true,
        enableWorker: false,
        enableStashBuffer: false,
        lazyLoad: false,
        deferLoadAfterSourceOpen: false
      });

      // 定时检查video状态并尝试播放
      let retryCount = 0;
      const checkAndPlay = () => {
        const video = this.videoElement.nativeElement;
        console.log(`Check and play attempt ${retryCount + 1}, video state:`, {
          readyState: video.readyState,
          paused: video.paused,
          currentTime: video.currentTime,
          buffered: video.buffered.length > 0 ? `${video.buffered.start(0)}-${video.buffered.end(0)}` : 'empty'
        });

        if (video.readyState >= 1 && video.paused) {
          video.play().then(() => {
            console.log('Delayed play successful');
          }).catch(error => {
            console.error('Delayed play failed:', error);
            retryCount++;
            if (retryCount < 5) {
              setTimeout(checkAndPlay, 2000);
            }
          });
        } else if (retryCount < 10) {
          retryCount++;
          setTimeout(checkAndPlay, 1000);
        }
      };

      // 2秒后开始检查
      setTimeout(checkAndPlay, 2000);
    } catch (error) {
      console.error('Failed to initialize FLV player:', error);
    }
  }

  private addFlvPlayerEventListeners(): void {
    if (!this.flvPlayer) return;

    // 错误事件
    this.flvPlayer.on(flvjs.Events.ERROR, (errorType: string, errorDetail: string, errorInfo: any) => {
      console.error('FLV Player Error:', errorType, errorDetail, errorInfo);
      if (this.activeDevice) {
        this.activeDevice.failedFlag = true;
        this.activeDevice.desc = `Error: ${errorDetail}`;
        this.cdRef.markForCheck();
      }
    });

    // 加载完成事件
    this.flvPlayer.on(flvjs.Events.LOADING_COMPLETE, () => {
      console.log('FLV loading complete');
    });

    // 更多flv.js事件
    this.flvPlayer.on(flvjs.Events.SCRIPTDATA_ARRIVED, () => {
      console.log('FLV script data arrived');
    });

    this.flvPlayer.on(flvjs.Events.STATISTICS_INFO, (info: any) => {
      console.log('FLV statistics:', info);

      // 每次统计更新时检查video状态，如果readyState还是0且有数据，强制重建连接
      const video = this.videoElement.nativeElement;
      if (info.totalSegmentCount > 0 && video.readyState === 0 && info.decodedFrames === 0) {
        console.log('Data received but video readyState still 0, attempting MediaSource reset');

        // 计数器防止无限重试
        if (!this.mediaSourceRetryCount) {
          this.mediaSourceRetryCount = 0;
        }

        if (this.mediaSourceRetryCount < 3) {
          this.mediaSourceRetryCount++;
          console.log(`MediaSource reset attempt ${this.mediaSourceRetryCount}`);

          setTimeout(() => {
            // 使用当前URL重新创建播放器，优先HLS
            if (this.url) {
              console.log('Recreating player with HLS priority fallback');
              this.recreatePlayerWithHlsPriority(this.url);
            }
          }, 2000);
        }
      }

      // 如果有解码帧，重置重试计数器
      if (info.decodedFrames > 0) {
        this.mediaSourceRetryCount = 0;
      }
    });

    // 媒体信息事件
    this.flvPlayer.on(flvjs.Events.MEDIA_INFO, (mediaInfo: any) => {
      console.log('FLV media info:', mediaInfo);
      console.log('Video codec:', mediaInfo.videoCodec);
      console.log('Audio codec:', mediaInfo.audioCodec);
      console.log('Video DataRate:', mediaInfo.videoDataRate);
      console.log('Audio DataRate:', mediaInfo.audioDataRate);

      // 强制尝试播放，因为有些流在解析完媒体信息后需要手动触发
      setTimeout(() => {
        const video = this.videoElement.nativeElement;
        console.log('Forcing play attempt after media info, video state:', {
          readyState: video.readyState,
          paused: video.paused,
          currentTime: video.currentTime
        });

        if (video.paused && video.readyState >= 2) {
          video.play().then(() => {
            console.log('Forced play successful after media info');
          }).catch(error => {
            console.error('Forced play failed after media info:', error);
          });
        }
      }, 500);
    });

    // Video 元素事件
    const video = this.videoElement.nativeElement;

    video.addEventListener('loadstart', () => {
      console.log('Video loading started');
      console.log('Video element state:', {
        readyState: video.readyState,
        networkState: video.networkState,
        currentTime: video.currentTime,
        duration: video.duration,
        paused: video.paused,
        ended: video.ended,
        src: video.src
      });
    });

    video.addEventListener('canplay', () => {
      console.log('Video can start playing, readyState:', video.readyState);
      // Check if video is ready and not already playing
      if (video.readyState >= 3 && video.paused) {
        try {
          console.log('Attempting to play video...');
          const playPromise = video.play();

          if (playPromise !== undefined) {
            playPromise.then(() => {
              console.log('Video started playing successfully');
            }).catch(error => {
              console.error('Play failed:', error);
              // Handle autoplay policy restrictions
              if (error.name === 'NotAllowedError') {
                console.log('Autoplay prevented by browser policy. User interaction required.');
                if (this.activeDevice) {
                  this.activeDevice.desc = 'Click to start video playback';
                  this.cdRef.markForCheck();
                }
              }
            });
          }
        } catch (error) {
          console.error('Play error:', error);
        }
      }
    });

    video.addEventListener('playing', () => {
      console.log('Video is playing');
      if (this.activeDevice) {
        this.activeDevice.desc = '正在播放直播流';
        this.cdRef.markForCheck();
      }
    });

    video.addEventListener('error', (e) => {
      console.error('Video element error:', e);
      console.error('Video error details:', {
        error: video.error,
        code: video.error?.code,
        message: video.error?.message,
        readyState: video.readyState,
        networkState: video.networkState,
        src: video.src,
        currentSrc: video.currentSrc
      });

      // 详细的错误代码解释
      if (video.error) {
        const errorMessages: { [key: number]: string } = {
          1: 'MEDIA_ERR_ABORTED - The video playback was aborted',
          2: 'MEDIA_ERR_NETWORK - A network error caused the video download to fail',
          3: 'MEDIA_ERR_DECODE - The video playback was aborted due to a corruption problem or unsupported features',
          4: 'MEDIA_ERR_SRC_NOT_SUPPORTED - The video could not be loaded, either because the server or network failed or format not supported'
        };
        console.error('Error type:', errorMessages[video.error.code] || `Unknown error code: ${video.error.code}`);
      }

      if (this.activeDevice) {
        this.activeDevice.failedFlag = true;
        this.activeDevice.desc = video.error ? `播放错误: ${video.error.code}` : '视频播放错误';
        this.cdRef.markForCheck();
      }
    });

    // Add click handler for manual play (to handle autoplay restrictions)
    video.addEventListener('click', () => {
      if (video.paused) {
        console.log('User clicked video, attempting to play...');
        video.play().then(() => {
          console.log('Manual play successful');
        }).catch(error => {
          console.error('Manual play failed:', error);
        });
      }
    });

    // Additional debug event listeners
    video.addEventListener('loadeddata', () => {
      console.log('Video loadeddata - first frame loaded, readyState:', video.readyState);
    });

    video.addEventListener('loadedmetadata', () => {
      console.log('Video loadedmetadata - duration and dimensions available');
    });

    video.addEventListener('canplaythrough', () => {
      console.log('Video canplaythrough - can play without buffering');
    });

    video.addEventListener('waiting', () => {
      console.log('Video waiting - buffering');
    });

    video.addEventListener('stalled', () => {
      console.log('Video stalled - network issues or buffering');
    });

    video.addEventListener('suspend', () => {
      console.log('Video suspend - loading suspended');
    });

    video.addEventListener('abort', () => {
      console.log('Video abort - loading aborted');
    });

    video.addEventListener('emptied', () => {
      console.log('Video emptied - media element emptied');
    });
  }

  private destroyPlayers(): void {
    // 销毁FLV播放器
    if (this.flvPlayer) {
      try {
        this.flvPlayer.pause();
        this.flvPlayer.unload();
        this.flvPlayer.detachMediaElement();
        this.flvPlayer.destroy();
        this.flvPlayer = null;
        console.log('FLV Player destroyed');
      } catch (error) {
        console.error('Error destroying FLV player:', error);
      }
    }

    // 销毁HLS播放器
    if (this.hlsPlayer) {
      try {
        this.hlsPlayer.destroy();
        this.hlsPlayer = null;
        console.log('HLS Player destroyed');
      } catch (error) {
        console.error('Error destroying HLS player:', error);
      }
    }

    this.currentPlayerType = null;
  }

  private recreatePlayerWithHlsPriority(url: string): void {
    console.log('Recreating player with HLS priority fallback');
    this.destroyPlayers();

    // 清除video element的src
    const video = this.videoElement.nativeElement;
    video.src = '';
    video.load();

    setTimeout(() => {
      try {
        if (Hls.isSupported()) {
          console.log('Fallback: Trying comprehensive HLS strategies first');

          // 生成所有可能的HLS URL变体
          const hlsUrls = this.generateAllHlsVariants(url);
          console.log('Fallback HLS variants:', hlsUrls);

          this.tryHlsUrlSequentially(hlsUrls, 0, () => {
            console.log('All HLS fallback attempts failed, using aggressive FLV config');
            this.initAggressiveFlvPlayer(url);
          });
        } else {
          console.log('HLS not supported, using aggressive FLV configuration as fallback');
          this.initAggressiveFlvPlayer(url);
        }
      } catch (error) {
        console.error('Failed to create fallback player:', error);
        // 最后的备选方案
        this.initAggressiveFlvPlayer(url);
      }
    }, 1000);
  }

  private generateAllHlsVariants(originalUrl: string): string[] {
    const variants: string[] = [];

    if (originalUrl.includes('.flv')) {
      const baseUrl = originalUrl.substring(0, originalUrl.lastIndexOf('.flv'));
      const urlParts = originalUrl.split('/');
      const filename = urlParts[urlParts.length - 1].replace('.flv', '');

      // 变体1: 直接替换扩展名
      variants.push(originalUrl.replace('.flv', '.m3u8'));

      // 变体2: live -> hls路径替换
      if (originalUrl.includes('/live/')) {
        variants.push(originalUrl.replace('/live/', '/hls/').replace('.flv', '.m3u8'));
      }

      // 变体3: 添加hls子目录
      const pathBase = urlParts.slice(0, -1).join('/');
      variants.push(`${pathBase}/hls/${filename}.m3u8`);

      // 变体4: playlist格式
      variants.push(`${baseUrl}/playlist.m3u8`);
      variants.push(`${baseUrl}/index.m3u8`);

      // 变体5: 不同的路径结构
      if (urlParts.length > 2) {
        const domain = urlParts.slice(0, 3).join('/');
        variants.push(`${domain}/hls/${filename}.m3u8`);
        variants.push(`${domain}/live-hls/${filename}.m3u8`);
      }

      // 变体6: 添加常见的HLS参数
      const baseUrlWithParams = originalUrl.split('?')[0].replace('.flv', '.m3u8');
      const params = originalUrl.includes('?') ? originalUrl.split('?')[1] : '';
      if (params) {
        variants.push(`${baseUrlWithParams}?${params}`);
        variants.push(`${baseUrlWithParams.replace('/live/', '/hls/')}?${params}`);
      }
    }

    // 去重
    return [...new Set(variants)];
  }

  private initAggressiveFlvPlayer(url: string): void {
    console.log('Creating FLV player with aggressive live stream configuration');

    try {
      this.flvPlayer = flvjs.createPlayer({
        type: 'flv',
        url: url,
        isLive: true,
        cors: true,
        withCredentials: false,
        hasAudio: false, // 尝试只播放视频，忽略音频问题
        hasVideo: true
      }, {
        enableWorker: false,
        enableStashBuffer: true,
        stashInitialSize: 128, // 减小初始缓冲区，加快启动
        isLive: true,
        lazyLoad: false,
        autoCleanupSourceBuffer: true,
        autoCleanupMaxBackwardDuration: 5, // 增加清理间隔
        autoCleanupMinBackwardDuration: 3,
        fixAudioTimestampGap: false,
        accurateSeek: false,
        seekType: 'range',
        deferLoadAfterSourceOpen: false,
        reuseRedirectedURL: true
      });

      this.currentPlayerType = 'flv';
      console.log('Aggressive FLV Player created with enhanced buffer settings');

      // 重新绑定
      const video = this.videoElement.nativeElement;
      this.flvPlayer.attachMediaElement(video);

        // 添加错误处理但不添加统计信息监听（避免无限循环）
        this.flvPlayer.on(flvjs.Events.ERROR, (errorType: string, errorDetail: string, errorInfo: any) => {
          console.error('Fallback FLV Player Error:', errorType, errorDetail, errorInfo);
        });

        // 添加所有可能的事件监听器用于调试
        this.flvPlayer.on(flvjs.Events.LOADING_COMPLETE, () => {
          console.log('Fallback: Loading complete');
        });

        this.flvPlayer.on(flvjs.Events.RECOVERED_EARLY_EOF, () => {
          console.log('Fallback: Recovered early EOF');
        });

        this.flvPlayer.on(flvjs.Events.MEDIA_INFO, (mediaInfo: any) => {
          console.log('Fallback FLV media info:', mediaInfo);

          // 检查是否只有视频流
          if (mediaInfo.hasVideo && !mediaInfo.hasAudio) {
            console.log('Stream has video only, this might work better');
          }

          // 更激进的播放尝试
          let attempts = 0;
          const tryPlay = () => {
            attempts++;
            const video = this.videoElement.nativeElement;
            console.log(`Fallback play attempt ${attempts}, video state:`, {
              readyState: video.readyState,
              paused: video.paused,
              buffered: video.buffered.length > 0 ? `${video.buffered.start(0)}-${video.buffered.end(0)}` : 'empty',
              duration: video.duration,
              currentTime: video.currentTime
            });

            if (video.readyState >= 1) {
              video.play().then(() => {
                console.log('Fallback play successful');
              }).catch(error => {
                console.error(`Fallback play failed (attempt ${attempts}):`, error);
                if (attempts < 3) {
                  setTimeout(tryPlay, 2000);
                }
              });
            } else if (attempts < 5) {
              setTimeout(tryPlay, 1000);
            }
          };

          setTimeout(tryPlay, 500);
        });

      // 加载
      this.flvPlayer.load();

    } catch (error) {
      console.error('Failed to create aggressive FLV player:', error);
    }
  }
}

export interface SelectDevice {
  url: string | null | undefined;
  sn: string | undefined;
}

export interface DeviceData {
  id: number | string,
  name?: string;
  sn?: string,
  value?: number,
  unit?: string,
  unitName?: string,
  selected?: boolean;
  failedFlag?: boolean;
  loading?: boolean;
  desc?: string,
  member?: string,
  team?: string,
  status: 'normal' | 'alarm' | 'offline'
}

