import { Component, Input, Output, TemplateRef } from '@angular/core';
import { ModalRef } from '@odx/angular/components/modal';
import {
  FormGroup,
  FormControl,
  Validators,
} from '@angular/forms';
import { selectOption } from '@services/context.service'
import { LocalStorageService } from './../../../services/local-storage.service';
import { MemberControllerBatchAssignTeamRequest, SystemMemberService } from 'src/app/api/backend';

@Component({
  selector: 'app-add-member-to-team',
  templateUrl: './add-member-to-team.component.html',
  styleUrls: ['./add-member-to-team.component.scss']
})
export class AddMemberToTeamComponent {
  loading: boolean = false;
  modalData: Data;
  memberIds: number[] = [];
  memberToTeamForm: FormGroup = new FormGroup({
    team: new FormControl(null, [Validators.required])
  });
  teamOptions: selectOption[] = [];

  constructor(
    private modalRef: ModalRef<Data, boolean>,
    private systemMemberService: SystemMemberService,

  ) {
    this.modalData = modalRef.data;
    this.teamOptions = LocalStorageService.getItem('teamOptions');
    this.memberIds = this.modalData.memberIds
    console.log( this.modalData)
  }
  public submit() {
    const formValue = this.memberToTeamForm.value;
    this.memberToTeamForm.markAllAsTouched()
    if (!this.memberToTeamForm.valid) {
      return;
    }
    let teamId: number = formValue.team;
    const payload: MemberControllerBatchAssignTeamRequest = {
      member_ids: [],
      team_id: teamId,
    };
    // Assuming the payload should have a 'members' array of numbers (member IDs)
    this.memberIds.forEach(id => {
      payload.member_ids?.push(id);
    });
    this.systemMemberService.memberControllerBatchAssignTeam(payload).subscribe(
      (_) => {
        this.loading = false;
        this.modalRef.close(true);
      },
      (_) => {
        this.loading = false;
      }
    );
  }
}

export interface Data {
  memberIds: number[];
}
