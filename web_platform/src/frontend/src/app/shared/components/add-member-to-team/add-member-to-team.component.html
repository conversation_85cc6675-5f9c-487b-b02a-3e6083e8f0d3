<div class="add-member-to-team-container">
  <div class="header-container">
    <odx-modal-header>
      <odx-area-header class="header-title">
        {{ 'teams.addMemberToTeam' | translate}}
      </odx-area-header>
    </odx-modal-header>
  </div>
  <div class="modal-content">
    <odx-modal-content>
      <form class="form-item" [formGroup]="memberToTeamForm">
        <odx-form-field odxLayout="12">
          <odx-form-field-label>{{
            "teams.team" | translate
          }}</odx-form-field-label>
            <odx-select
            [placeholder]="'common.selectHolder' | translate"
            formControlName="team"
            odxFormFieldControl
          >
            <odx-select-option
              [value]="item.value"
              *ngFor="let item of teamOptions | odxSelectSearchFilter"
            >
              {{ item.label }}
            </odx-select-option>
            <input
              [placeholder]="'teams.team' | translate"
              [odxSelectSearchField]="'teams.noTeamTypeFond' | translate"
            />
          </odx-select>
        </odx-form-field>
      </form>
    </odx-modal-content>
    <odx-toast-container id="dialogToast" />
  </div>
  <odx-modal-footer
      odxLayout="flex "
      class="justify-end"
    >
      <button odxButton odxModalDismiss>
        {{ "cancel" | translate }}
      </button>
      <button
        [odxLoadingSpinner]="loading"
        odxButton
        variant="primary"
        (click)="submit()"
      >
        {{ "confirm" | translate }}
      </button>
    </odx-modal-footer>
</div>