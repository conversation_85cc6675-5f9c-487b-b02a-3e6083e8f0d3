import { Component, Input, ChangeDetectorRef, OnChanges, SimpleChanges } from '@angular/core';
import { IndexQuerySchema, SystemDevice, SystemTenantService, SystemTenantAdminComplex } from 'src/app/api/backend';
import { fileSaver } from '../../utils/constant';
import { ModalService } from '@odx/angular/components/modal';
import { SortStatus, TableHeaderCell } from '@odx/angular/components/table';
import { TranslateService } from '@ngx-translate/core';
import * as _ from 'lodash';
import { TimeZoneService } from '@services/time-zone.service';
import { OdxToastCustomService } from '@services/odx-toast-custom.service';
import { UtilService } from '@services/util.service';
import {
  concatMap,
  EMPTY,
} from 'rxjs';
import { DeviceEditComponent } from '../device-edit/device-edit.component';
import { PieceData } from '../area-pieces/area-pieces.component'
import { AlarmPositionComponent } from 'src/app/shared/components/alarm-position/alarm-position.component';
import { Router } from '@angular/router';
import { SettingsService, User } from '@services/settings.service';

@Component({
  selector: 'device-list',
  templateUrl: './device-list.component.html',
  styleUrls: ['./device-list.component.scss']
})
export class DeviceListComponent implements OnChanges {
  private sortParams: string = '-id';
  private filterParams!: Record<string, string>;
  sortFields: any = {
    'lastUsageTime': 'updated_at',
    'DateOfManufacture': 'manufacture_date',
    'SIMExpired': 'sim_expired_date'
  };
  public typeHeaderList: { [key: string]: string[] } = {
    'pam': ['name','identifier', 'last_calibration_time', 'next_calibration_time', 'lastMember', 'team', 'version', 'remark', 'history'],
    'ba': ['name','identifier', 'next_calibration_time', 'lastUsageTime', 'lastMember', 'team', 'version', 'remark', 'history'],
    'tic': ['name','identifier', 'lastUsageTime', 'DateOfManufacture', 'SIMExpired', 'lastMember', 'team', 'remark', 'history'],
    'fixed': ['name','identifier', 'network', 'anomaly', 'inhibit', 'realValue', 'tag', 'controller', 'gas', 'measurement', 'last_calibration_time', 'next_calibration_time', 'location', 'history'],
    'controller': ['name','identifier', 'type', 'status', 'channel', 'location', 'remark', 'subDevices'],
    'ttt': ['name','identifier', 'lastUsageTime', 'lastMember', 'team', 'remark'],
    'other': ['name','identifier', 'next_calibration_time', 'remark']
  };
  public currentKeysList: string[] = [];
  public selectedElements: any[] = [];
  closeDelMsgBoxShow: boolean = false;
  dataToDisplay: any[] = [
    {
      selected: false,
      name: null,
      nfc: null,
      team: null,
      phone: null,
      email: null,
      create_time: null
    },
  ];

  public headerData: TableHeaderCell[] = [];
  public allHeaderData: TableHeaderCell[] = [
    { name: 'selected', check: true },
    {
      name: 'name',
      title: this.translateService.instant('device.name'),
      sortable: true,
      filter: true,
    },
    {
      name: 'identifier',
      title: this.translateService.instant('device.identifier'),
      sortable: true,
      filter: true,
    },
    {
      name: 'last_calibration_time',
      title: this.translateService.instant('device.lastCalibrationTime'),
      sortable: true,
    },
    {
      name: 'next_calibration_time',
      title: this.translateService.instant('device.nextCalibrationTime'),
      sortable: true,
    },
    {
      name: 'lastUsageTime',
      title: this.translateService.instant('device.lastUsageTime'),
      sortable: true
    },
    {
      name: 'DateOfManufacture',
      title: this.translateService.instant('device.DateOfManufacture'),
      sortable: true
    },
    {
      name: 'SIMExpired',
      title: this.translateService.instant('device.SIMExpired'),
      sortable: true
    },
    {
      name: 'lastMember',
      title: this.translateService.instant('device.lastMember'),
      sortable: false
    },
    {
      name: 'team',
      title: this.translateService.instant('device.team'),
      sortable: false,
      filter: false,
    },
    {
      name: 'type',
      title: this.translateService.instant('device.type'),
      sortable: true,
      filter: true,
    },
    {
      name: 'status',
      title: this.translateService.instant('device.status'),
      sortable: false,
      filter: false,
    },
    {
      name: 'channel',
      title: this.translateService.instant('device.channel'),
      sortable: true,
      filter: true,
    },
    {
      name: 'network',
      title: this.translateService.instant('device.network'),
      sortable: true,
      filter: true,
    },
    {
      name: 'anomaly',
      title: this.translateService.instant('device.anomaly'),
      sortable: true,
      filter: true,
    },
    {
      name: 'inhibit',
      title: this.translateService.instant('device.notice'),
      sortable: true,
      filter: true,
    },
    {
      name: 'realValue',
      title: this.translateService.instant('device.realValue'),
      sortable: true,
      filter: true,
    },
    {
      name: 'tag',
      title: this.translateService.instant('device.tag'),
      sortable: true,
      filter: true,
    },
    {
      name: 'controller',
      title: this.translateService.instant('device.controller'),
      sortable: true,
      filter: true,
    },
    {
      name: 'gas',
      title: this.translateService.instant('device.gas'),
      sortable: true,
      filter: true,
    },
    {
      name: 'measurement',
      title: this.translateService.instant('device.measurement'),
      sortable: true,
      filter: true,
    },
    {
      name: 'location',
      title: this.translateService.instant('common.location'),
      sortable: true,
      filter: true,
    },
    {
      name: 'version',
      title: this.translateService.instant('device.version'),
      sortable: false,
      filter: false,
    },
    {
      name: 'remark',
      title: this.translateService.instant('device.remark'),
      sortable: true,
      filter: true,
    },
    {
      name: 'subDevices',
      title: this.translateService.instant('device.subDevice'),
      sortable: false,
      filter: false,
    },
    {
      name: 'history',
      title: this.translateService.instant('device.history'),
      sortable: false,
      filter: false,
    }
  ];
  currentUser: User;

  pageIndex: number = 1;
  pageSize: number = 10;
  filter: string[] = [];
  showAll: boolean = false;
  total!: number
  loading: boolean = false;
  deleteLoading: boolean = false;
  listOfData: Device[] = [];
  baData: PieceData = {
    values: [25, 24, 23, 22, 20, 16, 14, 12, 8, 3],
    keys: ['13:23:02', '13:40:45', '13:45:34', '13:50:23', '13:55:10', '14:00:21', '14:05:45', '14:10:23', '14:15:00', '14:16:00'],
    pieces: [{ start: 0, end: 2 }, { start: 2, end: 5 }, { start: 5, end: 8 }, {start: 8, end: 10 }]
  };
  tenantId?: number;
  @Input() typeList?: string[];
  @Input() refreshTrigger?: any; // 父组件传入的刷新触发器


  ngOnChanges(changes: SimpleChanges): void {
    if ((changes['typeList'] && changes['typeList'].currentValue !== changes['typeList'].previousValue) || changes['refreshTrigger']) {
      this.updateHeaderData();
      this.listOfData = []
      this.loadData();
    }
  }

  private updateHeaderData(): void {
    // 根据 type 获取对应的表头字段列表
    if (this.typeList) {
      if (this.typeList.indexOf('all') !== -1) {
        this.headerData = this.allHeaderData;
        this.filter = [];
        console.log(this.typeList)
        console.log(this.headerData)
      } else {
        let headerFields: string[] = [];
        let typeFilter: string[] = []
        this.filter = [];
        this.typeList.forEach(item => {
          headerFields = headerFields.concat(this.typeHeaderList[item]);
          if (item == 'pam') {
            typeFilter.push('gas_detector')
          } else if (item == 'ba') {
            typeFilter.push('pressure_gauge')
          } else if (item == 'ttt') {
            typeFilter.push('smart_base_station')
            typeFilter.push('ttt')
          } else {
            typeFilter.push(item)
          }
        })
        this.filter = [`deviceMold.model_code:in:${typeFilter.join(',')}`]
        this.currentKeysList = headerFields;
        // 构建表头数据
        this.headerData = [
          { name: 'selected', check: true }
        ];

        // 根据字段列表动态添加表头
        this.allHeaderData.forEach(headerItem => {
          if (headerFields.indexOf(headerItem.name) !== -1) {
            this.headerData.push(headerItem);
          }
        });
      }
    }
  }

  constructor(
    private modalService: ModalService,
    private translateService: TranslateService,
    private cd: ChangeDetectorRef,
    public timeZoneService: TimeZoneService,
    private msgSrv: OdxToastCustomService,
    private utils: UtilService,
    private router: Router,
    private systemTenantService: SystemTenantService,
    private settingsService: SettingsService
  ) {
    this.currentUser = this.settingsService.User;
    this.tenantId = (this.currentUser.accountable as SystemTenantAdminComplex)?.tenant_id as number ?? 1;
    // 初始化表头数据
    this.updateHeaderData();
    // this.loadData();
  }
  // 当前页码变化
  public pageIndexChange(index: number): void {
    this.loading = true;
    this.pageIndex = index;
    this.loadData();
  }

  // 每页数据量变化
  public pageSizeChange(size: number): void {
    this.loading = true;
    this.pageIndex = 1;
    this.pageSize = size;
    this.loadData();
  }
  // 获取设备报警列表
  public loadData(): void {
    this.checkAll({ column: 'selected', check: false });
    this.loading = true;
    const query: IndexQuerySchema = {
      page: this.pageIndex,
      size: this.pageSize,
      filter: this.filter,
      sort: [this.sortParams],
      _with: ['deviceMold'],
    }
    this.systemTenantService.tenantControllerIndexDevice(this.tenantId ?? 1, false, undefined, false, true, query).subscribe(data => {
      this.loading = false
      this.total = data.meta.total;
      this.listOfData = data.data.map((item: any) => {
        return {
          ...item,
          selected: false,
          showDownItem: false,
          isNormalItem: true
        }
      })
    })
  }
  // 全选/全不选
  public checkAll(event: { column: string; check: boolean }): void {
    const { column, check } = event;
    this.listOfData.forEach((item) => {
      if (column in item)
        (item[column] as boolean) = check;
    });
    if (check) {
      this.closeDelMsgBoxShow = true;
      this.selectedElements = this.listOfData;
    } else {
      this.selectedElements = [];
      this.closeDelMsgBoxShow = false;
    }
  }

  public checkChange(_event: any) {
    setTimeout(() => {
      this.selectedElements = this.listOfData.filter((element) => {
        return element['selected'] === true;
      });
      this.closeDelMsgBoxShow = true;
    }, 5);
  }

  // 关闭 bar
  public closeDelMsgBox() {
    this.closeDelMsgBoxShow = false;
  }


  // 排序
  public sorted(sortParams: SortStatus): void {
    if (sortParams && sortParams.column) {
      let field = null;
      if (_.indexOf(['lastUsageTime', 'DateOfManufacture', 'SIMExpired'], sortParams.column) !== -1) {
        field = this.sortFields[sortParams.column]
      } else {
        field = sortParams.column
      }
      this.sortParams =
        (sortParams.sortVariant == 'asc' ? '' : '-') + field;
      this.sortParams += ',-id'
    } else {
      this.sortParams = '-id';
    }
    this.loadData();
  }

  // 表格头部筛选
  public tableFilter(filterParams: Record<string, string>): void {
    this.filterParams = filterParams;
    this.filterTeam();
  }

  // 条件筛选team
  public filterTeam(event: any = null): void {
    setTimeout(() => {
      const tableFilterValue = this.filterParams;
      this.filter = [];
      this.pageIndex = 1;
      this.loadData();
    });
  }

  // 批量删除
  public batchDeleteDevice() {
    let ids: any = []
    if (this.selectedElements?.length) {
      ids = this.selectedElements.map(item => item.id)
    }
    if (!ids.length) {
      this.msgSrv.warning(this.translateService.instant('common.deleteEmptyNote'));
      return;
    }
    const confirmModal = this.utils.openDeleteConfirmModal({
      title: 'device.delete',
      deleteItem: this.translateService.instant('the selected items'),
    });
    confirmModal.onClose$
      .pipe(
        concatMap((confirm) => {
          if (confirm) {
            this.deleteLoading = true;
            return this.translateService.get('success.delete');
          } else {
            return EMPTY;
          }
        })
      )
      .subscribe(
        (translate) => {
          this.msgSrv.success(translate);
          this.loadData();
        },
        (_err) => {
          this.deleteLoading = false;
        }
      );
  }

  // 切换成员列表显示状态
  public toggleDownItem(device: Device): void {
    device.showDownItem = !device.showDownItem;
    console.log("000000")
    console.log(this.listOfData)
    this.updateDisplayData();
  }

   // 更新显示数据，插入或移除成员列表行
   private updateDisplayData(): void {
      const newDisplayData: Device[] = [];

      this.listOfData.forEach(device => {
        // 添加团队行
        if (device.isNormalItem) {
          newDisplayData.push({
            ...device,
            isNormalItem: true
          });

          // 如果展开，添加成员列表行
          if (device.showDownItem) {
            newDisplayData.push({
              ...device,
              isNormalItem: false,
              showDownItem: false,
              key: `down-item-${device['key']}`,
            });
          }
        }
      });
      console.log(newDisplayData)
      this.listOfData = newDisplayData;
    }

  // 打开编辑弹框
  public batchEditMember() {
    let ids: any = []
    if (this.selectedElements?.length) {
      ids = this.selectedElements.map(item => item.id)
    }
    if (!ids.length) {
      this.msgSrv.warning(this.translateService.instant('common.editEmptyNote'));
      return;
    }
    let editModal = this.modalService.open(DeviceEditComponent, {
      data: {
        deviceIds: ids,
        deviceType: this.typeList?.length ? this.typeList[0] : null
      }
    });
    editModal.onClose$.subscribe((res) => {
      if (res) {
        this.loadData();
      }
    });
  }


  // 打开报警位置弹窗
  public showPosition(item: Device) {
    if (item?.longitude && item.latitude) {
      this.modalService.open(AlarmPositionComponent, {
        data: { coordinate: item?.longitude + ' ' + item.latitude, title: 'common.gps' }
      });
    }
  }

  // 打开设备详情
  goDetail(item: Device) {
    this.router.navigate([`tenant/device/detail/${item.id}`]);
  }

}
export interface Device extends SystemDevice {
  [key: string]: any;
  selected: boolean;
  isNormalItem?: boolean;
  showDownItem?: boolean;
}
// export interface Device {
//   [key: string]: any;
//   id: number;
//   selected: boolean;
//   name: string;
//   sn?: string;
//   lastCalibrationTime?: string;
//   nextCalibrationTime?: string;
//   lastUsageTime?: string;
//   lastMember?: string;
//   team?: string;
//   type?: string;
//   status?: string;
//   channel?: string;
//   remark?: string;
//   history?: string;
//   network?: string;
//   anomaly?: string;
//   inhibit?: string;
//   realValue?: string;
//   tag?: string;
//   controller?: string;
//   gas?: string;
//   measurement?: string;
//   location?: string;
//   subDevice?: number;
//   isNormalItem?: boolean;
//   showDownItem?: boolean;
//   deviceType?: string;
//   longitude?: string;
//   latitude?: string;
// }
