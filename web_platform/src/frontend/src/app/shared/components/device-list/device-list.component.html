<odx-content-box style="height: 100%;" class="content-box">
  <div class="device-table">
    <table odxTable [variant]="'default'" [data]="dataToDisplay" [headerData]="headerData" (sorted)="sorted($event)"
      (filtered)="tableFilter($event)" aria-describedby="team-table" (checked)="checkAll($event)"
      [odxLoadingSpinner]="loading">
      <thead aria-hidden="true">
        <th></th>
      </thead>
      <tr odx-table-row *ngFor="let item of listOfData; let index = index">
        <td *ngIf="item.isNormalItem" style="width: 50px" odx-table-cell [(checked)]="item['selected']" (click)="checkChange($event)"></td>
        <td *ngIf="item.isNormalItem && (typeList.indexOf('all') !== -1 || currentKeysList.indexOf('name') !== -1)" odx-table-cell>{{ item.name }}</td>
        <td *ngIf="item.isNormalItem && (typeList.indexOf('all') !== -1 || currentKeysList.indexOf('identifier') !== -1)" odx-table-cell>{{ item.identifier }}</td>
        <td *ngIf="item.isNormalItem && (typeList.indexOf('all') !== -1 || currentKeysList.indexOf('last_calibration_time') !== -1)" odx-table-cell>{{ item.last_calibration_time }}</td>
        <td *ngIf="item.isNormalItem && (typeList.indexOf('all') !== -1 || currentKeysList.indexOf('next_calibration_time') !== -1)" odx-table-cell>{{ item.next_calibration_time }}</td>
        <td *ngIf="item.isNormalItem && (typeList.indexOf('all') !== -1 || currentKeysList.indexOf('lastUsageTime') !== -1)" odx-table-cell>{{ item.updated_at }}</td>
        <td *ngIf="item.isNormalItem && (typeList.indexOf('all') !== -1 || currentKeysList.indexOf('DateOfManufacture') !== -1)" odx-table-cell>{{ item.manufacture_date }}</td>
        <td *ngIf="item.isNormalItem && (typeList.indexOf('all') !== -1 || currentKeysList.indexOf('SIMExpired') !== -1)" odx-table-cell>{{ item.sim_expired_date }}</td>
        <td *ngIf="item.isNormalItem && (typeList.indexOf('all') !== -1 || currentKeysList.indexOf('lastMember') !== -1)" odx-table-cell>
          {{item.associated_members && item.associated_members.length ? item.associated_members[item.associated_members.length - 1].name : item.last_members}}
        </td>
        <td *ngIf="item.isNormalItem && (typeList.indexOf('all') !== -1 || currentKeysList.indexOf('team') !== -1)" odx-table-cell>
          {{item.associated_members && item.associated_members.length ? item.associated_members[item.associated_members.length - 1].team?.name : ''}}
        </td>
        <td *ngIf="item.isNormalItem && (typeList.indexOf('all') !== -1 || currentKeysList.indexOf('type') !== -1)" odx-table-cell>{{ item.type }}</td>
        <td *ngIf="item.isNormalItem && (typeList.indexOf('all') !== -1 || currentKeysList.indexOf('status') !== -1)" odx-table-cell>
          <span *ngIf="item.deviceType == 'controller'">
            <app-icon class="off-line-icon" *ngIf="item.status == false" type="built-in" name="wifi-disabled" iconSet="safety"></app-icon>
            <app-icon class="on-line-icon" *ngIf="item.status !== false" type="built-in" name="wifi-100-percent" iconSet="safety"></app-icon>
          </span>
          <span *ngIf="item.deviceType == 'fixed'">{{ item.status }}</span>
        </td>
        <td *ngIf="item.isNormalItem && (typeList.indexOf('all') !== -1 || currentKeysList.indexOf('channel') !== -1)" odx-table-cell>{{ item.channel }}</td>
        <td *ngIf="item.isNormalItem && (typeList.indexOf('all') !== -1 || currentKeysList.indexOf('network') !== -1)" odx-table-cell>{{ item.network }}</td>
        <td *ngIf="item.isNormalItem && (typeList.indexOf('all') !== -1 || currentKeysList.indexOf('anomaly') !== -1)" odx-table-cell>{{ item.anomaly }}</td>
        <td *ngIf="item.isNormalItem && (typeList.indexOf('all') !== -1 || currentKeysList.indexOf('inhibit') !== -1)" odx-table-cell>{{ item.inhibit }}</td>
        <td *ngIf="item.isNormalItem && (typeList.indexOf('all') !== -1 || currentKeysList.indexOf('realValue') !== -1)" odx-table-cell>{{ item.realValue }}</td>
        <td *ngIf="item.isNormalItem && (typeList.indexOf('all') !== -1 || currentKeysList.indexOf('tag') !== -1)" odx-table-cell>{{ item.tag }}</td>
        <td *ngIf="item.isNormalItem && (typeList.indexOf('all') !== -1 || currentKeysList.indexOf('controller') !== -1)" odx-table-cell>{{ item.controller }}</td>
        <td *ngIf="item.isNormalItem && (typeList.indexOf('all') !== -1 || currentKeysList.indexOf('gas') !== -1)" odx-table-cell>{{ item.gas }}</td>
        <td *ngIf="item.isNormalItem && (typeList.indexOf('all') !== -1 || currentKeysList.indexOf('measurement') !== -1)" odx-table-cell>{{ item.measurement }}</td>
        <td *ngIf="item.isNormalItem && (typeList.indexOf('all') !== -1 || currentKeysList.indexOf('location') !== -1)" odx-table-cell>
           <span *ngIf="item.deviceType == 'controller'">
            <span class="location-item" *ngIf="item.longitude && item.latitude"  (click)="showPosition(item)">
              <app-icon type="built-in" name="location"></app-icon>
              <!-- {{ item.longitude }}° N,{{ item.latitude }}° W -->
            </span>
          </span>
        </td>
        <td *ngIf="item.isNormalItem && (typeList.indexOf('all') !== -1 || currentKeysList.indexOf('version') !== -1)" odx-table-cell>{{ item.version }}</td>
        <td *ngIf="item.isNormalItem && (typeList.indexOf('all') !== -1 || currentKeysList.indexOf('remark') !== -1)" odx-table-cell>{{ item.remark }}</td>
        <td *ngIf="item.isNormalItem && (typeList.indexOf('all') !== -1 || currentKeysList.indexOf('subDevices') !== -1)"odx-table-cell>
           <span *ngIf="item.deviceType == 'controller'" (click)="toggleDownItem(item)">
            <app-icon class="action-icon" *ngIf="!item.showDownItem" name="chevron-down"></app-icon>
            <app-icon class="action-icon" *ngIf="item.showDownItem" name="chevron-up"></app-icon>
          </span>
          {{ item.subDevice }}
        </td>
        <td *ngIf="item.isNormalItem && (typeList.indexOf('all') !== -1 || currentKeysList.indexOf('history') !== -1)" odx-table-cell>
          <app-icon class="action-icon" *ngIf="item.deviceType == 'fixed' || item.deviceType == 'pam'" type="built-in" name="data-send" iconSet="safety" (click)="goDetail(item)"></app-icon>
          <span *ngIf="item.deviceType == 'ba' || item.deviceType == 'tic'" (click)="toggleDownItem(item)">
            <app-icon class="action-icon" *ngIf="!item.showDownItem" name="chevron-down"></app-icon>
            <app-icon class="action-icon" *ngIf="item.showDownItem" name="chevron-up"></app-icon>
          </span>
        </td>
        <td colspan="9" *ngIf="item.deviceType == 'ba' && !item.isNormalItem" odx-table-cell>
          <div class="device-chart">
            <div class="chart-header">
              <span>{{"device.history" | translate }}</span>
              <button class="btn-item" odxButton>
                <app-icon class="action-icon" type="built-in" name="data-send" iconSet="safety"></app-icon>
                {{"device.moreData" | translate}}
              </button>
            </div>
            <area-pieces [data]="baData"></area-pieces>
          </div>
        </td>
         <td colspan="9" *ngIf="item.deviceType == 'tic' && !item.isNormalItem" odx-table-cell>
          <div class="device-chart">
            <div class="chart-header">
              <span>{{"device.history" | translate }}</span>
            </div>
            <area-pieces [data]="baData"></area-pieces>
          </div>
        </td>
        <td colspan="9" *ngIf="item.deviceType == 'controller' && !item.isNormalItem" odx-table-cell>
        </td>
      </tr>

    </table>
  </div>

  <odx-content-box-footer>
    <div class="bar-box">
      <div class="delMsgBox" *ngIf="closeDelMsgBoxShow && selectedElements.length">
        <div>
          <odx-icon name="close" (click)="closeDelMsgBox()"></odx-icon>
          <span> {{'app.itemsSelected' | translate:{num:selectedElements.length} }}</span>
        </div>
        <div class="bar-action">
          <div *ngIf="typeList.indexOf('all') === -1 && typeList.length === 1" class="action-item">
            <odx-icon name="edit" [odxLoadingSpinner]="deleteLoading" (click)="batchEditMember()"
              class="del-icon"></odx-icon>
          </div>
          <div class="action-item">
            <odx-icon name="delete" [odxLoadingSpinner]="deleteLoading" (click)="batchDeleteDevice()"
              class="del-icon"></odx-icon>
          </div>
        </div>
      </div>
    </div>
    <odx-table-pagination [total]="total" [pageSizeConfig]="false" [(currentPage)]="pageIndex" [pageSize]="pageSize"
      (currentPageChange)="pageIndexChange($event)" (onPageSizeChange)="pageSizeChange($event)"
      class="page-item"></odx-table-pagination>
  </odx-content-box-footer>
</odx-content-box>