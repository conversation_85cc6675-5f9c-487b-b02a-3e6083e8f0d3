import { Component, Input, ChangeDetectorRef, OnChanges, SimpleChanges  } from '@angular/core';
import { IndexQuerySchema, SystemMemberService, SystemMember, SystemMemberResource } from 'src/app/api/backend';
import { fileSaver } from '../../utils/constant';
import { ModalService } from '@odx/angular/components/modal';
import { SortStatus, TableHeaderCell } from '@odx/angular/components/table';
import { TranslateService } from '@ngx-translate/core';
import * as _ from 'lodash';
import { TimeZoneService } from '@services/time-zone.service';
import { OdxToastCustomService } from '@services/odx-toast-custom.service';
import { UtilService } from '@services/util.service';
import {
  concatMap,
  EMPTY,
  combineLatest,
  expand,
} from 'rxjs';
import { MemberEditComponent } from '../member-edit/member-edit.component';
import { AddMemberToTeamComponent } from '../add-member-to-team/add-member-to-team.component'
@Component({
  selector: 'member-list',
  templateUrl: './member-list.component.html',
  styleUrls: ['./member-list.component.scss']
})
export class MemberListComponent implements OnChanges{
  private sortParams: string = '-id';
  private filterParams!: Record<string, string>;
  sortFields: any = {

  };
  public selectedElements: any[] = [];
  closeDelMsgBoxShow: boolean = false;
  dataToDisplay: any[] = [
    {
      selected: false,
      name: null,
      card_id: null,
      team: null,
      phone: null,
      email: null,
      created_at: null
    },
  ];
  public headerData: TableHeaderCell[] = [
    { name: 'selected', check: true },
    {
      name: 'name',
      title: this.translateService.instant('teams.memberName'),
      sortable: true,
      filter: true,
    },
    {
      name: 'card_id',
      title: this.translateService.instant('teams.memberNFC'),
      sortable: true,
      filter: true,
    },
    {
      name: 'team',
      title: this.translateService.instant('teams.team'),
      sortable: true,
      filter: true,
    },
    {
      name: 'phone',
      title: this.translateService.instant('teams.phone'),
      sortable: true,
      filter: true,
    },
    {
      name: 'email',
      title: this.translateService.instant('teams.email'),
      sortable: true,
      filter: true,
    },
    {
      name: 'created_at',
      title: this.translateService.instant('teams.create_time'),
      sortable: true,
      filter: false,
    },
    {
      name: 'action',
      title: this.translateService.instant('app.action'),
      sortable: false,
      filter: false,
    }
  ];

  pageIndex: number = 1;
  pageSize: number = 10;
  filter: string[] = [];
  showAll: boolean = false;
  total!: number
  loading: boolean = false;
  deleteLoading: boolean = false;
  listOfData: Member[] = [];

  @Input() tenantId?: number;
  @Input() refreshTrigger?: any; // 父组件传入的刷新触发器
  @Input() start?: any;
  @Input() end?: any;

  ngOnChanges(changes: SimpleChanges) {
    if (changes['refreshTrigger'] || changes['start'] || changes['end']) {
      this.applyDateFilter();
      this.loadData();
    }
  }

  constructor(
    private modalService: ModalService,
    private translateService: TranslateService,
    private cd: ChangeDetectorRef,
    public timeZoneService: TimeZoneService,
    private msgSrv: OdxToastCustomService,
    private utils: UtilService,
    private systemMemberService: SystemMemberService
  ) {
    this.loadData();
  }
  // 当前页码变化
  public pageIndexChange(index: number): void {
    this.loading = true;
    this.pageIndex = index;
    this.loadData();
  }

  // 每页数据量变化
  public pageSizeChange(size: number): void {
    this.loading = true;
    this.pageIndex = 1;
    this.pageSize = size;
    this.loadData();
  }
  // 获取设备报警列表
  public loadData(): void {
    this.checkAll({ column: 'selected', check: false });
    this.loading = true;
    const query: IndexQuerySchema = {
      page: this.pageIndex,
      size: this.pageSize,
      filter: this.filter,
      sort: [this.sortParams],
      _with: ['team'],
    }
    this.systemMemberService.memberControllerIndex(query).subscribe(data => {
      this.loading = false;
      this.total = data.meta.total;
      this.listOfData = data.data.map((item: any) => {
        return {
          ...item,
          selected: false
        }
      })
    });

  }

  private applyDateFilter(): void {
    // 清理旧的 created_at 过滤条件
    this.filter = (this.filter || []).filter(f => !f.startsWith('created_at:'));
    if (this.start && this.end) {
      const startStr = this.formatDate(this.start);
      const endStr = this.formatDate(this.end, true);
      this.filter.push(`created_at:gte:${startStr}`);
      this.filter.push(`created_at:lte:${endStr}`);
      this.pageIndex = 1;
    }
  }

  private formatDate(d: any, endOfDay: boolean = false): string {
    try {
      const date = new Date(d);
      if (endOfDay) {
        date.setHours(23, 59, 59, 999);
      } else {
        date.setHours(0, 0, 0, 0);
      }
      const yyyy = date.getFullYear();
      const mm = String(date.getMonth() + 1).padStart(2, '0');
      const dd = String(date.getDate()).padStart(2, '0');
      const hh = String(date.getHours()).padStart(2, '0');
      const mi = String(date.getMinutes()).padStart(2, '0');
      const ss = String(date.getSeconds()).padStart(2, '0');
      return `${yyyy}-${mm}-${dd} ${hh}:${mi}:${ss}`;
    } catch {
      return '';
    }
  }
  // 全选/全不选
  public checkAll(event: { column: string; check: boolean }): void {
    const { column, check } = event;
    this.listOfData.forEach((item) => {
      if (column in item)
        (item[column] as boolean) = check;
    });
    if (check) {
      this.closeDelMsgBoxShow = true;
      this.selectedElements = this.listOfData;
    } else {
      this.selectedElements = [];
      this.closeDelMsgBoxShow = false;
    }
  }

  public checkChange(_event: any) {
    setTimeout(() => {
      this.selectedElements = this.listOfData.filter((element) => {
        return element['selected'] === true;
      });
      this.closeDelMsgBoxShow = true;
    }, 5);
  }

  // 关闭 bar
  public closeDelMsgBox() {
    this.closeDelMsgBoxShow = false;
  }


  // 排序
  public sorted(sortParams: SortStatus): void {
    if (sortParams && sortParams.column) {
      let field = null;
      if (_.indexOf([], sortParams.column) !== -1) {
        field = this.sortFields[sortParams.column]
      } else {
        field = sortParams.column
      }
      this.sortParams =
        (sortParams.sortVariant == 'asc' ? '' : '-') + field;
      this.sortParams += ',-id'
    } else {
      this.sortParams = '-id';
    }
    this.loadData();
  }

  // 表格头部筛选
  public tableFilter(filterParams: Record<string, string>): void {
    this.filterParams = filterParams;
    this.filterTeam();
  }

  // 条件筛选team
  public filterTeam(event: any = null): void {
    setTimeout(() => {
      const tableFilterValue = this.filterParams;
      this.filter = [];
      if (tableFilterValue) {
        if (tableFilterValue['name']) {
          this.filter.push(`name:like:%${tableFilterValue['name']}%`);
        }
        if (tableFilterValue['card_id']) {
          this.filter.push(`card_id:like:%${tableFilterValue['card_id']}%`);
        }
        if (tableFilterValue['phone']) {
          this.filter.push(`phone:like:%${tableFilterValue['phone']}%`);
        }
        if (tableFilterValue['email']) {
          this.filter.push(`email:like:%${tableFilterValue['email']}%`);
        }
      }
      this.pageIndex = 1;
      this.loadData();
    });
  }

  // 批量删除
  public batchDeleteMember() {
    let ids: any = []
    if (this.selectedElements?.length) {
      ids = this.selectedElements.map(item => item.id)
    }
    if (!ids.length) {
      this.msgSrv.warning(this.translateService.instant('common.deleteEmptyNote'));
      return;
    }
    const confirmModal = this.utils.openDeleteConfirmModal({
      title: 'teams.deleteMember',
      deleteItem: this.translateService.instant('the selected items'),
    });
    const msgKeys = ['success.delete', 'failed.delete'];
    confirmModal.onClose$
      .pipe(
        concatMap((confirm) => {
          if (confirm) {
            return combineLatest([
              this.systemMemberService.memberControllerBatchDestroy({ ids: ids }),
              this.translateService.get(msgKeys),
            ]);
          } else {
            return EMPTY;
          }
        })
      )
      .subscribe(
        ([, translate]) => {
          const [successMsg, failMsg] = msgKeys.map((key) => translate[key]);
          this.msgSrv.success(successMsg);
          this.deleteLoading = false;
          this.loadData();
        },
        (_err) => {
          const [successMsg, failMsg] = msgKeys.map((key) => _err[key]);
          this.msgSrv.warning(failMsg);
          this.deleteLoading = false;
        }
      );
  }
  // 删除member
  public deleteMember(member: SystemMemberResource): void {
    const confirmModal = this.utils.openDeleteConfirmModal({
      title: 'teams.deleteMember',
      deleteItem: member.name,
    });
    const msgKeys = ['success.delete', 'failed.delete'];
    confirmModal.onClose$
      .pipe(
        concatMap((confirm) => {
          if (confirm) {
            // 确认删除，调用接口
            return combineLatest([
              this.systemMemberService.memberControllerDestroy(
                member.id
              ),
              this.translateService.get(msgKeys),
            ]);
          } else {
            return EMPTY;
          }
        })
      )
      .subscribe(
        ([translate]) => {
          // 删除成功
          const [successMsg, failMsg] = msgKeys.map((key) => translate[key]);
          this.msgSrv.success(successMsg);
          this.loadData();
        },
        ([translate]) => {
          // 删除失败
          const [successMsg, failMsg] = msgKeys.map((key) => translate[key]);
          this.msgSrv.warning(failMsg);
          this.loading = false;
        }
      );
  }

  // 打开编辑弹框
  public batchEditMember(member: Member) {
    let ids: any = []
    if (this.selectedElements?.length) {
      ids = this.selectedElements.map(item => item.id)
    }
    if (!ids.length) {
      this.msgSrv.warning(this.translateService.instant('common.editEmptyNote'));
      return;
    }
    let updateModal = this.modalService.open(MemberEditComponent, {
      data: {
        memberIds: ids
      }
    });
    updateModal.onClose$.subscribe((res) => {
      if (res) {
        this.loadData();
      }
    });
  }
  public batchBlindTeam() {
    let ids: any = []
    if (this.selectedElements?.length) {
      ids = this.selectedElements.map(item => item.id)
    }
    if (!ids.length) {
      this.msgSrv.warning(this.translateService.instant('common.editEmptyNote'));
      return;
    }
    let blindModal = this.modalService.open(AddMemberToTeamComponent, {
      data: {
        memberIds: ids
      },
      size: 'small'
    });
     blindModal.onClose$.subscribe((res) => {
      if (res) {
        this.loadData();
      }
    });
  }
}
export interface Member extends SystemMember {
  [key: string]: any;
  selected: boolean;
}
