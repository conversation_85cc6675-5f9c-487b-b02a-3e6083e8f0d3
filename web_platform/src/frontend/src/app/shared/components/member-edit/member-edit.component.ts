import { Component } from '@angular/core';
import { ModalRef } from '@odx/angular/components/modal';
import { UtilService } from '@services/util.service';
import {
  FormGroup,
  FormControl,
  Validators,
} from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';

import { OdxToastCustomService } from '@services/odx-toast-custom.service';
import { selectOption } from '@services/context.service'
import { Member } from '../member-list/member-list.component';
import { IndexQuerySchema, SystemMemberService, SystemMember, SystemTenantAdminComplex } from 'src/app/api/backend';
import { LocalStorageService } from './../../../services/local-storage.service';
import { SettingsService, User } from '@services/settings.service';

@Component({
  selector: 'app-member-edit',
  templateUrl: './member-edit.component.html',
  styleUrls: ['./member-edit.component.scss']
})
export class MemberEditComponent {
  title: string = 'teams.addMember';
  loading: boolean = false;
  memberIds?: number[];
  memberList: Member[] = [];
  currentMember: Member | null = null;
  modalData: any;
  teamOptions: selectOption[] = [];
  tenantId?: number;
  currentUser: User;

  memberForm: FormGroup = new FormGroup({
    name: new FormControl(null, [Validators.required]),
    team_id: new FormControl(null, []),
    phone: new FormControl(null, [Validators.required]),
    email: new FormControl(null, [Validators.required]),
    card_id: new FormControl('', []),
  });
  constructor(
    private modalRef: ModalRef<any, boolean>,
    private _utilService: UtilService,
    private translateService: TranslateService,
    private msgSrv: OdxToastCustomService,
    private systemMemberService: SystemMemberService,
    private settingsService: SettingsService
  ) {
    this.modalData = modalRef.data;
    this.memberIds = this.modalData?.memberIds;
    this.teamOptions = LocalStorageService.getItem('teamOptions');
    this.currentUser = this.settingsService.User;
    this.tenantId = (this.currentUser.accountable as SystemTenantAdminComplex)?.tenant_id as number ?? 1;

    if (this.memberIds?.length) {
      this.title = 'teams.editMember';
      this.getMembersInfo();
    }
  }
  private getMembersInfo(): void {
    if (this.memberIds?.length) {
      let filter = []
      filter.push(`id:in:${this.memberIds.join('|')}`);
      const query: IndexQuerySchema = {
        page: 1,
        size: 99999,
        filter: filter,
        sort: ['id'],
        _with: ['team'],
      }
      this.systemMemberService.memberControllerIndex(query).subscribe(data => {
        this.loading = false;
        this.memberList = data.data.map((item: any) => {
          return {
            ...item,
            selected: false
          }
        })
        console.log( this.memberList)
        this.changeMember(0)
      });
    }
  }
  public changeMember(index: number) {
    this.memberList.forEach(item => {
      item.selected = false
    })
    this.memberList[index].selected = true
    this.currentMember = this.memberList[index];
    this.memberForm.reset({
      name: this.memberList[index].name,
      phone: this.memberList[index].phone,
      team_id: this.memberList[index].team_id,
      email: this.memberList[index].email,
      card_id: this.memberList[index].card_id
    })
  }
  public submit(): void {
    const formValue = this.memberForm.value;
    // let request = [];
    this.memberForm.markAllAsTouched()
    if (!this.memberForm.valid) {
      return;
    }
    this.loading = true;
    let memberInfo: SystemMember = {
        name: formValue.name,
        email: formValue.email,
        phone: formValue.phone,
        tenant_id: this.tenantId,
        team_id: formValue.team_id,
        card_id: formValue.card_id
      }
    if (this.memberIds) {
       this.systemMemberService.memberControllerUpdate(this.currentMember?.id as number, memberInfo).subscribe(
        (_) => {
          this.loading = false;
          this.translateService.get('success.update').subscribe((msg) => {
            this.msgSrv.success(msg);
            this.getMembersInfo();
          });
        },
        (_) => {
          this.loading = false;
          this.translateService.get('failed.update').subscribe((failMsg) => {
            this.msgSrv.warning(failMsg);
          });
        }
      );
    } else {
      this.systemMemberService.memberControllerStore(memberInfo).subscribe(
        (_) => {
          this.loading = false;
          this.translateService.get('success.save').subscribe((msg) => {
            this.msgSrv.success(msg);
            this.modalRef.close(true);
          });
        },
        (_) => {
          this.loading = false;
          this.translateService.get('failed.save').subscribe((failMsg) => {
            this.msgSrv.warning(failMsg);
          });
        }
      );
    }

  }

  closeModalWithCancel () {
    this.modalRef.close(this.memberIds?.length ? true : false);
  }
}
