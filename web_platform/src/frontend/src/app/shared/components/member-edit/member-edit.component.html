<div class="member-edit-container">
  <div class="header-container">
    <odx-modal-header>
      <odx-area-header class="header-title">
        {{ title |translate}}
      </odx-area-header>
    </odx-modal-header>
  </div>
  <div class="modal-content">
    <odx-modal-content>
      <div [ngClass]="{'content-item': memberIds?.length, 'no-member': !memberIds?.length}">
        <div class="left-item" *ngIf="memberIds?.length">
          <p class="title-item">{{'teams.memberList' | translate}}</p>
          <div (click)="changeMember(index)" [ngClass]="{'member-item': true, 'selected-item': item.selected}"
            *ngFor="let item of memberList; let index = index">
            <span>{{ item.name }}</span>
            <app-icon *ngIf="item.selected" type="built-in" name="close"></app-icon>
          </div>
        </div>
        <div class="right-item">
          <p *ngIf="memberIds?.length" class="title-item">{{'teams.memberInfo'|translate}}</p>
          <form class="form-item" [formGroup]="memberForm">
            <odx-form-field odxLayout="12">
              <odx-form-field-label>{{
                "teams.memberName" | translate
                }}</odx-form-field-label>
              <input required formControlName="name" [placeholder]="'app.input-placeholder' | translate"
                odxFormFieldControl [maxlength]="20" />
              <ng-template odxFormFieldError="pattern">
                {{ "report.name.invalid" | translate }}
              </ng-template>
              <ng-template odxFormFieldError="required" let-context>
                {{ "app.login.required.error" | translate }}
              </ng-template>
            </odx-form-field>
            <odx-form-field odxLayout="12">
              <odx-form-field-label>{{
                "teams.team" | translate
                }}</odx-form-field-label>
              <odx-select [placeholder]="'common.selectHolder' | translate" formControlName="team_id"
                odxFormFieldControl>
                <odx-select-option [value]="item.value" *ngFor="let item of teamOptions | odxSelectSearchFilter">
                  {{ item.label }}
                </odx-select-option>
                <input [placeholder]="'teams.team' | translate"
                  [odxSelectSearchField]="'teams.noTeamTypeFond' | translate" />
              </odx-select>
            </odx-form-field>
            <odx-form-field odxLayout="12">
              <odx-form-field-label>{{
                "teams.phone" | translate
                }}</odx-form-field-label>
              <input required formControlName="phone" [placeholder]="'app.input-placeholder' | translate"
                odxFormFieldControl />
              <ng-template odxFormFieldError="required" let-context>
                {{ "app.login.required.error" | translate }}
              </ng-template>
            </odx-form-field>
            <odx-form-field odxLayout="12">
              <odx-form-field-label>{{
                "teams.email" | translate
                }}</odx-form-field-label>
              <input required formControlName="email" [placeholder]="'app.input-placeholder' | translate"
                odxFormFieldControl />
              <ng-template odxFormFieldError="required" let-context>
                {{ "app.login.required.error" | translate }}
              </ng-template>
            </odx-form-field>
            <odx-form-field odxLayout="12">
              <odx-form-field-label>{{
                "teams.memberNFC" | translate
                }}</odx-form-field-label>
              <input formControlName="card_id" [placeholder]="'app.input-placeholder' | translate"
                odxFormFieldControl />
            </odx-form-field>
          </form>
        </div>
      </div>
    </odx-modal-content>
  </div>
  <odx-modal-footer odxLayout="flex " class="justify-end">
    <button odxButton (click)="closeModalWithCancel()">
      {{ "cancel" | translate }}
    </button>
    <button [odxLoadingSpinner]="loading" odxButton variant="primary" (click)="submit()">
      {{ "confirm" | translate }}
    </button>
  </odx-modal-footer>
</div>