import { Component, ViewChild, ElementRef, ChangeDetectorRef } from '@angular/core';
import { ModalRef } from '@odx/angular/components/modal';
import { UtilService } from '@services/util.service';
import { defaultMapConfig } from '@shared';
import {
  FormGroup,
  FormControl,
  Validators,
} from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { forkJoin } from 'rxjs';
import { OdxToastCustomService } from '@services/odx-toast-custom.service';
import { SortStatus, TableHeaderCell } from '@odx/angular/components/table';
import { Member } from '../member-list/member-list.component'
import { SystemTeamService, SystemTeam, TeamControllerUpdateRequest, SystemTenantAdminComplex } from 'src/app/api/backend';
import {
  concatMap,
  combineLatest,
  EMPTY,
} from 'rxjs';
import {SettingsService} from '@services/settings.service';
@Component({
  selector: 'app-team-edit',
  templateUrl: './team-edit.component.html',
  styleUrls: ['./team-edit.component.scss']
})
export class TeamEditComponent {
  public headerData: TableHeaderCell[] = [
    {
      name: 'name',
      title: this.translateService.instant('teams.memberName'),
      sortable: true,
      filter: false,
    },
    {
      name: 'phone',
      title: this.translateService.instant('teams.phone'),
      sortable: true,
      filter: false,
    },
    {
      name: 'email',
      title: this.translateService.instant('teams.email'),
      sortable: true,
      filter: false,
    },
    {
      name: 'action',
      title: this.translateService.instant('app.action'),
      sortable: false,
      filter: false,
    }
  ];
  title: string = 'teams.add';
  subTile: string = 'teams.addSubAccount';
  loading: boolean = false;
  teamId?: number;
  modalData: any;
  members: any[] = [];
  teamForm: FormGroup = new FormGroup({
    name: new FormControl(null, [Validators.required]),
    person_in_charge: new FormControl(null, [Validators.required]),
    phone_number: new FormControl(null, [Validators.required]),
    email: new FormControl(null, [Validators.required]),
    accountName: new FormControl(null, []),
    password: new FormControl(null, []),
    confirmPassword: new FormControl(null, [])
  });
  constructor(
    private modalRef: ModalRef<any, boolean>,
    private utils: UtilService,
    private translateService: TranslateService,
    private msgSrv: OdxToastCustomService,
    private systemTeamService: SystemTeamService,
    private cdRef: ChangeDetectorRef,
    private settingsService: SettingsService
  ) {
    this.modalData = modalRef.data;
    this.teamId = this.modalData?.id;
    if (this.teamId) {
      this.title = 'teams.edit';
      this.subTile = 'teams.editSubAccount';
      this.getTeamInfo();
    }
  }
  private getTeamInfo(): void {
    const team = this.modalData;
    this.teamForm?.patchValue({
      name: team.name,
      person_in_charge: team.person_in_charge,
      phone_number: team.phone_number,
      email: team.email
    });
    this.members = this.modalData.members
    console.log(this.members)
  }
  public submit(): void {
    const formValue = this.teamForm.value;
    let request = [];
    this.teamForm.markAllAsTouched()
    if (!this.teamForm.valid) {
      return;
    }
    this.loading = true;

    if (this.teamId) {
      const payload: TeamControllerUpdateRequest = {
        name: formValue.name,
        person_in_charge: formValue.person_in_charge,
        phone_number: formValue.phone_number,
        email: formValue.email,
        members: []
      };
      this.members.forEach(member => {
        if (member.name && member.email && member.phone) {
          payload.members?.push({
            id: member.id,
            name: member.name,
            phone: member.phone,
            email: member.email
          })
        }
      })
      request.push(
        this.systemTeamService.teamControllerUpdate(
          this.teamId as number,
          payload
        )
      );
    } else {
      const payload: SystemTeam = {
        name: formValue.name,
        person_in_charge: formValue.person_in_charge,
        phone_number: formValue.phone_number,
        email: formValue.email,
        tenant_id: (this.settingsService.User.accountable as SystemTenantAdminComplex)?.tenant_id as number ?? 1
      };
      request.push(
        this.systemTeamService.teamControllerStore(
          payload
        )
      );
    }
    // 保存成功提示
    forkJoin(request).subscribe(
      (_) => {
        this.loading = false;
        this.translateService.get('success.save').subscribe((msg) => {
          this.msgSrv.success(msg);
          this.modalRef.close(true);
        });
      },
      (_) => {
        this.loading = false;
        this.translateService.get('failed.save').subscribe((failMsg) => {
          this.msgSrv.warning(failMsg);
        });
      }
    );
  }
  public sorted(sortParams: SortStatus) {
    console.log(sortParams);

    if (!this.members || this.members.length === 0) {
      return;
    }

    const { column, sortVariant } = sortParams;

    // 对 members 数组进行排序
    this.members = this.members.sort((a, b) => {
      let valueA = a[column];
      let valueB = b[column];

      // 处理空值
      if (valueA == null) valueA = '';
      if (valueB == null) valueB = '';

      // 转换为字符串进行比较（适用于 name, phone, email 等字段）
      valueA = valueA.toString().toLowerCase();
      valueB = valueB.toString().toLowerCase();

      // 根据排序方向进行比较
      if (sortVariant === 'desc') {
        // 降序：B > A
        return valueB.localeCompare(valueA);
      } else {
        // 升序：A > B
        return valueA.localeCompare(valueB);
      }
    });

    console.log('排序后的 members:', this.members);
  }

  public deleteTeamMember(index: Number, member: Member) {
    const confirmModal = this.utils.openDeleteConfirmModal({
      title: 'teams.deleteMember',
      deleteItem: member.name,
    });
    confirmModal.onClose$
    .subscribe(
      (translate) => {
        console.log(index)
        this.members = this.members.filter((item, i) => index !== i)
        this.cdRef.markForCheck();
      },
      (_err) => {

      }
    );
  }

  public addTeamMember() {
    this.members.push({
      id: 0,
      name: '',
      phone: '',
      email: ''
    })
    this.cdRef.markForCheck();
  }
}
