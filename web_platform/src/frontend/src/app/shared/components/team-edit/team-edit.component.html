<div class="team-edit-container">
  <div class="header-container">
    <odx-modal-header>
      <odx-area-header class="header-title">
        {{ title |translate}}
      </odx-area-header>
    </odx-modal-header>
  </div>
  <div [ngClass]="{'edit-content': teamId && members.length, 'modal-content': true}">
    <odx-modal-content>
      <div [ngClass]="{'left-item': teamId && members.length}">
        <form class="form-item" [formGroup]="teamForm">
          <odx-form-field odxLayout="12">
            <odx-form-field-label>{{
              "teams.name" | translate
              }}</odx-form-field-label>
            <input required formControlName="name" [placeholder]="'app.input-placeholder' | translate"
              odxFormFieldControl />
            <ng-template odxFormFieldError="pattern">
              {{ "report.name.invalid" | translate }}
            </ng-template>
            <ng-template odxFormFieldError="required" let-context>
              {{ "app.login.required.error" | translate }}
            </ng-template>
          </odx-form-field>
          <odx-form-field odxLayout="12">
            <odx-form-field-label>{{
              "teams.person" | translate
              }}</odx-form-field-label>
            <input required formControlName="person_in_charge" [placeholder]="'app.input-placeholder' | translate"
              odxFormFieldControl />
            <ng-template odxFormFieldError="required" let-context>
              {{ "app.login.required.error" | translate }}
            </ng-template>
          </odx-form-field>
          <odx-form-field odxLayout="12">
            <odx-form-field-label>{{
              "teams.phone" | translate
              }}</odx-form-field-label>
            <input required formControlName="phone_number" [placeholder]="'app.input-placeholder' | translate"
              odxFormFieldControl [maxlength]="11" />
            <ng-template odxFormFieldError="required" let-context>
              {{ "app.login.required.error" | translate }}
            </ng-template>
          </odx-form-field>
          <odx-form-field odxLayout="12">
            <odx-form-field-label>{{
              "teams.email" | translate
              }}</odx-form-field-label>
            <input required formControlName="email" [placeholder]="'app.input-placeholder' | translate"
              odxFormFieldControl [maxlength]="30" />
            <ng-template odxFormFieldError="required" let-context>
              {{ "app.login.required.error" | translate }}
            </ng-template>
          </odx-form-field>
          <p class="sub-title">{{ subTile | translate }}</p>
          <odx-form-field odxLayout="12">
            <odx-form-field-label>{{
              "teams.accountName" | translate
              }}</odx-form-field-label>
            <input formControlName="accountName" [placeholder]="'app.input-placeholder' | translate"
              odxFormFieldControl [maxlength]="20" />
            <ng-template odxFormFieldError="required" let-context>
              {{ "app.login.required.error" | translate }}
            </ng-template>
          </odx-form-field>
          <odx-form-field odxLayout="12">
            <odx-form-field-label>{{
              "app.login.password.placeholder" | translate
              }}</odx-form-field-label>
            <input formControlName="password" [placeholder]="'app.input-placeholder' | translate"
              odxFormFieldControl type="password" [maxlength]="20" />
            <ng-template odxFormFieldError="required" let-context>
              {{ "app.login.required.error" | translate }}
            </ng-template>
          </odx-form-field>
          <odx-form-field odxLayout="12">
            <odx-form-field-label>{{
              "user.confirmPassword" | translate
              }}</odx-form-field-label>
            <input formControlName="confirmPassword" [placeholder]="'app.input-placeholder' | translate"
              odxFormFieldControl [maxlength]="20" type="password" />
            <ng-template odxFormFieldError="required" let-context>
              {{ "app.login.required.error" | translate }}
            </ng-template>
          </odx-form-field>
        </form>
      </div>
      <div *ngIf="teamId && members.length " class="right-item">
        <div class="member-table">

          <table odxTable [variant]="'default'" [data]="members" [headerData]="headerData" (sorted)="sorted($event)"
            aria-describedby="member-table" [odxLoadingSpinner]="loading">
            <thead aria-hidden="true">
              <th></th>
            </thead>
            <tr odx-table-row *ngFor="let item of members; let index = index">
              <td odx-table-cell>
                <odx-form-field odxLayout="12">
                  <input odxFormFieldControl [(ngModel)]="item.name" [placeholder]="'teams.memberName' | translate" />
                </odx-form-field>
              </td>
              <td odx-table-cell>
                <odx-form-field><input odxFormFieldControl [(ngModel)]="item.phone"
                    [placeholder]="'teams.phone' | translate" />
                </odx-form-field>
              </td>
              <td odx-table-cell>
                <odx-form-field>
                  <input odxFormFieldControl [(ngModel)]="item.email" [placeholder]="'teams.email' | translate" />
                </odx-form-field>
              </td>
              <td odx-table-cell>
                <app-icon (click)="deleteTeamMember(index, item)" class="action-icon delete-icon" name="delete"></app-icon>
              </td>
            </tr>
          </table>
          <p class="add-item" (click)="addTeamMember()"><app-icon class="add-icon" name="plus"></app-icon>
            {{"teams.addNew" | translate}}</p>
        </div>
      </div>
    </odx-modal-content>
  </div>
  <odx-modal-footer odxLayout="flex " class="justify-end">
    <button odxButton odxModalDismiss>
      {{ "cancel" | translate }}
    </button>
    <button [odxLoadingSpinner]="loading" odxButton variant="primary" (click)="submit()">
      {{ "confirm" | translate }}
    </button>
  </odx-modal-footer>
</div>