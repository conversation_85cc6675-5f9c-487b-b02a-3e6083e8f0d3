@import "../../../../styles.scss";


.team-edit-container {
  height: 80vh;
  ::ng-deep .modal-content {
    height: calc(100% - 120px);
  }
  ::ng-deep .odx-modal__content {
    height: 100%;
  }
  .odx-modal__content {
    padding-top: 0;
    overflow: hidden;
    .sub-title {
      font-size: calc($baseFont * 20 / 24);
      font-family: "DraegerPangeaText-SemiBold";
      font-weight: 600;
      margin: calc($marginBase * 24 / 24) 0;
    }

    .left-item {
      width: calc($marginBase * 300 / 24);
      border-right: 1px solid #C7D3E0;
      padding-right: calc($marginBase * 23 / 24);
    }

    .right-item {
      width: calc(100% - $marginBase * 320 / 24);
      height: 100%;;
      overflow-x: scroll;
      .member-table {
        height: calc(100% - $marginBase * 48 / 24);

        .add-item {
          cursor: pointer;
          padding: calc($marginBase * 12 / 24);
          color: #0091F7;
          line-height: calc($baseFont * 24 / 24);
          font-size: calc($baseFont * 12 / 24);

          .add-icon {
            background-color: #0091F7;
            padding: calc($marginBase * 3 / 24);
            margin-right: calc($marginBase * 5 / 24);
          }

          ::ng-deep .odx-icon {
            font-size: calc($baseFont * 18 / 24);
            color: #fff;
          }
        }
      }
    }
  }

  .edit-content {
    .odx-modal__content {
      display: flex;
      justify-content: space-between;
    }
  }
}