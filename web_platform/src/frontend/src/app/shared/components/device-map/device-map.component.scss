@import "../../../../styles.scss";

.map-wrapper {
  width: 100%;
  height: 100%;
  position: relative;

  ::ng-deep {
    .content-window-card {
      .dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
      }
    }
  }

  .header-toggle {
    position: absolute;
    top: calc($marginBase * 30 / 24);
    left: calc($marginBase * 30 / 24);
    font-size: calc($baseFont * 14/ 24);
    font-weight: normal;

    ::ng-deep {
      .odx-toggle-button__indicator {
        background-color: var(--map-button-background-color);
      }

      .odx-toggle-button.is-active {
        .odx-toggle-button__indicator {
          background-color: transparent;
        }
      }
    }
  }

  .footer-toggle {
    position: absolute;
    bottom: calc($marginBase * 15 / 24);
    left: calc(50% - 180px);
    transform: translateX(-25%);
    display: flex;
    align-items: center;
    box-shadow: 0 calc($marginBase * 2 / 24) calc($marginBase * 6 / 24) rgba(0, 2, 5, 0.4);
    border-radius: calc($marginBase * 6 / 24);
    background: var(--Highlight-odx-c-highlight, rgba(0, 145, 247, 1));
    ::ng-deep {
      .odx-button--primary {
        margin: 0;
      }
    }
    .zoom-in {
      height: calc($marginBase * 24 / 24);
    }
    .model-item {
      font-size: calc($baseFont * 16 / 24);
      min-width: calc($marginBase * 140 / 24);
      font-family: "DraegerPangea-Medium";
      font-weight: 500;
      line-height: calc($marginBase * 24 / 24);
      border-left: 1px solid rgba(0, 39, 102, 0.12);
      margin: calc($marginBase * 6 / 24) 0;
      ::ng-deep .odx-icon {
        font-size: calc($baseFont * 20 / 24);
        margin-right: calc($marginBase * 12 / 24);
      }
    }

    .view-item {
      font-size: calc($baseFont * 16 / 24);
      min-width: calc($marginBase * 130 / 24);
      font-family: "DraegerPangea-Medium";
      line-height: calc($marginBase * 24 / 24);
      font-weight: 500;
      margin: calc($marginBase * 6 / 24) 0;
      border-left: 1px solid rgba(0, 39, 102, 0.12);
      ::ng-deep .odx-icon {
        margin-right: calc($marginBase * 12 / 24);
      }
    }

    .button-item {
      color: #fff;
      background: var(--Highlight-odx-c-highlight, rgba(0, 145, 247, 1));
      display: inline-block;
      line-height: calc($marginBase * 24 / 24);
      font-size: calc($baseFont * 16/ 24);
      display: flex;
      align-items: center;
      padding: 0 calc($marginBase * 20 / 24);
      margin: calc($marginBase * 6 / 24) 0;
      font-family: "DraegerPangea-Medium";
      font-weight: 500;
      border-left: 1px solid rgba(0, 39, 102, 0.12);
      ::ng-deep .odx-switch {
        height: calc($marginBase * 24 / 24);
        margin-left: calc($marginBase * 12 / 24);
        .odx-switch__indicator {
          width:  calc(var(--odx-vertical-rythm-base-size) * 1);
        }
        .odx-switch__indicator:before {
          color: #fff;
          background: #fff;
          height: calc($marginBase * 12 / 24);
          width: calc($marginBase * 12 / 24);
          position: absolute;
          top: calc($marginBase * -5 / 24);
          // transform: translate(-50%, -50%);
        }
      }

      ::ng-deep .odx-switch.is-active .odx-icon {
        background: #fff;
        border-color: #fff;
      }
    }

    ::ng-deep {
      .odx-button--primary {
        background: var(--Highlight-odx-c-highlight, rgba(0, 145, 247, 1));
        color: #fff;
      }
    }

    .central {
      margin-left: -3px;
    }
  }

  .dark-footer-toggle {
    ::ng-deep {
      .odx-button--primary {
        // background: #344351;
        color: #D7E3F6;
      }
    }
  }

  ::ng-deep {
    .radar-marker {
      position: relative;
      width: 30px;
      height: 30px;

      .wrapper {
        position: absolute;
        width: calc(100% + 40px);
        height: calc(100% + 40px);
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        z-index: -1;

        .pulse {
          position: absolute;
          width: 100%;
          height: 100%;
          left: 0%;
          top: 0%;
          border: 3px solid #3399ff;
          border-radius: 50%;
          opacity: 0;
        }

        .alarm-pulse {
          border-color: var(--device-alarm-color-even);
        }

        .warning-pulse,
        .fault-pulse {
          border-color: #FFE600;
        }

        .p1 {
          animation: warn 3s ease-out infinite;
        }

        .p2 {
          animation: warn2 3s ease-out infinite;
        }

        .warning-pulse,
        .fault-pulse {
          background-color: #FFFAD7;
        }

        .alarm-pulse {
          background-color: #FFE0E3;
        }

        @keyframes warn {
          0% {
            transform: scale(0.3);
            opacity: 0.0;
          }

          25% {
            transform: scale(0.3);
            opacity: 0.1;
          }

          50% {
            transform: scale(0.5);
            opacity: 0.3;
          }

          75% {
            transform: scale(0.8);
            opacity: 0.5;
          }

          100% {
            transform: scale(1);
            opacity: 0.0;
          }
        }

        @keyframes warn2 {
          0% {
            transform: scale(0.3);
            opacity: 0.0;
          }

          25% {
            transform: scale(0.3);
            opacity: 0.1;
          }

          50% {
            transform: scale(0.3);
            opacity: 0.3;
          }

          75% {
            transform: scale(0.5);
            opacity: 0.5;
          }

          100% {
            transform: scale(0.8);
            opacity: 0.0;
          }
        }
      }
    }

    .big-radar-marker {
      width: 48px;
      height: 48px;
    }
  }
  ::ng-deep {
  
    .marker {
      text-align: center;
      .member-icon-wrapper {
        position: relative;
      }
      .wrapper {
        position: absolute;
        width: calc(100% + 40px);
        height: calc(100% + 40px);
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        z-index: -1;

        .pulse {
          position: absolute;
          width: 100%;
          height: 100%;
          left: 0%;
          top: 0%;
          border: 3px solid #3399ff;
          border-radius: 50%;
          opacity: 0;
        }

        .alarm-pulse {
          border-color: var(--device-alarm-color-even);
        }

        .warning-pulse, .fault-pulse {
          border-color: #FFE600;
        }

        .p1 {
          animation: warn 3s ease-out infinite;
        }

        .p2 {
          animation: warn2 3s ease-out infinite;
        }
        .warning-pulse, .fault-pulse {
          background-color: #FFFAD7;
        }
        .alarm-pulse {
          background-color: #FFE0E3;
        }
        @keyframes warn {
          0% {
            transform: scale(0.3);
            opacity: 0.0;
          }

          25% {
            transform: scale(0.3);
            opacity: 0.1;
          }

          50% {
            transform: scale(0.5);
            opacity: 0.3;
          }

          75% {
            transform: scale(0.8);
            opacity: 0.5;
          }

          100% {
            transform: scale(1);
            opacity: 0.0;
          }
        }

        @keyframes warn2 {
          0% {
            transform: scale(0.3);
            opacity: 0.0;
          }

          25% {
            transform: scale(0.3);
            opacity: 0.1;
          }

          50% {
            transform: scale(0.3);
            opacity: 0.3;
          }

          75% {
            transform: scale(0.5);
            opacity: 0.5;
          }

          100% {
            transform: scale(0.8);
            opacity: 0.0;
          }
        }
      }
      .progress-ring {
        position: relative;
        // width: 50px;
        // height: 50px;
        margin: 0 auto;
        .progress-icon {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
        }
        .progress-captain-icon {
          top: 51%;
          left: 53%;
        }
        svg {
            transform: rotate(-90deg);
        }
        circle {
            fill: none;
            stroke-width: 8;
            stroke-linecap: round;
        }
        .bg {
            stroke: #D9D9D9;
        }
        .bar {
            stroke-dasharray: 125.6;
            stroke-dashoffset: 125.6;
            transition: stroke-dashoffset 0.5s ease, stroke 0.3s;
        }
        .select-bar {
            stroke-dasharray: 150;
            stroke-dashoffset: 150;
        }
      } 
      .marker-name {
        font-size: 13px;
        color: var(--blue-700);
        text-align: center;
        font-weight: bold;
        text-shadow:
          0 0 3px #fff,
          0 0 6px #fff,
          -1px -1px 0 #fff,
          1px -1px 0 #fff,
          -1px 1px 0 #fff,
          1px 1px 0 #fff;
      }
      .marker-device {
        display: flex;
        align-items: center;
        .gateway-item {
          display: flex;
          align-items: center;
          background: #002766;
          border-radius: 12px;
          .device-icon {
            padding: 3px;
          }
        }
        .device_offline {
          background: #D9D9D9;
        }
        .right-item {
          display: flex;
          align-items: center;
          margin-left: 2px;
          background: #D9D9D9;
          border-radius: 10px;
          .device-icon {
            padding: 2px;
            border-radius: 10px;
            background: none;
          }
        }
        .device_alarm, .right-item .device_alarm{
          background: #F30303;
        }
        .device_warning, .right-item .device_warning {
          background: #FFE300;
        }
        .device_offline .device-icon, .right-item .device_offline {
          opacity: 0.6;
        }
      }
    }
  }
  ::ng-deep {
    .station-marker {
      height: 40px;
      width: 40px;
      .marker-name {
        font-size: 13px;
        color: var(--blue-700);
        text-align: center;
        font-weight: bold;
        text-shadow:
          0 0 3px #fff,
          0 0 6px #fff,
          -1px -1px 0 #fff,
          1px -1px 0 #fff,
          -1px 1px 0 #fff,
          1px 1px 0 #fff;
      }
    }
  }
}
 