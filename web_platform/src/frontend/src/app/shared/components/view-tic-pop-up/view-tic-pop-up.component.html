<div class="view-tic-popup-container">
  <div class="header-container">
    <odx-modal-header class="modal-header">
      <odx-area-header class="header-title">
        <span>{{ 'common.viewTic' | translate }}</span>
        <app-icon (click)="miniViewTic()" class="type-icon" type="built-in" iconSet="core" name="minus"></app-icon>
      </odx-area-header>
    </odx-modal-header>
  </div>
  <div class="modal-content">
    <odx-modal-content>
      <div class="video-content">
        <app-video-list [sn]="sn" (selectDeviceChanged)="onUrl($event)" [rightHide]="true"></app-video-list>
      </div>
    </odx-modal-content>
  </div>
</div>