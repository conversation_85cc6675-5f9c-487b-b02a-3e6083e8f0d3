import { Component, OnInit, ChangeDetectorRef, Inject } from '@angular/core';
import { ModalRef } from '@odx/angular/components/modal';
import { TranslateService } from '@ngx-translate/core';
import {
  SystemMemberDataService
} from 'src/app/api/backend';
@Component({
  selector: 'app-view-tic-pop-up',
  templateUrl: './view-tic-pop-up.component.html',
  styleUrls: ['./view-tic-pop-up.component.scss']
})
export class ViewTicPopUpComponent implements OnInit {
  public devices: DeviceData[] = [];
  public activeDevice?: DeviceData;
  public url?: string;
  public loading: boolean = false;
  public failedFlag: boolean = false;
  public desc: string = '';
  public sn: string = '';
  constructor(
    private modalRef: ModalRef<any, { action: 'mini' | 'cancel'; url?: string, sn?: string }>,
    private modalData: ModalRef<{sn: string}, boolean>,
    private translateService: TranslateService,
    private cdRef: ChangeDetectorRef,
    private systemMemberDataService: SystemMemberDataService,
  ) {

  }
  miniViewTic(): void {
    // 返回 action 与当前 url
    this.modalRef.close({ action: 'mini', url: this.url, sn: this.sn });
  }

  closeModal(): void {
    // 关闭并返回取消动作
    this.modalRef.close({ action: 'cancel' });
  }

  onUrl(e: any): void {
    if (e.url && e.sn) {
      this.url = e.url
      this.sn = e.sn
    }
  }
  ngOnInit(): void {
    // 这里可以根据传入的 sn 进行初始化逻辑
    // 例如：根据 sn 请求播放地址后赋值给 this.url
    const data = this.modalData?.data;
    if (data && data.sn) {
      this.sn = data.sn;
    }
  }
}

export interface DeviceData {
  id: number | string,
  name?: string;
  sn?: string,
  value?: number,
  unit?: string,
  unitName?: string,
  selected?: boolean;
  failedFlag?: boolean;
  loading?: boolean;
  desc?: string
}

