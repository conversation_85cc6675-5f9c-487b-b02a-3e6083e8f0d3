import { Component, Input, ChangeDetectorRef, OnChanges, SimpleChanges } from '@angular/core';
import { IndexQuerySchema, SystemTeamService, SystemTeam, SystemTeamResource } from 'src/app/api/backend';
import { fileSaver } from '../../utils/constant';
import { ModalService } from '@odx/angular/components/modal';
import { SortStatus, TableHeaderCell } from '@odx/angular/components/table';
import { TranslateService } from '@ngx-translate/core';
import * as _ from 'lodash';
import { TimeZoneService } from '@services/time-zone.service';
import { OdxToastCustomService } from '@services/odx-toast-custom.service';
import { UtilService } from '@services/util.service';
import {
  concatMap,
  combineLatest,
  EMPTY,
} from 'rxjs';
import { TeamEditComponent } from '../team-edit/team-edit.component';
import { ContextService } from '@services/context.service';

import { Member } from '../member-list/member-list.component'
import { is } from 'date-fns/locale';
@Component({
  selector: 'team-list',
  templateUrl: './team-list.component.html',
  styleUrls: ['./team-list.component.scss']
})

export class TeamListComponent implements OnChanges {
  private sortParams: string = '-id';
  private filterParams!: Record<string, string>;
  sortFields: any = {

  };
  public selectedElements: any[] = [];
  closeDelMsgBoxShow: boolean = false;
  dataToDisplay: any[] = [
    {
      selected: false,
      name: null,
      person: null,
      team: null,
      phone: null,
      email: null,
      create_time: null
    },
  ];
  public headerData: TableHeaderCell[] = [
    { name: 'selected', check: true },
    {
      name: 'name',
      title: this.translateService.instant('teams.name'),
      sortable: true,
      filter: true,
    },
    {
      name: 'person_in_charge',
      title: this.translateService.instant('teams.person'),
      sortable: true,
      filter: true,
    },
    {
      name: 'phone_number',
      title: this.translateService.instant('teams.phone'),
      sortable: true,
      filter: true,
    },
    {
      name: 'email',
      title: this.translateService.instant('teams.email'),
      sortable: true,
      filter: true,
    },
    {
      name: 'created_at',
      title: this.translateService.instant('teams.create_time'),
      sortable: true,
      filter: false,
    },
    {
      name: 'member_count',
      title: this.translateService.instant('teams.memberCount'),
      sortable: true,
      filter: false,
    },
    {
      name: 'action',
      title: this.translateService.instant('app.action'),
      sortable: false,
      filter: false,
    }
  ];

  pageIndex: number = 1;
  pageSize: number = 10;
  filter: string[] = [];
  showAll: boolean = false;
  total!: number
  loading: boolean = false;
  deleteLoading: boolean = false;
  listOfData: Team[] = [];

  @Input() tenantId?: number;
  @Input() refreshTrigger?: any; // 父组件传入的刷新触发器
  @Input() start?: any;
  @Input() end?: any;

  ngOnChanges(changes: SimpleChanges) {
    if (changes['refreshTrigger'] || changes['start'] || changes['end']) {
      this.applyDateFilter();
      this.loadData();
    }
  }

  constructor(
    private modalService: ModalService,
    private translateService: TranslateService,
    private cd: ChangeDetectorRef,
    public timeZoneService: TimeZoneService,
    private msgSrv: OdxToastCustomService,
    private utils: UtilService,
    private systemTeamService: SystemTeamService,
    private contextService: ContextService
  ) {
    this.loadData();
  }
  // 当前页码变化
  public pageIndexChange(index: number): void {
    this.loading = true;
    this.pageIndex = index;
    this.loadData();
  }

  // 每页数据量变化
  public pageSizeChange(size: number): void {
    this.loading = true;
    this.pageIndex = 1;
    this.pageSize = size;
    this.loadData();
  }
  // 获取设备报警列表
  public loadData(): void {
    this.checkAll({ column: 'selected', check: false });
    this.loading = true;
    const query: IndexQuerySchema = {
      page: this.pageIndex,
      size: this.pageSize,
      filter: this.filter,
      sort: [this.sortParams],
      _with: ['members'],
      with_count: ['members']
    }

    this.systemTeamService.teamControllerIndex(query).subscribe(data => {
      this.loading = false;
      this.total = data.meta.total;
      this.listOfData = data.data.map((item: any) => {
        return {
          ...item,
          selected: false,
          showMemberList: false,
          isTeamRow: true,
        }
      })
    });

  }

  private applyDateFilter(): void {
    // 清理旧的 created_at 过滤条件
    this.filter = (this.filter || []).filter(f => !f.startsWith('created_at:'));
    if (this.start && this.end) {
      const startStr = this.formatDate(this.start);
      const endStr = this.formatDate(this.end, true);
      this.filter.push(`created_at:gte:${startStr}`);
      this.filter.push(`created_at:lte:${startStr}`);
      this.pageIndex = 1;
    }
  }

  private formatDate(d: any, endOfDay: boolean = false): string {
    try {
      const date = new Date(d);
      if (endOfDay) {
        date.setHours(23, 59, 59, 999);
      } else {
        date.setHours(0, 0, 0, 0);
      }
      const yyyy = date.getFullYear();
      const mm = String(date.getMonth() + 1).padStart(2, '0');
      const dd = String(date.getDate()).padStart(2, '0');
      const hh = String(date.getHours()).padStart(2, '0');
      const mi = String(date.getMinutes()).padStart(2, '0');
      const ss = String(date.getSeconds()).padStart(2, '0');
      return `${yyyy}-${mm}-${dd} ${hh}:${mi}:${ss}`;
    } catch {
      return '';
    }
  }
  // 全选/全不选
  public checkAll(event: { column: string; check: boolean }): void {
    const { column, check } = event;

    console.log(this.dataToDisplay)
    this.listOfData.forEach((item) => {
      if (column in item)
        (item[column] as boolean) = check;
    });
    if (check) {
      this.closeDelMsgBoxShow = true;
      this.selectedElements = this.listOfData;
    } else {
      this.selectedElements = [];
      this.closeDelMsgBoxShow = false;
    }
  }

  public checkChange(_event: any) {
    setTimeout(() => {
      this.selectedElements = this.listOfData.filter((element) => {
        return element['selected'] === true;
      });
      this.closeDelMsgBoxShow = true;
    }, 5);
  }

  // 关闭 bar
  public closeDelMsgBox() {
    this.closeDelMsgBoxShow = false;
    this.checkAll({
      check: false,
      column: "selected"
    })
  }


  // 排序
  public sorted(sortParams: SortStatus): void {
    if (sortParams && sortParams.column) {
      let field = null;
      if (_.indexOf([], sortParams.column) !== -1) {
        field = this.sortFields[sortParams.column]
      } else {
        field = sortParams.column
      }
      this.sortParams =
        (sortParams.sortVariant == 'asc' ? '' : '-') + field;
      this.sortParams += ',-id'
    } else {
      this.sortParams = '-id';
    }
    this.loadData();
  }

  // 表格头部筛选
  public tableFilter(filterParams: Record<string, string>): void {
    this.filterParams = filterParams;
    this.filterTeam();
  }

  // 条件筛选team
  public filterTeam(event: any = null): void {
    setTimeout(() => {
      const tableFilterValue = this.filterParams;
      this.filter = [];
      if (tableFilterValue) {
        if (tableFilterValue['name']) {
          this.filter.push(`name:like:%${tableFilterValue['name']}%`);
        }
        if (tableFilterValue['person_in_charge']) {
          this.filter.push(`person_in_charge:like:%${tableFilterValue['person_in_charge']}%`);
        }
        if (tableFilterValue['phone_number']) {
          this.filter.push(`phone_number:like:%${tableFilterValue['phone_number']}%`);
        }
        if (tableFilterValue['email']) {
          this.filter.push(`email:like:%${tableFilterValue['email']}%`);
        }
      }
      this.pageIndex = 1;
      this.loadData();
    });
  }

  // 批量删除
  public batchDeleteTeam() {
    let ids: any = []
    if (this.selectedElements?.length) {
      ids = this.selectedElements.map(item => item.id)
    }
    if (!ids.length) {
      this.msgSrv.warning(this.translateService.instant('common.deleteEmptyNote'));
      return;
    }
    const confirmModal = this.utils.openDeleteConfirmModal({
      title: 'teams.delete',
      deleteItem: this.translateService.instant('the selected items'),
    });
    const msgKeys = ['success.delete', 'failed.delete'];
    confirmModal.onClose$
      .pipe(
        concatMap((confirm) => {
          if (confirm) {
            this.deleteLoading = true;
            return combineLatest([
              this.systemTeamService.teamControllerBatchDestroy({ ids: ids}),
              this.translateService.get(msgKeys),
            ]);
          } else {
            return EMPTY;
          }
        })
      )
      .subscribe(
        ([,translate]) => {
          // 删除成功
          const [successMsg, failMsg] = msgKeys.map((key) => translate[key]);
          this.msgSrv.success(successMsg);
          this.loadData();
          this.contextService.getTeamOptions();
          this.deleteLoading = false;
        },
        ([translate]) => {
          // 删除失败
          const [successMsg, failMsg] = msgKeys.map((key) => translate[key]);
          this.msgSrv.warning(failMsg);
          this.deleteLoading = false;
        }
      );
  }
  // 删除team
  public deleteTeam(team: SystemTeamResource): void {
    const confirmModal = this.utils.openDeleteConfirmModal({
      title: 'teams.delete',
      deleteItem: team.name,
    });
    const msgKeys = ['success.delete', 'failed.delete'];
    confirmModal.onClose$
      .pipe(
        concatMap((confirm) => {
          if (confirm) {
            // 确认删除，调用接口
            return combineLatest([
              this.systemTeamService.teamControllerDestroy(
                team.id
              ),
              this.translateService.get(msgKeys),
            ]);
          } else {
            return EMPTY;
          }
        })
      )
      .subscribe(
        ([translate]) => {
          // 删除成功
          const [successMsg, failMsg] = msgKeys.map((key) => translate[key]);
          this.msgSrv.success(successMsg);
          this.loadData();
          this.contextService.getTeamOptions();
        },
        ([translate]) => {
          // 删除失败
          const [successMsg, failMsg] = msgKeys.map((key) => translate[key]);
          this.msgSrv.warning(failMsg);
          this.loading = false;
        }
      );
  }

  // 打开编辑弹框
  public showTeamEdit(team: Team) {
    const updateModal = this.modalService.open(TeamEditComponent, {
      data: team,
      size: team.members.length ? 'large' : 'small'
    });

    updateModal.onClose$.subscribe((res) => {
      if (res) {
        this.loadData();
        this.contextService.getTeamOptions();
      }
    });
  }

  // 切换成员列表显示状态
  public toggleMemberList(team: Team): void {
    team.showMemberList = !team.showMemberList;
    console.log("000000")
    console.log(this.listOfData)
    this.updateDisplayData();
  }

  // 更新显示数据，插入或移除成员列表行
  private updateDisplayData(): void {
    const newDisplayData: Team[] = [];

    this.listOfData.forEach(team => {
      // 添加团队行
      if (team.isTeamRow) {
        newDisplayData.push({
          ...team,
          isTeamRow: true
        });

        // 如果展开，添加成员列表行
        if (team.showMemberList) {
          newDisplayData.push({
            ...team,
            isTeamRow: false,
            showMemberList: false,
            key: `member-list-${team['key']}`,
          });
        }
      }
    });
    console.log(newDisplayData)
    this.listOfData = newDisplayData;
  }
}
export interface Team extends SystemTeam {
  [key: string]: any;
  selected: boolean;
  memberCount: number;
  showMemberList?: boolean; // 控制成员列表显示状态
  isTeamRow: boolean;
  members: any[]
}

