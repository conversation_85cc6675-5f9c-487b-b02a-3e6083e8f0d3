<div class="list">
  <nz-collapse [nzAccordion]="true">
    <nz-collapse-panel *ngFor="let item of items;trackBy: trackItemById" [nzHeader]="nzHeader" [nzExtra]="extraTpl"
      [nzActive]="item.selected" [ngClass]="{'map-item':true,
    'alarm-item':item.dataStatus === 'alarm',
    'warning-item':item.dataStatus==='warning',
    'selected-item': item.selected}" (nzActiveChange)="selectItem(item)">
      <ng-template #nzHeader>
        <div class="map-item-info">
          <p class="item-header">{{item.name}}
            <app-icon *ngIf="item.team_select == false && item.type == 'group'" (click)="hide($event, item)"
              type="built-in" name="view"></app-icon>
            <app-icon *ngIf="item.team_select && item.type == 'group'" (click)="open($event, item)" type="built-in"
              name="view-off"></app-icon>
          </p>
          <p class="text-item">
            <app-icon type="built-in" name="user" size="small"></app-icon>
            <span>{{'common.member' | translate}} {{item.user_count}}</span>
            <app-icon type="built-in" name="bodyguard" size="small" iconSet="safety"></app-icon>
            <span>{{'common.device' | translate}}{{item.device_count}}</span>
          </p>
        </div>
      </ng-template>
      <ng-template #extraTpl>
        <app-icon type="built-in" name="evacuation" size="small" iconSet="safety"></app-icon>
      </ng-template>
      <div class="map-item-content">
        <div *ngFor="let member of item.subItems" (click)="selectMember(member)" [ngClass]="{'member-item': true, 'alarm-member':member.status === 'alarm',
    'warning-member':member.status==='warning', 'selected-member': member.selected}">
          <div>
            <app-icon class="member-icon" *ngIf="member.type == 'captain'" type="svg" name="member_captain"></app-icon>
            <app-icon class="member-icon" *ngIf="member.type == 'member'" type="svg" name="member"></app-icon>
            {{member.name}}
          </div>
          <div>
            <span *ngFor="let device of member.devices">
              <odx-chip [ngClass]="{'device-item': true, 'alarm-item':device.status === 'alarm',
    'warning-item':device.status==='warning', 'offline-item':device.online=== false,}">
                <app-icon class="device-icon" *ngIf="device.type == 'pressure_gauge'" type="built-in" name="scba"
                  iconSet="safety"></app-icon>
                <app-icon class="device-icon" *ngIf="device.type == 'ttt'" type="built-in" name="communication-tool"
                  iconSet="safety"></app-icon>
                <app-icon class="device-icon" *ngIf="device.type == 'gas_detector'" type="built-in" name="xam-2000"
                  iconSet="safety"></app-icon>
                <app-icon class="device-icon" *ngIf="device.type == 'tic'" type="built-in" name="ucf-6000-9000"
                  iconSet="safety"></app-icon>
                <app-icon class="device-icon" *ngIf="device.type == 'smart_base_station'" type="built-in" name="bodyguard"
                  iconSet="safety"></app-icon>
                <span *ngIf="device.status === 'alarm' || device.status === 'warning'"></span>
                <span class="alarm-value-list"
                      [class.single-value]="getAlarmValueCount(device.telemetryList) === 1"
                      *ngIf="device.telemetryList && device.telemetryList.length > 0">
                  <div class="scroll-container">
                    <!-- 第一组数据 -->
                    <ng-container *ngFor="let telemetry of device.telemetryList; trackBy: trackByTelemetryName">
                      <span *ngIf="telemetry.name != 'fallAlarm' && telemetry.name != 'activeAlarm' && telemetry.name != 'pressureAlarm' && telemetry.name != 'batteryAlarm' && telemetry.value && telemetry.status != 'normal'" class="alarm-value">
                          <span class="alarm-value-item" *ngIf="device.type == 'gas_detector'">{{telemetry.name}}</span> {{telemetry.value}}
                          <span class="alarm-value-item" *ngIf="device.type == 'pressure_gauge' || device.type == 'ttt'">{{telemetry.unit}}</span>
                      </span>
                      <span *ngIf="(telemetry.name == 'fallAlarm' || telemetry.name == 'activeAlarm' || telemetry.name == 'pressureAlarm') && telemetry.status === 'alarm' || (telemetry.name == 'batteryAlarm' && telemetry.status === 'warning')" class="alarm-value">
                        <app-icon *ngIf="telemetry.name == 'fallAlarm'"  type="svg" [name]="'fall_alarm_white'"></app-icon>
                        <app-icon *ngIf="telemetry.name == 'activeAlarm'"  type="svg" [name]="'active_alarm_white'"></app-icon>
                        <app-icon *ngIf="telemetry.name == 'pressureAlarm'"  type="svg" [name]="'pressure_alarm'"></app-icon>
                        <app-icon *ngIf="telemetry.name == 'batteryAlarm'"  type="svg" [name]="'battery_alarm'"></app-icon>
                      </span>
                    </ng-container>
                    <!-- 第二组数据，用于无缝循环，只有当报警值数量大于1时才显示 -->
                    <ng-container *ngIf="getAlarmValueCount(device.telemetryList) > 1">
                      <ng-container *ngFor="let telemetry of device.telemetryList; trackBy: trackByTelemetryName">
                        <span *ngIf="telemetry.name != 'fallAlarm' && telemetry.name != 'activeAlarm' && telemetry.name != 'pressureAlarm' && telemetry.name != 'batteryAlarm' && telemetry.value && telemetry.status != 'normal'" class="alarm-value">
                            <span *ngIf="device.type == 'gas_detector'">{{telemetry.name}}</span> {{telemetry.value}}
                            <span *ngIf="device.type == 'pressure_gauge' || device.type == 'ttt'">{{telemetry.unit}}</span>
                        </span>
                        <span *ngIf="(telemetry.name == 'fallAlarm' || telemetry.name == 'activeAlarm' || telemetry.name == 'pressureAlarm') && telemetry.status === 'alarm' || (telemetry.name == 'batteryAlarm' && telemetry.status === 'warning')" class="alarm-value">
                          <app-icon *ngIf="telemetry.name == 'fallAlarm'"  type="svg" [name]="'fall_alarm_white'"></app-icon>
                          <app-icon *ngIf="telemetry.name == 'activeAlarm'"  type="svg" [name]="'active_alarm_white'"></app-icon>
                          <app-icon *ngIf="telemetry.name == 'pressureAlarm'"  type="svg" [name]="'pressure_alarm'"></app-icon>
                          <app-icon *ngIf="telemetry.name == 'batteryAlarm'"  type="svg" [name]="'battery_alarm'"></app-icon>
                        </span>
                      </ng-container>
                    </ng-container>
                  </div>
                </span>
              </odx-chip>
            </span>
          </div>
        </div>
      </div>
    </nz-collapse-panel>
  </nz-collapse>
</div>