import { AbstractControl, FormControl } from '@angular/forms';
import FileSaver from 'file-saver';
export const phonePattern =
  /^(13[0-9]|18[0-9]|14[012345679]|15[0-35-9]|16[2567]|17[0-8]|19[0-35-9])\d{8}$/;
export const emailPattern =
  /^[A-Za-z0-9]+([_\.][A-Za-z0-9]+)*@([A-Za-z0-9\-]+\.)+[A-Za-z]{2,6}$/;
export const pinCodePattern =
  /^[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}$/;
export const passwordPattern = /^.{3,10}$/;
export const noSpecialCharacters = '[_a-zA-Z0-9\u4e00-\u9fa5 ]*';
export const macPatter = /^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$|^([0-9A-Fa-f]{4}\.){2}([0-9A-Fa-f]{4})$ /;
export function transformFormControl(control: AbstractControl): FormControl {
  return control as FormControl;
}
// 格式化间隔或者计时
export function formatDuration(duration: number): string {
  const days = parseInt(`${duration / (60 * 60 * 24)}`);
  const hours = parseInt(`${(duration % (60 * 60 * 24)) / (60 * 60)}`);
  const minutes = parseInt(`${(duration % (60 * 60)) / 60}`);
  const seconds = duration % 60;
  let time = '';
  if (days) {
    time = `${days}D ${hours}H ${minutes}M`;
  } else {
    if (hours) {
      time = `${hours}:${minutes}`;
    } else if (minutes) {
      time = `${minutes > 9 ? minutes : `0${minutes}`}${
        seconds > 9 ? `:${seconds}` : `:0${seconds}`
      }`;
    } else {
      time = `00:${seconds > 9 ? `${seconds}` : `0${seconds}`}`;
    }
  }
  return time;
}
// 导出并保存文件
export function fileSaver(fileData: Blob, fileName?: string) {
  return FileSaver.saveAs(fileData, fileName);
}

export const defaultMapConfig = {
  lng:116.545436,
  lat:40.099459,
  zoom:8,
  zooms:[3, 19],
  provinceZoom:7,
  cityZoom:10,
  streetZoom:14,
  showCenterZoom:19
}