import { Inject, Injectable, Injector } from '@angular/core';
import { Router } from '@angular/router';
// import { LoadingService } from '@delon/abc/loading';
import { ACLService } from '@delon/acl';
import { DA_SERVICE_TOKEN, ITokenService, } from '@delon/auth';
import { SettingsService } from './settings.service';
import { BehaviorSubject, of, Observable, forkJoin } from 'rxjs';
import { tap, catchError, map, switchMap, distinctUntilChanged, share, } from 'rxjs/operators';
import { environment } from 'src/environments/environment';
import { ContextService } from '@services/context.service';
import { Configuration } from '../api/backend';
import { SystemAuthService, SystemSettingService } from "../api/backend";
import { SystemLoginResponse, SystemMeResponse, SystemTenantWithResourcePolicy, SystemTenantAdminComplex } from "../api/backend";
import { LocalStorageService } from './local-storage.service';
import * as _ from 'lodash';
import  moment from 'moment';
import { UtilService } from './util.service';
@Injectable({
  providedIn: 'root'
})
export class CurrentUserService {
  // Set apiModule config
  private static apiConfiguration: Configuration = new Configuration({
    basePath: environment['SERVER_URL'],
    accessToken: LocalStorageService.getItem('tokenPack', { access_token: null }).access_token
  });
  currentAccount: BehaviorSubject<SystemMeResponse | null> = new BehaviorSubject<SystemMeResponse | null>(null);
  currentTenant: BehaviorSubject<SystemTenantWithResourcePolicy | null> = new BehaviorSubject<SystemTenantWithResourcePolicy | null>(null);

  private get contextService() {
    return this.injector.get(ContextService);
  }

  constructor(
    private router: Router,
    // private loadingService: LoadingService,
    private settings: SettingsService,
    private injector: Injector,
    private aclService: ACLService,
    private systemAuthService: SystemAuthService,
    private systemSettingService: SystemSettingService,
    @Inject(DA_SERVICE_TOKEN) private tokenService: ITokenService,
    private utilService: UtilService,
  ) {
    // 当前登录用户变化，加载租户列表，用户角色等
    this.currentAccount.pipe(
      switchMap(account => {
        const tenant = (account?.accountable as SystemTenantAdminComplex)?.tenant;
        const permissionNames = account?.permission_names;
        if (this.isTenantAdmin() && tenant && permissionNames && permissionNames.length > 0) {
          const tenantId = tenant.id;
          return forkJoin([
            this.contextService.reloadTenantRoles(tenantId),
            this.contextService.reloadTenants(),
          ]).pipe(
            map(([roles, tenants]) => tenants.find(item => item.id === tenantId) || null),
          );
        }
        return of(null);
      }),
      tap(tenant => {
        // 获取当前Tenant
        this.currentTenant.next(tenant)
      }),
      switchMap(() => {
        if (this.isSuperAdmin() || this.isPlatformAdmin()) {
          return this.contextService.reloadPlatformRoles();
        }
        return of(null);
      }),
    ).subscribe();
  }
  // Save the SystemLoginResponse info
  tokenPack: TokenPackData = {
    access_token: '',
    token_type: '',
    expires_datetime: 0,
    expires_in: 0
  };
  // ApiMOdule Config
  static getApiModuleConfiguration(): Configuration {
    return CurrentUserService.apiConfiguration;
  }
  // 获取个人信息
  loadMe() {
    return this.systemAuthService.authControllerMe().pipe(
      catchError(() => {
        console.log('logout catch error');
        // 获取失败，跳转登录
        this.router.navigateByUrl('/passport/login').finally();
        return of(null);
      }),
      // 成功，更新当前的账户信息
      tap(result => result && this.updateCurrentAccount(result.data))
    );
  }
  // 获取当前账户信息
  updateCurrentAccount(value: SystemMeResponse) {
    if (!value) {
      this.injector.get(Router).navigateByUrl('/passport/login');
      return;
    }
    // 处理用户信息及权限，保存到设置
    const currentName = value.accountable ? value.accountable.name : value.login_name;
    const userInfo = {
      ...value,
      name: currentName || '',
      avatar_url: value.avatar?.url,
      email: value.accountable?.email,
      company: (value.accountable as any)?.tenant.name,
      roleZhName: []
    };
    this.settings.setUser(userInfo);
    this.updateAcl(userInfo as UserInfo);
    this.currentAccount.next(value);
    setTimeout(() => {
      this.contextService.getTeamOptions();
      this.contextService.getDeviceTypes();
    }, 500);
  }

  // 更新当前用户权限服务
  updateAcl(userInfo: UserInfo) {
    this.aclService.setRole(userInfo.role_names || ['']);
    if (userInfo.role_names?.includes('super_admin') || userInfo.role_names?.includes('default_top_head_admin')) {
      this.aclService.setFull(true);
    } else {
      this.aclService.setFull(false);
      let permissions;
      permissions = userInfo.permission_names || [];
      this.aclService.setAbility(permissions);
    }
  }
  //  更新token管理信息
  updateTokenPack(value: SystemLoginResponse, keepMe = true) {
    this.tokenPack = {
      ...value
    };
    this.tokenPack.expires_datetime = new Date().getTime() + value.expires_in * 1000;
    CurrentUserService.apiConfiguration.accessToken = this.tokenPack.access_token;
    const token = {
      token: `Bearer ${this.tokenPack.access_token}`,
      time: +new Date(),
      token_expires_datetime: this.tokenPack.expires_datetime
    };
    if (keepMe) {
      LocalStorageService.setItem('tokenPack', this.tokenPack);
    } else {
      LocalStorageService.removeItem('tokenPack');
    }
    this.tokenService.set(token);
  }
  stackCurrentTokenPack() {
    const tokenPack = LocalStorageService.getItem('tokenPack');
    LocalStorageService.setItem('stackedTokenPack', tokenPack);
  }
  hasStackedTokenPack(): boolean {
    return !!LocalStorageService.getItem('stackedTokenPack')
  }
  /**
   * Restore 'stackedTokenPack' from localstorage back to this.tokenPack
   */
  async restoreStackedTokenPack() {
    const stackedTokenPack: TokenPackData | null = _.cloneDeep(LocalStorageService.getItem('stackedTokenPack'));
    if (stackedTokenPack) {
      this.tokenPack = stackedTokenPack;
      CurrentUserService.apiConfiguration.accessToken = this.tokenPack.access_token;
      const token = {
        token: `Bearer ${this.tokenPack.access_token}`,
        time: +new Date(),
        token_expires_datetime: this.tokenPack.expires_datetime
      };
      LocalStorageService.removeItem('stackedTokenPack');
      LocalStorageService.setItem('tokenPack', this.tokenPack);
      await this.tokenService.set(token);
      return true;
    } else {
      return false;
    }

  }

  /**
   *  判断token是否预期
   *
   * @param offsetSeconds
   * @return boolean true表示过期 false 没有过期
   */
  isTokenExpired(offsetSeconds = 0): boolean {
    const expires_datetime = this.tokenService.get()?.['token_expires_datetime'];
    if (!expires_datetime) {
      return true;
    }
    return new Date().getTime() > expires_datetime - offsetSeconds * 1000;
  }
  // 刷新token
  refreshToken() {
    return this.systemAuthService.authControllerRefresh().pipe(
      tap(result => {
        // console.log(result, 'refresh token when token is expired');
        this.updateTokenPack(result, true);
      })
    );
  }
  // 用户登出
  logout(): Observable<any> {
    if (!this.currentAccount.getValue()) {
      return this.afterLogout();
    }
    return this.systemAuthService.authControllerLogout().pipe(
      tap(result => {
        this.afterLogout();
      }),
      catchError(err => {
        return this.afterLogout();
      })
    )
  }
// 登出成功之后，删除token，重置用户信息
  afterLogout() {
    LocalStorageService.removeItem('tokenPack');
    this.tokenPack = { access_token: '', expires_in: 0, token_type: '' };
    this.currentAccount.next({});
    this.tokenService.clear();
    this.settings.setUser({});
    return of(true)
  }

  /**
   *
   * @param roles Roles you are looking for
   * @param roleNames Roles you want to search from. Keep it null to search from current account role_names.
   */
  hasAnyRoles(roles: string[], roleNames: string[] | null = null): boolean {
    if (roleNames !== null) {
      return !!roleNames.find(role => roles.includes(role));
    }
    return !!(this.currentAccount.value && this.currentAccount.value.role_names?.find(role => roles.includes(role)));
  }

  isPlatformAdmin(): boolean {
    return !!this.currentAccount.value && this.currentAccount.value.accountable_type === 'PlatformAdmin';
  }

  isSuperAdmin(): boolean {
    return this.hasAnyRoles(['super_admin']);
  }

  isTopTenantAdmin(): boolean {
    const { accountable }: any = this.currentAccount.value;
    return accountable ? accountable.tenant?.parent_tenant_id === 0 : false;
  }

  isSubTenantAdmin(): boolean {
    const { accountable }: any = this.currentAccount.value;
    return accountable ? accountable.tenant?.parent_tenant_id > 0 : false;
  }

  isTenantAdmin(): boolean {
    return !!this.currentAccount.value && this.currentAccount.value.accountable_type === 'TenantAdmin';
  }
  /**
   *
   * @returns returns an Observable that emits items from the source Observable with distinct values.
   */
  currentTenantChangedShare() {
    return this.currentTenant.pipe(
      distinctUntilChanged((prev, curr) => prev?.id === curr?.id),
      share()
    );
  }
}

interface TokenPackData extends SystemLoginResponse {
  expires_datetime?: number; // expire timestamp
}
export interface UserInfo extends SystemMeResponse {
  name?: string;
  roleZhName?: unknown[];
}
