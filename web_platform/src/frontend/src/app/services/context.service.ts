import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { tap, map } from 'rxjs/operators';
import { UtilService, Tree } from './util.service';
import { LocalStorageService } from './local-storage.service';
import {
  SystemPermissionService,
  SystemRole,
  SystemRoleResource,
  SystemRoleService,
  IndexQuerySchema,
  SystemTenantService,
  SystemTenantWithResourcePolicy,
  SystemDeviceMoldResource,
  SystemDeviceTypeResource,
  SystemDeviceTypeService,
  SystemTeamService,
  SystemDeviceMoldService
} from '../api/backend';
import { SettingsService } from './settings.service';

@Injectable({
  providedIn: 'root'
})
export class ContextService {
  platformRoles: BehaviorSubject<SystemRole[]> = new BehaviorSubject([] as SystemRole[]);
  tenantRoles$: BehaviorSubject<SystemRoleResource[]> = new BehaviorSubject<SystemRoleResource[]>([]);
  tenantTree$: BehaviorSubject<Tree<SystemTenantWithResourcePolicy>[]> = new BehaviorSubject<Tree<SystemTenantWithResourcePolicy>[]>([]);
  permissionData: BehaviorSubject<any> = new BehaviorSubject({});
  tenantPermissionData: BehaviorSubject<any> = new BehaviorSubject({});
  moldItem$: BehaviorSubject<SystemDeviceMoldResource> = new BehaviorSubject({} as SystemDeviceMoldResource);
  constructor(
    private systemRoleService: SystemRoleService,
    private systemTenantService: SystemTenantService,
    private systemPermissionService: SystemPermissionService,
    private systemDeviceTypeService: SystemDeviceTypeService,
    private settings: SettingsService,
    private systemTeamService: SystemTeamService,
    private systemDeviceMoldService: SystemDeviceMoldService

  ) {

  }
  // 获取platform用户角色
  reloadPlatformRoles(query = null) {
    let indexQuery: IndexQuerySchema;
    if (!query) {
      indexQuery = {
        _with: ['permissions'],
        size: 9999
      };
    } else {
      indexQuery = query;
    }
    return this.systemRoleService.roleControllerIndex(indexQuery).pipe(
      tap(result => {
        const roles = result.data.filter(item => item.name !== 'super_admin' && item.name !== 'default_platform_admin');
        this.platformRoles.next(roles);
      })
    );
  }

  //获取平台权限列表
  getSystemPermission() {
    return this.systemPermissionService.permissionControllerPlatform().pipe(
      tap(res => {
        const permission = res.data ? res.data : null;
        const allPermission = {};
        permission?.forEach(permission => {
          if (permission.name) {
            Object.assign(allPermission, { [permission.name]: permission.id })
          }
        });
        this.permissionData.next(allPermission);
      })
    );
  }

  //获取租户（Tenant）权限列表
  getTenantPermission() {
    return this.systemPermissionService.permissionControllerTenant().pipe(
      tap(res => {
        const permission = res.data ? res.data : null;
        const allPermission = {};
        permission?.forEach(permission => {
          if (permission.name) {
            Object.assign(allPermission, { [permission.name]: permission.id })
          }
        });
        this.tenantPermissionData.next(allPermission);
      })
    );
  }
  // 获取操作管理员角色列表
  reloadTenantRoles(tenantId: number) {
    const indexQuery: IndexQuerySchema = {
      _with: ['permissions'],
      size: 9999
    };
    return this.systemTenantService.tenantControllerIndexTenantRole(tenantId, indexQuery).pipe(
      tap(result => {
        this.tenantRoles$.next(result.data || []);
      })
    );
  }
  // 获取租户列表信息（树形结构）
  reloadTenants(): Observable<SystemTenantWithResourcePolicy[]> {
    return this.systemTenantService.tenantControllerResourcePolicy().pipe(
      tap(result => {
        const tempData: SystemTenantWithResourcePolicy[] = JSON.parse(JSON.stringify(result.data));
        const tenantTree = UtilService.formatFlatArrayToTree(tempData);
        this.tenantTree$.next(tenantTree || []);
      }), map(res => {
        return res.data;
      })
    );
  }
  setDeviceMoldItem(deviceMold: SystemDeviceMoldResource) {
    LocalStorageService.setItem('deviceMold', deviceMold);
    this.moldItem$.next(deviceMold);
  }
  private allDeviceTypesResource = new BehaviorSubject<SystemDeviceTypeResource[]>([]);
  allDeviceTypesResult$ = this.allDeviceTypesResource.asObservable();
  private emitAllDeviceTypes(result: SystemDeviceTypeResource[]) {
    this.allDeviceTypesResource.next(result);
  }
  /**
   *
   * @param id
   * @param observe
   * @param reportProgress
   * @param options
   */
  public getAllTypes() {
    const indexQuery = {
      size: 9999
    };
    return this.systemDeviceTypeService.deviceTypeControllerIndex(indexQuery).pipe(
      tap(result => {
        const allTypes = result.data;
        this.emitAllDeviceTypes(allTypes);
      })
    );
  }

  public getDeviceTypes () {
    const indexQuery = {
      size: 9999
    };
    this.systemDeviceMoldService.deviceMoldControllerIndex(indexQuery).subscribe(result => {
      if (result.data) {
        let deviceMolds: selectOption[] = [];
        result.data.forEach(item => {
          deviceMolds.push({
            value: item.id,
            label: item.model_code
          })
        })
        LocalStorageService.setItem('deviceMolds', deviceMolds);
      }
    })
  }

  public getTeamOptions() {
    const indexQuery = {
      size: 9999
    };

    // let tenantId = (this.settings.User.accountable as any)?.tenant_id;
    // if (tenantId) {
    this.systemTeamService.teamControllerIndex(indexQuery)
      .subscribe(
        (result) => {
          let teamOptions: selectOption[] = [];
          result.data.forEach(item => {
            teamOptions.push({
              value: item.id,
              label: item.name
            })
          })
          LocalStorageService.setItem('teamOptions', teamOptions);
        }
      );
    //}
  }
}
export interface PermissionsGroup {
  permissions: Permission[]
}
export interface Permission {
  value: number;
  checked: boolean;
  label: string;
  name: string;
}

export interface selectOption {
  value: number | string,
  label: string | undefined,
  disabled?: boolean | undefined
}

