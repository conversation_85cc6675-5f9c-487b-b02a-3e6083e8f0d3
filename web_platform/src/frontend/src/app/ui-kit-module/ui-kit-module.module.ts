import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CoreModule } from '@odx/angular';
import { HeaderModule } from '@odx/angular/components/header';
import { AvatarComponent } from '@odx/angular/components/avatar';
import { MenuModule } from '@odx/angular/components/menu';
import { DropdownModule } from '@odx/angular/components/dropdown';
// 加载状态 loading组件
import { LoadingSpinnerModule } from '@odx/angular/components/loading-spinner';
// odx 内容组件
import { ContentBoxComponent, ContentBoxFooterDirective, ContentBoxHeaderDirective } from '@odx/angular/components/content-box';
// 弹窗组件
import { ModalModule } from '@odx/angular/components/modal';
// tooltip组件
import { TooltipDirective } from '@odx/angular/components/tooltip';
// select 下拉选择组件
import { SelectModule } from '@odx/angular/components/select';
// odx form field 组件
import { FormFieldModule } from '@odx/angular/components/form-field';
// 按钮组合切换
import { ToggleButtonGroupModule } from '@odx/angular/components/toggle-button-group';
// switch 组件
import { SwitchModule } from '@odx/angular/components/switch';
// search 组件
import { SearchBarModule } from '@odx/angular/components/search-bar';
// chip 组件
import { ChipComponent }  from '@odx/angular/components/chip';
// tab 组件
import { TabBarModule } from '@odx/angular/components/tab-bar';
// 折叠组件
import { AccordionModule } from '@odx/angular/components/accordion'
// 时间筛选
import { DatepickerModule }  from '@odx/angular/components/datepicker'

import { PageChangeEvent, PaginatorModule } from '@odx/angular/components/paginator';
import { SortStatus, TableComponent, TableHeaderCell, TableModule, TableSortVariant, TableVariant } from '@odx/angular/components/table';
import { ToastModule } from '@odx/angular/components/toast';
@NgModule({
  declarations: [],
  imports: [
    CommonModule,
    CoreModule,
    HeaderModule,
    AvatarComponent,
    DropdownModule,
    LoadingSpinnerModule,
    ContentBoxComponent,
    ContentBoxFooterDirective,
    ContentBoxHeaderDirective,
    MenuModule,
    ModalModule,
    TooltipDirective,
    SelectModule,
    FormFieldModule,
    ToggleButtonGroupModule,
    SwitchModule,
    PaginatorModule,
    TableComponent,
    TableModule,
    SearchBarModule,
    ChipComponent,
    TabBarModule,
    AccordionModule,
    DatepickerModule,
    ToastModule
  ],
  exports: [
    HeaderModule,
    AvatarComponent,
    DropdownModule,
    LoadingSpinnerModule,
    ContentBoxComponent,
    ContentBoxFooterDirective,
    ContentBoxHeaderDirective,
    MenuModule,
    ModalModule,
    TooltipDirective,
    SelectModule,
    FormFieldModule,
    ToggleButtonGroupModule,
    SwitchModule,
    PaginatorModule,
    TableComponent,
    TableModule,
    SearchBarModule,
    ChipComponent,
    TabBarModule,
    AccordionModule,
    DatepickerModule,
    ToastModule
  ]
})
export class UiKitModuleModule { }
