/**
 * IoT Platform Backend API
 *
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


/**
 * !!! When with relationships, use camelCase, NOT snake_case!!!
 */
export interface IndexQuerySchema { 
    /**
     * Global search term. Use `%` to wildcard search.
     */
    search?: string;
    /**
     * Supports two filter formats:<br>  *      1. Array format: `filter[]=field:operator:value` (multiple parameters)<br>  *      2. Comma-separated format: `filter=field1:operator:value1,field2:operator:value2` (single parameter)<br>  *      Available operators: `gt` `gte` `lt` `lte` `eq` `ne` `neq` `like` `in` `nin` `null` `not-null` `true` `false`<br>  *      Examples:<br>  *      - Array: `filter[]=age:gt:18&filter[]=status:in:pending|active`<br>  *      - Comma-separated: `filter=age:gt:18,status:in:pending|active,created_at:gte:2025-09-11 16:00:00,created_at:lte:2025-09-19 16:00:00`
     */
    filter?: Array<string>;
    /**
     * Sorting fields. Leading minus as DESC sorting.
     */
    sort?: Array<string>;
    /**
     * Get relationships.
     */
    _with?: Array<string>;
    /**
     * Get the count number of relationships.
     */
    with_count?: Array<string>;
    /**
     * Page number. Start from 1.
     */
    page?: number;
    /**
     * Items per page. 
     */
    size?: number;
}

