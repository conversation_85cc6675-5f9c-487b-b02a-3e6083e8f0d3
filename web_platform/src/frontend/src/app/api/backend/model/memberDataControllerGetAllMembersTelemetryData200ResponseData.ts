/**
 * IoT Platform Backend API
 *
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { MemberDataControllerGetAllMembersTelemetryData200ResponseDataMembersInner } from './memberDataControllerGetAllMembersTelemetryData200ResponseDataMembersInner';
import { MemberDataControllerGetAllMembersTelemetryData200ResponseDataTimeRange } from './memberDataControllerGetAllMembersTelemetryData200ResponseDataTimeRange';


export interface MemberDataControllerGetAllMembersTelemetryData200ResponseData { 
    /**
     * Number of members with data in time range
     */
    members_count?: number;
    /**
     * Total number of members in tenant
     */
    total_members_in_tenant?: number;
    time_range?: MemberDataControllerGetAllMembersTelemetryData200ResponseDataTimeRange;
    members?: Array<MemberDataControllerGetAllMembersTelemetryData200ResponseDataMembersInner>;
}

