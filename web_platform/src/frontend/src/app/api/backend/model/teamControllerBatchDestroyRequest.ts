/**
 * IoT Platform Backend API
 *
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


export interface TeamControllerBatchDestroyRequest { 
    /**
     * Array of Team IDs to delete
     */
    ids: Array<number>;
    /**
     * Force delete teams with associated members (will remove member associations)
     */
    force?: boolean;
}

