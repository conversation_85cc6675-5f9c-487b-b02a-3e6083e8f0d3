/**
 * IoT Platform Backend API
 *
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { TeamControllerUpdateRequestAllOfMembersInner } from './teamControllerUpdateRequestAllOfMembersInner';


export interface TeamControllerUpdateRequest { 
    /**
     * The ID.
     */
    id?: number;
    /**
     * Team name
     */
    name?: string;
    /**
     * Person in charge of the team
     */
    person_in_charge?: string;
    /**
     * Contact phone number
     */
    phone_number?: string;
    /**
     * Contact email address
     */
    email?: string;
    /**
     * Status: 1=active, 0=inactive
     */
    status?: number;
    /**
     * Team description
     */
    description?: string;
    /**
     * Additional metadata
     */
    metadata?: object;
    /**
     * The foreign key to a tenant
     */
    tenant_id?: number;
    /**
     * The datetime when this record is created.
     */
    created_at?: string;
    /**
     * The datetime when this record is updated. Null represents not updated.
     */
    updated_at?: string;
    /**
     * The datetime when this record is deleted. Null represents not deleted.
     */
    deleted_at?: string;
    /**
     * Complete replacement mode: Array of member objects to create/update/associate with this team. Only members in this array will remain associated with the team. Members not in this array will have their team association removed. Pass empty array [] to remove all member associations. Omit this field to leave member associations unchanged.
     */
    members?: Array<TeamControllerUpdateRequestAllOfMembersInner>;
    /**
     * Legacy support: Simple array of member IDs to associate with this team. Uses complete replacement mode - only these members will remain associated. Cannot be used together with \'members\' array.
     */
    member_ids?: Array<number>;
}

