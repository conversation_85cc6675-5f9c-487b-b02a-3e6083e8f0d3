/**
 * IoT Platform Backend API
 *
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


export interface TeamControllerUpdateRequestAllOfMembersInner { 
    /**
     * Member ID for updating existing member. Omit for creating new member.
     */
    id?: number;
    /**
     * Card ID
     */
    card_id?: string;
    /**
     * Member name (required for new members)
     */
    name?: string;
    /**
     * Phone number
     */
    phone?: string;
    /**
     * Email address
     */
    email?: string;
    /**
     * Department
     */
    department?: string;
    /**
     * Job position
     */
    position?: string;
    /**
     * Status: 1=active, 0=inactive
     */
    status?: TeamControllerUpdateRequestAllOfMembersInner.StatusEnum;
    /**
     * Additional metadata
     */
    metadata?: object;
}
export namespace TeamControllerUpdateRequestAllOfMembersInner {
    export const StatusEnum = {
        NUMBER_0: 0,
        NUMBER_1: 1
    } as const;
    export type StatusEnum = typeof StatusEnum[keyof typeof StatusEnum];
}


