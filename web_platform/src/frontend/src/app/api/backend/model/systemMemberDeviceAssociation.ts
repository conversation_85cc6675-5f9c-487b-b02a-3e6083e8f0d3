/**
 * IoT Platform Backend API
 *
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


/**
 * Member Device Association model for tracking device-member relationships.
 */
export interface SystemMemberDeviceAssociation { 
    /**
     * The ID.
     */
    id?: number;
    /**
     * Member ID from data stream
     */
    member_id?: string;
    /**
     * Device identifier from data stream
     */
    device_identifier?: string;
    /**
     * Device type: wristband, pressure_gauge, gas_detector, tic, etc.
     */
    device_type?: string;
    /**
     * Gateway device identifier
     */
    gateway_device_identifier?: string;
    /**
     * When this association started
     */
    association_start?: string;
    /**
     * When this association ended
     */
    association_end?: string;
    /**
     * Whether this association is currently active
     */
    is_active?: boolean;
    /**
     * Device metadata from data stream
     */
    device_metadata?: object;
    /**
     * The datetime when this record is created.
     */
    created_at?: string;
    /**
     * The datetime when this record is updated. Null represents not updated.
     */
    updated_at?: string;
}

