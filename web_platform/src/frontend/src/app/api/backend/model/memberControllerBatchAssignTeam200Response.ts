/**
 * IoT Platform Backend API
 *
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { MemberControllerBatchAssignTeam200ResponseData } from './memberControllerBatchAssignTeam200ResponseData';


export interface MemberControllerBatchAssignTeam200Response { 
    success?: boolean;
    data?: MemberControllerBatchAssignTeam200ResponseData;
    message?: string;
}

