/**
 * IoT Platform Backend API
 *
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { MemberDataControllerGetMemberTelemetryData200ResponseQueryInfoTimeRange } from './memberDataControllerGetMemberTelemetryData200ResponseQueryInfoTimeRange';


export interface MemberDataControllerGetAllMembersTelemetryData200ResponseQueryInfo { 
    /**
     * \'all_members\' or \'specific_members\'
     */
    query_type?: string;
    tenant_id?: number;
    /**
     * Array of member IDs used in filter (null if all members)
     */
    member_ids?: Array<number> | null;
    /**
     * Whether only latest data was requested
     */
    latest_only?: boolean;
    time_range?: MemberDataControllerGetMemberTelemetryData200ResponseQueryInfoTimeRange;
}

