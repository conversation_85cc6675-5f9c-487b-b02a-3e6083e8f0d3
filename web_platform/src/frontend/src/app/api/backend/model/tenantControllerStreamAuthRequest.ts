/**
 * IoT Platform Backend API
 *
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


export interface TenantControllerStreamAuthRequest { 
    /**
     * Application name
     */
    app: string;
    /**
     * Stream name (device identifier)
     */
    stream: string;
    /**
     * URL parameters containing authentication info
     */
    param: string;
}

