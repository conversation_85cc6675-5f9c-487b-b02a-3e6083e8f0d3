/**
 * IoT Platform Backend API
 *
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


/**
 * Time range (only present when latest_only=false)
 */
export interface MemberDataControllerGetMemberTelemetryData200ResponseQueryInfoTimeRange { 
    start?: string;
    end?: string;
}

