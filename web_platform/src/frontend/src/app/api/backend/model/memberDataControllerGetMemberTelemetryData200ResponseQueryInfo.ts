/**
 * IoT Platform Backend API
 *
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { MemberDataControllerGetMemberTelemetryData200ResponseQueryInfoTimeRange } from './memberDataControllerGetMemberTelemetryData200ResponseQueryInfoTimeRange';


export interface MemberDataControllerGetMemberTelemetryData200ResponseQueryInfo { 
    member_id?: string;
    tenant_id?: number;
    /**
     * Whether only latest data was requested
     */
    latest_only?: boolean;
    time_range?: MemberDataControllerGetMemberTelemetryData200ResponseQueryInfoTimeRange;
}

