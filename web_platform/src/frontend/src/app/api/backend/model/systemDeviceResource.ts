/**
 * IoT Platform Backend API
 *
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { SystemDevicePropertyValueResource } from './systemDevicePropertyValueResource';
import { SystemRuleCriteria } from './systemRuleCriteria';
import { SystemSpaceResource } from './systemSpaceResource';
import { SystemDeviceActionResource } from './systemDeviceActionResource';
import { SystemDeviceMoldResource } from './systemDeviceMoldResource';
import { SystemBuildingDevicePositionPosition } from './systemBuildingDevicePositionPosition';
import { SystemDevicePropertyFieldResource } from './systemDevicePropertyFieldResource';
import { SystemDeviceTelemetryFieldResource } from './systemDeviceTelemetryFieldResource';
import { SystemWatchPointResource } from './systemWatchPointResource';
import { SystemTenantWithRelationships } from './systemTenantWithRelationships';
import { SystemBuildingResource } from './systemBuildingResource';
import { SystemOperatorDeviceUsages } from './systemOperatorDeviceUsages';
import { SystemTelemetryValueCommon } from './systemTelemetryValueCommon';
import { SystemTelemetryValue } from './systemTelemetryValue';
import { SystemDeviceTenantAdminWithPivot } from './systemDeviceTenantAdminWithPivot';
import { SystemDeviceCalibrationRecordResource } from './systemDeviceCalibrationRecordResource';


/**
 * Response resource of model Device.
 */
export interface SystemDeviceResource { 
    /**
     * Class DeviceResource
     */
    readonly id: number;
    /**
     * Real-time device data from cache (only when include_latest_data=true)
     */
    latest_data?: object;
    /**
     * Streaming URL for devices with model_code=\'tic\' (only when device supports streaming)
     */
    streaming_url?: string | null;
    /**
     * Currently associated members and their teams (only when include_associated_members=true)
     */
    associated_members?: Array<object>;
    /**
     * The name of a device.
     */
    name?: string;
    /**
     * The third party platform name of a device.
     */
    platform?: string;
    /**
     * The identifier of a device.
     */
    identifier?: string;
    position?: SystemBuildingDevicePositionPosition;
    /**
     * The status of the device. 0代表停止 1代表运行  2代表有错误 3代表通讯故障
     */
    status?: number;
    /**
     * The foreign key to a watch point.
     */
    watch_point_ids?: Array<number>;
    /**
     * The foreign key to a device mold.
     */
    device_mold_id?: number;
    /**
     * The foreign key to a tenant.
     */
    tenant_id?: number;
    /**
     * Device Watch Points.
     */
    watch_points?: Array<SystemWatchPointResource>;
    device_mold?: SystemDeviceMoldResource;
    tenant?: SystemTenantWithRelationships;
    space?: SystemSpaceResource;
    /**
     * Building ids attached to a controller.
     */
    building_ids?: Array<number>;
    building?: SystemBuildingResource;
    /**
     * controller related buildings.
     */
    controller_buildings?: Array<SystemBuildingResource>;
    /**
     * Device property values.
     */
    device_property_values?: Array<SystemDevicePropertyValueResource>;
    /**
     * Device property fields.
     */
    device_property_fields?: Array<SystemDevicePropertyFieldResource>;
    /**
     * Device telemetry fields.
     */
    device_telemetry_fields?: Array<SystemDeviceTelemetryFieldResource>;
    /**
     * Telemetry Values.
     */
    telemetry_values?: Array<SystemTelemetryValue>;
    /**
     * Device actions.
     */
    device_actions?: Array<SystemDeviceActionResource>;
    /**
     * Attached rule criteria.
     */
    rule_criteria?: Array<SystemRuleCriteria>;
    /**
     * Attached rule criteria of definition.
     */
    rule_definition_criteria?: Array<SystemRuleCriteria>;
    /**
     * If this is connected to a gateway, then this property is the gateway.
     */
    gateway?: SystemDeviceResource;
    /**
     * If this is a gateway device, then all connected sub devices to this property.
     */
    sub_devices?: Array<SystemDeviceResource>;
    /**
     * Device latest calibration record. only one record.
     */
    device_latest_calibration_record?: Array<SystemDeviceCalibrationRecordResource>;
    /**
     * Device latest calibration record.
     */
    device_calibration_record?: Array<SystemDeviceCalibrationRecordResource>;
    /**
     * Tenant admins relates to this device. This device might be private. `is_public == false` 
     */
    tenant_admins?: Array<SystemDeviceTenantAdminWithPivot>;
    /**
     * Telemetry Values common.
     */
    field_latest_values?: Array<SystemTelemetryValueCommon>;
    /**
     * Operator Device Usages.
     */
    last_operator_device_usage?: SystemOperatorDeviceUsages;
    /**
     * Last members.
     */
    last_members?: string;
    /**
     * 上次连接时间，与最新使用记录的时间一致
     */
    last_save_time?: string;
    /**
     * The datetime when this record is deleted. Null represents not deleted.
     */
    deleted_at?: string;
    /**
     * The datetime when this record is created.
     */
    created_at?: string;
    /**
     * The datetime when this record is updated. Null represents not updated.
     */
    updated_at?: string;
    /**
     * Determine if this device was a gateway.
     */
    is_gateway?: boolean;
    /**
     * If this device connected to a gateway device, then this field keeps the gateway device primary key
     */
    gateway_device_id?: number;
    /**
     * The foreign key to a space.
     */
    space_id?: number;
    /**
     * 是否为公共设备（true默认）；false为专用设备。
     */
    is_public?: boolean;
    /**
     * true在线，false为离线。
     */
    is_online?: boolean;
    /**
     * 经度。
     */
    longitude?: string;
    /**
     * 纬度。
     */
    latitude?: string;
    /**
     * 详细地址。
     */
    address?: string;
    /**
     * channel type.
     */
    channel_type?: string;
    /**
     * The foreign key to a building.
     */
    building_id?: number;
    /**
     * 设备所在楼层，为 null 时，表示可能不在建筑内
     */
    floor?: number;
    /**
     * 异常状态。可能的值：fault/A1/A2/warning/info, 空字符串\"表示无异常值（正常）
     */
    anomaly?: string;
    /**
     * 抑制状态。0：正常，1：异常
     */
    inhibit?: number;
    /**
     * 气体通道。
     */
    gas_channel?: string;
    /**
     * 测量范围。
     */
    measurement_range?: string;
    /**
     * 上次校准时间。
     */
    last_calibration_time?: string;
    /**
     * 下次校准时间。
     */
    next_calibration_time?: string;
    /**
     * 校正周期。
     */
    calibration_cycle?: number;
    /**
     * 设备标签号。
     */
    tag_no?: string;
    /**
     * 通道ID。
     */
    channel_id?: string;
    /**
     * 出厂日期。
     */
    manufacture_date?: string;
    /**
     * SIM卡过期日期。
     */
    sim_expired_date?: string;
    /**
     * 设备序列号。
     */
    sn?: string;
    /**
     * 备注。
     */
    remark?: string;
}

