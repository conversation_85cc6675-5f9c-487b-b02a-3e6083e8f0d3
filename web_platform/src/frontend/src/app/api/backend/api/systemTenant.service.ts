/**
 * IoT Platform Backend API
 *
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
/* tslint:disable:no-unused-variable member-ordering */

import { Inject, Injectable, Optional }                      from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams,
         HttpResponse, HttpEvent, HttpParameterCodec, HttpContext 
        }       from '@angular/common/http';
import { CustomHttpParameterCodec }                          from '../encoder';
import { Observable }                                        from 'rxjs';

// @ts-ignore
import { BaseStoreResponse200 } from '../model/baseStoreResponse200';
// @ts-ignore
import { DeviceControllerIndex200Response } from '../model/deviceControllerIndex200Response';
// @ts-ignore
import { IndexQuerySchema } from '../model/indexQuerySchema';
// @ts-ignore
import { RoleControllerIndex200Response } from '../model/roleControllerIndex200Response';
// @ts-ignore
import { RuleControllerIndex200Response } from '../model/ruleControllerIndex200Response';
// @ts-ignore
import { StrategyTemplateCategoryControllerIndex200Response } from '../model/strategyTemplateCategoryControllerIndex200Response';
// @ts-ignore
import { SystemDevice } from '../model/systemDevice';
// @ts-ignore
import { SystemRole } from '../model/systemRole';
// @ts-ignore
import { SystemRuleCreateSchema } from '../model/systemRuleCreateSchema';
// @ts-ignore
import { SystemSpaceCreateRequestSchema } from '../model/systemSpaceCreateRequestSchema';
// @ts-ignore
import { SystemTenantAdminCreateSchema } from '../model/systemTenantAdminCreateSchema';
// @ts-ignore
import { SystemTenantCreateSchema } from '../model/systemTenantCreateSchema';
// @ts-ignore
import { SystemTenantDeviceStatisticsRequestSchema } from '../model/systemTenantDeviceStatisticsRequestSchema';
// @ts-ignore
import { SystemTenantUpdateSchema } from '../model/systemTenantUpdateSchema';
// @ts-ignore
import { TenantAdminControllerIndex200Response } from '../model/tenantAdminControllerIndex200Response';
// @ts-ignore
import { TenantControllerDeviceStatistics200Response } from '../model/tenantControllerDeviceStatistics200Response';
// @ts-ignore
import { TenantControllerIndex200Response } from '../model/tenantControllerIndex200Response';
// @ts-ignore
import { TenantControllerIndexSpace200Response } from '../model/tenantControllerIndexSpace200Response';
// @ts-ignore
import { TenantControllerResourcePolicy200Response } from '../model/tenantControllerResourcePolicy200Response';
// @ts-ignore
import { TenantControllerShow200Response } from '../model/tenantControllerShow200Response';

// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS }                     from '../variables';
import { Configuration }                                     from '../configuration';
import { BaseService } from '../api.base.service';



@Injectable({
  providedIn: 'root'
})
export class SystemTenantService extends BaseService {

    constructor(protected httpClient: HttpClient, @Optional() @Inject(BASE_PATH) basePath: string|string[], @Optional() configuration?: Configuration) {
        super(basePath, configuration);
    }

    /**
     * 
     * TODO TenantController::demoAdmin 
     * @param id Tenant id
     * @param systemTenantAdminCreateSchema Payload to create TenantAdmin
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public tenantControllerDemoAdmin(id: number, systemTenantAdminCreateSchema: SystemTenantAdminCreateSchema, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<BaseStoreResponse200>;
    public tenantControllerDemoAdmin(id: number, systemTenantAdminCreateSchema: SystemTenantAdminCreateSchema, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<BaseStoreResponse200>>;
    public tenantControllerDemoAdmin(id: number, systemTenantAdminCreateSchema: SystemTenantAdminCreateSchema, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<BaseStoreResponse200>>;
    public tenantControllerDemoAdmin(id: number, systemTenantAdminCreateSchema: SystemTenantAdminCreateSchema, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<any> {
        if (id === null || id === undefined) {
            throw new Error('Required parameter id was null or undefined when calling tenantControllerDemoAdmin.');
        }
        if (systemTenantAdminCreateSchema === null || systemTenantAdminCreateSchema === undefined) {
            throw new Error('Required parameter systemTenantAdminCreateSchema was null or undefined when calling tenantControllerDemoAdmin.');
        }

        let localVarHeaders = this.defaultHeaders;

        // authentication (api_http_auth) required
        localVarHeaders = this.configuration.addCredentialToHeaders('api_http_auth', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        const localVarHttpContext: HttpContext = options?.context ?? new HttpContext();

        const localVarTransferCache: boolean = options?.transferCache ?? true;


        // to determine the Content-Type header
        const consumes: string[] = [
            'application/json'
        ];
        const httpContentTypeSelected: string | undefined = this.configuration.selectHeaderContentType(consumes);
        if (httpContentTypeSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Content-Type', httpContentTypeSelected);
        }

        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/system/tenant/demo_admin`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<BaseStoreResponse200>('post', `${basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                body: systemTenantAdminCreateSchema,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Remove the specified Tenant from storage.
     * TODO Delete one Tenant by id
     * @param id Tenant id
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public tenantControllerDestroy(id: number, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: undefined, context?: HttpContext, transferCache?: boolean}): Observable<any>;
    public tenantControllerDestroy(id: number, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: undefined, context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<any>>;
    public tenantControllerDestroy(id: number, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: undefined, context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<any>>;
    public tenantControllerDestroy(id: number, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: undefined, context?: HttpContext, transferCache?: boolean}): Observable<any> {
        if (id === null || id === undefined) {
            throw new Error('Required parameter id was null or undefined when calling tenantControllerDestroy.');
        }

        let localVarHeaders = this.defaultHeaders;

        // authentication (api_http_auth) required
        localVarHeaders = this.configuration.addCredentialToHeaders('api_http_auth', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        const localVarHttpContext: HttpContext = options?.context ?? new HttpContext();

        const localVarTransferCache: boolean = options?.transferCache ?? true;


        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/system/tenant/${this.configuration.encodeParam({name: "id", value: id, in: "path", style: "simple", explode: false, dataType: "number", dataFormat: undefined})}`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<any>('delete', `${basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * 组织管理页面 &gt;&gt; 设备状态统计 设备统计 报警统计
     * 组织管理页面 &gt;&gt; 设备状态统计 设备统计 报警统计
     * @param id id
     * @param systemTenantDeviceStatisticsRequestSchema Payload to method &#x60;TenantController::deviceStatistics&#x60;
     * @param includeSubTenants Include sub tenants
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public tenantControllerDeviceStatistics(id: number, systemTenantDeviceStatisticsRequestSchema: SystemTenantDeviceStatisticsRequestSchema, includeSubTenants?: boolean, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<TenantControllerDeviceStatistics200Response>;
    public tenantControllerDeviceStatistics(id: number, systemTenantDeviceStatisticsRequestSchema: SystemTenantDeviceStatisticsRequestSchema, includeSubTenants?: boolean, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<TenantControllerDeviceStatistics200Response>>;
    public tenantControllerDeviceStatistics(id: number, systemTenantDeviceStatisticsRequestSchema: SystemTenantDeviceStatisticsRequestSchema, includeSubTenants?: boolean, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<TenantControllerDeviceStatistics200Response>>;
    public tenantControllerDeviceStatistics(id: number, systemTenantDeviceStatisticsRequestSchema: SystemTenantDeviceStatisticsRequestSchema, includeSubTenants?: boolean, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<any> {
        if (id === null || id === undefined) {
            throw new Error('Required parameter id was null or undefined when calling tenantControllerDeviceStatistics.');
        }
        if (systemTenantDeviceStatisticsRequestSchema === null || systemTenantDeviceStatisticsRequestSchema === undefined) {
            throw new Error('Required parameter systemTenantDeviceStatisticsRequestSchema was null or undefined when calling tenantControllerDeviceStatistics.');
        }

        let localVarQueryParameters = new HttpParams({encoder: this.encoder});
        localVarQueryParameters = this.addToHttpParams(localVarQueryParameters,
          <any>includeSubTenants, '_includeSubTenants');

        let localVarHeaders = this.defaultHeaders;

        // authentication (api_http_auth) required
        localVarHeaders = this.configuration.addCredentialToHeaders('api_http_auth', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        const localVarHttpContext: HttpContext = options?.context ?? new HttpContext();

        const localVarTransferCache: boolean = options?.transferCache ?? true;


        // to determine the Content-Type header
        const consumes: string[] = [
            'application/json'
        ];
        const httpContentTypeSelected: string | undefined = this.configuration.selectHeaderContentType(consumes);
        if (httpContentTypeSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Content-Type', httpContentTypeSelected);
        }

        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/system/Tenant/${this.configuration.encodeParam({name: "id", value: id, in: "path", style: "simple", explode: false, dataType: "number", dataFormat: undefined})}/deviceStatistics`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<TenantControllerDeviceStatistics200Response>('get', `${basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                body: systemTenantDeviceStatisticsRequestSchema,
                params: localVarQueryParameters,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Get list of Tenants
     * Super admin &amp; Platform admins will load all tenants. &lt;br&gt;     Tenant admin will load all tenant based on the policy setting \&#39;scaffold.tenant_admin_to_tenant_resource_policy.tenant.view\&#39;:&lt;br&gt;     &lt;b&gt;self&lt;/b&gt;: Able to load tenant from the tenant of current user. No cascade.&lt;br&gt;     &lt;b&gt;cascade&lt;/b&gt;: Able to load tenants from the tenant and sub-tenants of current user.&lt;br&gt;     &lt;b&gt;top-cascade&lt;/b&gt;: Able to load tenants from the top tenant and sub-tenants of current user.
     * @param indexQuery Index query parameters. Please check IndexQuerySchema.
     * @param includeSubTenants Include sub tenants
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public tenantControllerIndex(indexQuery?: IndexQuerySchema, includeSubTenants?: boolean, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<TenantControllerIndex200Response>;
    public tenantControllerIndex(indexQuery?: IndexQuerySchema, includeSubTenants?: boolean, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<TenantControllerIndex200Response>>;
    public tenantControllerIndex(indexQuery?: IndexQuerySchema, includeSubTenants?: boolean, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<TenantControllerIndex200Response>>;
    public tenantControllerIndex(indexQuery?: IndexQuerySchema, includeSubTenants?: boolean, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<any> {

        let localVarQueryParameters = new HttpParams({encoder: this.encoder});
        localVarQueryParameters = this.addToHttpParams(localVarQueryParameters,
          <any>indexQuery, 'IndexQuery');
        localVarQueryParameters = this.addToHttpParams(localVarQueryParameters,
          <any>includeSubTenants, '_includeSubTenants');

        let localVarHeaders = this.defaultHeaders;

        // authentication (api_http_auth) required
        localVarHeaders = this.configuration.addCredentialToHeaders('api_http_auth', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        const localVarHttpContext: HttpContext = options?.context ?? new HttpContext();

        const localVarTransferCache: boolean = options?.transferCache ?? true;


        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/system/tenant`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<TenantControllerIndex200Response>('get', `${basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                params: localVarQueryParameters,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Get devices list from a given tenant.
     * Super admin &amp; Platform admins will load all device for the $tenantId. &lt;br&gt;     Tenant admin will load all device based on the policy setting \&#39;scaffold.tenant_admin_to_tenant_resource_policy.device.view\&#39;:&lt;br&gt;     &lt;b&gt;self&lt;/b&gt;: Able to load devices from the tenant of current user. No cascade.&lt;br&gt;     &lt;b&gt;cascade&lt;/b&gt;: Able to load devices from the tenant and sub-tenants of current user.&lt;br&gt;     &lt;b&gt;top-cascade&lt;/b&gt;: Able to load devices from the top tenant and sub-tenants of current user.&lt;br&gt;&lt;br&gt;     &lt;b&gt;Real-time Data:&lt;/b&gt; When include_latest_data&#x3D;true, each device will include a latest_data field containing real-time telemetry, online status, and gateway/member relationships from the device-data-router cache.
     * @param id Tenant ID
     * @param isGateway is gateway
     * @param status device online status
     * @param includeLatestData Include real-time device data from cache
     * @param includeAssociatedMembers Include current associated members and their teams
     * @param indexQuery Index query parameters. Please check IndexQuerySchema.
     * @param includeSubTenants Include sub tenants
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public tenantControllerIndexDevice(id: number, isGateway?: boolean, status?: number, includeLatestData?: boolean, includeAssociatedMembers?: boolean, indexQuery?: IndexQuerySchema, includeSubTenants?: boolean, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<DeviceControllerIndex200Response>;
    public tenantControllerIndexDevice(id: number, isGateway?: boolean, status?: number, includeLatestData?: boolean, includeAssociatedMembers?: boolean, indexQuery?: IndexQuerySchema, includeSubTenants?: boolean, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<DeviceControllerIndex200Response>>;
    public tenantControllerIndexDevice(id: number, isGateway?: boolean, status?: number, includeLatestData?: boolean, includeAssociatedMembers?: boolean, indexQuery?: IndexQuerySchema, includeSubTenants?: boolean, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<DeviceControllerIndex200Response>>;
    public tenantControllerIndexDevice(id: number, isGateway?: boolean, status?: number, includeLatestData?: boolean, includeAssociatedMembers?: boolean, indexQuery?: IndexQuerySchema, includeSubTenants?: boolean, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<any> {
        if (id === null || id === undefined) {
            throw new Error('Required parameter id was null or undefined when calling tenantControllerIndexDevice.');
        }

        let localVarQueryParameters = new HttpParams({encoder: this.encoder});
        localVarQueryParameters = this.addToHttpParams(localVarQueryParameters,
          <any>isGateway, 'is_gateway');
        localVarQueryParameters = this.addToHttpParams(localVarQueryParameters,
          <any>status, 'status');
        localVarQueryParameters = this.addToHttpParams(localVarQueryParameters,
          <any>includeLatestData, 'include_latest_data');
        localVarQueryParameters = this.addToHttpParams(localVarQueryParameters,
          <any>includeAssociatedMembers, 'include_associated_members');
        localVarQueryParameters = this.addToHttpParams(localVarQueryParameters,
          <any>indexQuery, 'IndexQuery');
        localVarQueryParameters = this.addToHttpParams(localVarQueryParameters,
          <any>includeSubTenants, '_includeSubTenants');

        let localVarHeaders = this.defaultHeaders;

        // authentication (api_http_auth) required
        localVarHeaders = this.configuration.addCredentialToHeaders('api_http_auth', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        const localVarHttpContext: HttpContext = options?.context ?? new HttpContext();

        const localVarTransferCache: boolean = options?.transferCache ?? true;


        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/system/tenant/${this.configuration.encodeParam({name: "id", value: id, in: "path", style: "simple", explode: false, dataType: "number", dataFormat: undefined})}/device`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<DeviceControllerIndex200Response>('get', `${basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                params: localVarQueryParameters,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Get rules list from a given tenant.
     * Super admin &amp; Platform admins will load all rule for the $tenantId. &lt;br&gt;     Tenant admin will load all rule based on the policy setting \&#39;scaffold.tenant_admin_to_tenant_resource_policy.rule.view\&#39;:&lt;br&gt;     &lt;b&gt;self&lt;/b&gt;: Able to load rules from the tenant of current user. No cascade.&lt;br&gt;     &lt;b&gt;cascade&lt;/b&gt;: Able to load rules from the tenant and sub-tenants of current user.&lt;br&gt;     &lt;b&gt;top-cascade&lt;/b&gt;: Able to load rules from the top tenant and sub-tenants of current user.
     * @param id Tenant ID
     * @param indexQuery Index query parameters. Please check IndexQuerySchema.
     * @param includeSubTenants Include sub tenants
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public tenantControllerIndexRule(id: number, indexQuery?: IndexQuerySchema, includeSubTenants?: boolean, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<RuleControllerIndex200Response>;
    public tenantControllerIndexRule(id: number, indexQuery?: IndexQuerySchema, includeSubTenants?: boolean, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<RuleControllerIndex200Response>>;
    public tenantControllerIndexRule(id: number, indexQuery?: IndexQuerySchema, includeSubTenants?: boolean, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<RuleControllerIndex200Response>>;
    public tenantControllerIndexRule(id: number, indexQuery?: IndexQuerySchema, includeSubTenants?: boolean, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<any> {
        if (id === null || id === undefined) {
            throw new Error('Required parameter id was null or undefined when calling tenantControllerIndexRule.');
        }

        let localVarQueryParameters = new HttpParams({encoder: this.encoder});
        localVarQueryParameters = this.addToHttpParams(localVarQueryParameters,
          <any>indexQuery, 'IndexQuery');
        localVarQueryParameters = this.addToHttpParams(localVarQueryParameters,
          <any>includeSubTenants, '_includeSubTenants');

        let localVarHeaders = this.defaultHeaders;

        // authentication (api_http_auth) required
        localVarHeaders = this.configuration.addCredentialToHeaders('api_http_auth', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        const localVarHttpContext: HttpContext = options?.context ?? new HttpContext();

        const localVarTransferCache: boolean = options?.transferCache ?? true;


        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/system/tenant/${this.configuration.encodeParam({name: "id", value: id, in: "path", style: "simple", explode: false, dataType: "number", dataFormat: undefined})}/rule`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<RuleControllerIndex200Response>('get', `${basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                params: localVarQueryParameters,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Get spaces list from a given tenant.
     * Super admin shall load all space for the $tenantId. &lt;br&gt;     Platform admins will NOT pass. &lt;br&gt;     Tenant admin will load all space based on the policy setting \&#39;scaffold.tenant_admin_to_tenant_resource_policy.space.view\&#39;:&lt;br&gt;     &lt;b&gt;self&lt;/b&gt;: Able to load spaces from the tenant of current user. No cascade.&lt;br&gt;     &lt;b&gt;cascade&lt;/b&gt;: Able to load spaces from the tenant and sub-tenants of current user.&lt;br&gt;     &lt;b&gt;top-cascade&lt;/b&gt;: Able to load spaces from the top tenant and sub-tenants of current user.
     * @param id Tenant ID
     * @param indexQuery Index query parameters. Please check IndexQuerySchema.
     * @param includeSubTenants Include sub tenants
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public tenantControllerIndexSpace(id: number, indexQuery?: IndexQuerySchema, includeSubTenants?: boolean, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<TenantControllerIndexSpace200Response>;
    public tenantControllerIndexSpace(id: number, indexQuery?: IndexQuerySchema, includeSubTenants?: boolean, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<TenantControllerIndexSpace200Response>>;
    public tenantControllerIndexSpace(id: number, indexQuery?: IndexQuerySchema, includeSubTenants?: boolean, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<TenantControllerIndexSpace200Response>>;
    public tenantControllerIndexSpace(id: number, indexQuery?: IndexQuerySchema, includeSubTenants?: boolean, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<any> {
        if (id === null || id === undefined) {
            throw new Error('Required parameter id was null or undefined when calling tenantControllerIndexSpace.');
        }

        let localVarQueryParameters = new HttpParams({encoder: this.encoder});
        localVarQueryParameters = this.addToHttpParams(localVarQueryParameters,
          <any>indexQuery, 'IndexQuery');
        localVarQueryParameters = this.addToHttpParams(localVarQueryParameters,
          <any>includeSubTenants, '_includeSubTenants');

        let localVarHeaders = this.defaultHeaders;

        // authentication (api_http_auth) required
        localVarHeaders = this.configuration.addCredentialToHeaders('api_http_auth', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        const localVarHttpContext: HttpContext = options?.context ?? new HttpContext();

        const localVarTransferCache: boolean = options?.transferCache ?? true;


        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/system/tenant/${this.configuration.encodeParam({name: "id", value: id, in: "path", style: "simple", explode: false, dataType: "number", dataFormat: undefined})}/space`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<TenantControllerIndexSpace200Response>('get', `${basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                params: localVarQueryParameters,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Get tenant admin list from a tenant
     * Super admin and platform admin shall pass. &lt;br&gt;     Tenant admins own the $tenantId shall pass.&lt;br&gt; Other can not
     * @param id Tenant id
     * @param indexQuery Index query parameters. Please check IndexQuerySchema.
     * @param includeSubTenants Include sub tenants
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public tenantControllerIndexTenantAdmin(id: number, indexQuery?: IndexQuerySchema, includeSubTenants?: boolean, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<TenantAdminControllerIndex200Response>;
    public tenantControllerIndexTenantAdmin(id: number, indexQuery?: IndexQuerySchema, includeSubTenants?: boolean, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<TenantAdminControllerIndex200Response>>;
    public tenantControllerIndexTenantAdmin(id: number, indexQuery?: IndexQuerySchema, includeSubTenants?: boolean, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<TenantAdminControllerIndex200Response>>;
    public tenantControllerIndexTenantAdmin(id: number, indexQuery?: IndexQuerySchema, includeSubTenants?: boolean, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<any> {
        if (id === null || id === undefined) {
            throw new Error('Required parameter id was null or undefined when calling tenantControllerIndexTenantAdmin.');
        }

        let localVarQueryParameters = new HttpParams({encoder: this.encoder});
        localVarQueryParameters = this.addToHttpParams(localVarQueryParameters,
          <any>indexQuery, 'IndexQuery');
        localVarQueryParameters = this.addToHttpParams(localVarQueryParameters,
          <any>includeSubTenants, '_includeSubTenants');

        let localVarHeaders = this.defaultHeaders;

        // authentication (api_http_auth) required
        localVarHeaders = this.configuration.addCredentialToHeaders('api_http_auth', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        const localVarHttpContext: HttpContext = options?.context ?? new HttpContext();

        const localVarTransferCache: boolean = options?.transferCache ?? true;


        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/system/tenant/${this.configuration.encodeParam({name: "id", value: id, in: "path", style: "simple", explode: false, dataType: "number", dataFormat: undefined})}/tenant_admin`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<TenantAdminControllerIndex200Response>('get', `${basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                params: localVarQueryParameters,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Get tenant role list from a tenant
     * Get tenant role list from a tenant. &lt;br&gt;Super admin and platform admin shall pass. &lt;br&gt;     Tenant admins belong to the $tenantId shall pass.&lt;br&gt; Other can not
     * @param id Tenant id
     * @param indexQuery Index query parameters. Please check IndexQuerySchema.
     * @param includeSubTenants Include sub tenants
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public tenantControllerIndexTenantRole(id: number, indexQuery?: IndexQuerySchema, includeSubTenants?: boolean, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<RoleControllerIndex200Response>;
    public tenantControllerIndexTenantRole(id: number, indexQuery?: IndexQuerySchema, includeSubTenants?: boolean, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<RoleControllerIndex200Response>>;
    public tenantControllerIndexTenantRole(id: number, indexQuery?: IndexQuerySchema, includeSubTenants?: boolean, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<RoleControllerIndex200Response>>;
    public tenantControllerIndexTenantRole(id: number, indexQuery?: IndexQuerySchema, includeSubTenants?: boolean, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<any> {
        if (id === null || id === undefined) {
            throw new Error('Required parameter id was null or undefined when calling tenantControllerIndexTenantRole.');
        }

        let localVarQueryParameters = new HttpParams({encoder: this.encoder});
        localVarQueryParameters = this.addToHttpParams(localVarQueryParameters,
          <any>indexQuery, 'IndexQuery');
        localVarQueryParameters = this.addToHttpParams(localVarQueryParameters,
          <any>includeSubTenants, '_includeSubTenants');

        let localVarHeaders = this.defaultHeaders;

        // authentication (api_http_auth) required
        localVarHeaders = this.configuration.addCredentialToHeaders('api_http_auth', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        const localVarHttpContext: HttpContext = options?.context ?? new HttpContext();

        const localVarTransferCache: boolean = options?.transferCache ?? true;


        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/system/tenant/${this.configuration.encodeParam({name: "id", value: id, in: "path", style: "simple", explode: false, dataType: "number", dataFormat: undefined})}/role`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<RoleControllerIndex200Response>('get', `${basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                params: localVarQueryParameters,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Get tenants for current login user with resource policy marks.
     * Get current login user all tenants with resource policy marks. Super admin and platform will return empty array
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public tenantControllerResourcePolicy(observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<TenantControllerResourcePolicy200Response>;
    public tenantControllerResourcePolicy(observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<TenantControllerResourcePolicy200Response>>;
    public tenantControllerResourcePolicy(observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<TenantControllerResourcePolicy200Response>>;
    public tenantControllerResourcePolicy(observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<any> {

        let localVarHeaders = this.defaultHeaders;

        // authentication (api_http_auth) required
        localVarHeaders = this.configuration.addCredentialToHeaders('api_http_auth', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        const localVarHttpContext: HttpContext = options?.context ?? new HttpContext();

        const localVarTransferCache: boolean = options?.transferCache ?? true;


        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/system/tenant_resource_policy`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<TenantControllerResourcePolicy200Response>('get', `${basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Get one Tenant
     * Super admin &amp; Platform admins will load any tenant to the $id. &lt;br&gt;     Tenant admin will load any tenant based on the policy setting \&#39;scaffold.tenant_admin_to_tenant_resource_policy.tenant.view\&#39;:&lt;br&gt;     &lt;b&gt;self&lt;/b&gt;: Able to load tenant from the tenant only of current user. No cascade.&lt;br&gt;     &lt;b&gt;cascade&lt;/b&gt;: Able to load tenants from the tenant and sub-tenants of current user.&lt;br&gt;     &lt;b&gt;top-cascade&lt;/b&gt;: Able to load tenants from the top tenant and sub-tenants of current user.
     * @param id Tenant id
     * @param _with Get relationships.
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public tenantControllerShow(id: number, _with?: Array<string>, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<TenantControllerShow200Response>;
    public tenantControllerShow(id: number, _with?: Array<string>, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<TenantControllerShow200Response>>;
    public tenantControllerShow(id: number, _with?: Array<string>, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<TenantControllerShow200Response>>;
    public tenantControllerShow(id: number, _with?: Array<string>, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<any> {
        if (id === null || id === undefined) {
            throw new Error('Required parameter id was null or undefined when calling tenantControllerShow.');
        }

        let localVarQueryParameters = new HttpParams({encoder: this.encoder});
        if (_with) {
            localVarQueryParameters = this.addToHttpParams(localVarQueryParameters,
                [..._with].join(COLLECTION_FORMATS['csv']), 'with');
        }

        let localVarHeaders = this.defaultHeaders;

        // authentication (api_http_auth) required
        localVarHeaders = this.configuration.addCredentialToHeaders('api_http_auth', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        const localVarHttpContext: HttpContext = options?.context ?? new HttpContext();

        const localVarTransferCache: boolean = options?.transferCache ?? true;


        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/system/tenant/${this.configuration.encodeParam({name: "id", value: id, in: "path", style: "simple", explode: false, dataType: "number", dataFormat: undefined})}`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<TenantControllerShow200Response>('get', `${basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                params: localVarQueryParameters,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Store a new Tenant
     * Super admin, platform admins have permission \&#39;paManageTenant\&#39; shall pass.
     * @param systemTenantCreateSchema Payload to create a tenant
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public tenantControllerStore(systemTenantCreateSchema: SystemTenantCreateSchema, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<BaseStoreResponse200>;
    public tenantControllerStore(systemTenantCreateSchema: SystemTenantCreateSchema, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<BaseStoreResponse200>>;
    public tenantControllerStore(systemTenantCreateSchema: SystemTenantCreateSchema, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<BaseStoreResponse200>>;
    public tenantControllerStore(systemTenantCreateSchema: SystemTenantCreateSchema, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<any> {
        if (systemTenantCreateSchema === null || systemTenantCreateSchema === undefined) {
            throw new Error('Required parameter systemTenantCreateSchema was null or undefined when calling tenantControllerStore.');
        }

        let localVarHeaders = this.defaultHeaders;

        // authentication (api_http_auth) required
        localVarHeaders = this.configuration.addCredentialToHeaders('api_http_auth', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        const localVarHttpContext: HttpContext = options?.context ?? new HttpContext();

        const localVarTransferCache: boolean = options?.transferCache ?? true;


        // to determine the Content-Type header
        const consumes: string[] = [
            'application/json'
        ];
        const httpContentTypeSelected: string | undefined = this.configuration.selectHeaderContentType(consumes);
        if (httpContentTypeSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Content-Type', httpContentTypeSelected);
        }

        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/system/tenant`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<BaseStoreResponse200>('post', `${basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                body: systemTenantCreateSchema,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Create a device for a given tenant.
     * Create a device for a given tenant.&lt;br&gt;     Super admin shall pass.&lt;br&gt;     Platform admins shall NOT pass.&lt;br&gt;     Tenant admin permission is based on the policy setting \&#39;scaffold.tenant_admin_to_tenant_resource_policy.device.manage\&#39;.&lt;br&gt;     The tenant admin with permission \&#39;taManageDevice\&#39; is able to create a device to the given $tenantId when: &lt;br&gt;     &lt;b&gt;self&lt;/b&gt;: The $tenantId equals to $loginAccount-&gt;accountable-&gt;tenant_id.&lt;br&gt;     &lt;b&gt;cascade&lt;/b&gt;: The $tenantId is one of cascade tenant of $loginAccount-&gt;accountable-&gt;tenant&lt;br&gt;     &lt;b&gt;top-cascade&lt;/b&gt;: The $tenantId is one of cascade tenant of the top-tenant of current user.     
     * @param id Tenant ID
     * @param systemDevice Payload to create a device.
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public tenantControllerStoreDevice(id: number, systemDevice: SystemDevice, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<BaseStoreResponse200>;
    public tenantControllerStoreDevice(id: number, systemDevice: SystemDevice, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<BaseStoreResponse200>>;
    public tenantControllerStoreDevice(id: number, systemDevice: SystemDevice, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<BaseStoreResponse200>>;
    public tenantControllerStoreDevice(id: number, systemDevice: SystemDevice, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<any> {
        if (id === null || id === undefined) {
            throw new Error('Required parameter id was null or undefined when calling tenantControllerStoreDevice.');
        }
        if (systemDevice === null || systemDevice === undefined) {
            throw new Error('Required parameter systemDevice was null or undefined when calling tenantControllerStoreDevice.');
        }

        let localVarHeaders = this.defaultHeaders;

        // authentication (api_http_auth) required
        localVarHeaders = this.configuration.addCredentialToHeaders('api_http_auth', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        const localVarHttpContext: HttpContext = options?.context ?? new HttpContext();

        const localVarTransferCache: boolean = options?.transferCache ?? true;


        // to determine the Content-Type header
        const consumes: string[] = [
            'application/json'
        ];
        const httpContentTypeSelected: string | undefined = this.configuration.selectHeaderContentType(consumes);
        if (httpContentTypeSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Content-Type', httpContentTypeSelected);
        }

        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/system/tenant/${this.configuration.encodeParam({name: "id", value: id, in: "path", style: "simple", explode: false, dataType: "number", dataFormat: undefined})}/device`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<BaseStoreResponse200>('post', `${basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                body: systemDevice,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Create a rule for a given tenant.
     * Create a rule for a given tenant.&lt;br&gt;     Super admin shall pass.&lt;br&gt;     Platform admins shall NOT pass.&lt;br&gt;     Tenant admin permission is based on the policy setting \&#39;scaffold.tenant_admin_to_tenant_resource_policy.rule.manage\&#39;.&lt;br&gt;     The tenant admin with permission \&#39;taManageRule\&#39; is able to create a rule to the given $tenantId when: &lt;br&gt;     &lt;b&gt;self&lt;/b&gt;: The $tenantId equals to $loginAccount-&gt;accountable-&gt;tenant_id.&lt;br&gt;     &lt;b&gt;cascade&lt;/b&gt;: The $tenantId is one of cascade tenant of $loginAccount-&gt;accountable-&gt;tenant&lt;br&gt;     &lt;b&gt;top-cascade&lt;/b&gt;: The $tenantId is one of cascade tenant of the top-tenant of current user.     
     * @param id Tenant ID
     * @param systemRuleCreateSchema Payload to create a rule
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public tenantControllerStoreRule(id: number, systemRuleCreateSchema: SystemRuleCreateSchema, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<BaseStoreResponse200>;
    public tenantControllerStoreRule(id: number, systemRuleCreateSchema: SystemRuleCreateSchema, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<BaseStoreResponse200>>;
    public tenantControllerStoreRule(id: number, systemRuleCreateSchema: SystemRuleCreateSchema, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<BaseStoreResponse200>>;
    public tenantControllerStoreRule(id: number, systemRuleCreateSchema: SystemRuleCreateSchema, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<any> {
        if (id === null || id === undefined) {
            throw new Error('Required parameter id was null or undefined when calling tenantControllerStoreRule.');
        }
        if (systemRuleCreateSchema === null || systemRuleCreateSchema === undefined) {
            throw new Error('Required parameter systemRuleCreateSchema was null or undefined when calling tenantControllerStoreRule.');
        }

        let localVarHeaders = this.defaultHeaders;

        // authentication (api_http_auth) required
        localVarHeaders = this.configuration.addCredentialToHeaders('api_http_auth', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        const localVarHttpContext: HttpContext = options?.context ?? new HttpContext();

        const localVarTransferCache: boolean = options?.transferCache ?? true;


        // to determine the Content-Type header
        const consumes: string[] = [
            'application/json'
        ];
        const httpContentTypeSelected: string | undefined = this.configuration.selectHeaderContentType(consumes);
        if (httpContentTypeSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Content-Type', httpContentTypeSelected);
        }

        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/system/tenant/${this.configuration.encodeParam({name: "id", value: id, in: "path", style: "simple", explode: false, dataType: "number", dataFormat: undefined})}/rule`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<BaseStoreResponse200>('post', `${basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                body: systemRuleCreateSchema,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Create a space for a given tenant.
     * Create a space for a given tenant.&lt;br&gt;     Super admin shall pass.&lt;br&gt;     Platform admins shall NOT pass.&lt;br&gt;     Tenant admin permission is based on the policy setting \&#39;scaffold.tenant_admin_to_tenant_resource_policy.space.manage\&#39;.&lt;br&gt;     The tenant admin with permission \&#39;taManageSpace\&#39; is able to create a space to the given $tenantId when: &lt;br&gt;     &lt;b&gt;self&lt;/b&gt;: The $tenantId equals to $loginAccount-&gt;accountable-&gt;tenant_id.&lt;br&gt;     &lt;b&gt;cascade&lt;/b&gt;: The $tenantId is one of cascade tenant of $loginAccount-&gt;accountable-&gt;tenant&lt;br&gt;     
     * @param id Tenant ID
     * @param systemSpaceCreateRequestSchema Payload to method &#x60;TenantController::storeSpace&#x60;
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public tenantControllerStoreSpace(id: number, systemSpaceCreateRequestSchema: SystemSpaceCreateRequestSchema, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<BaseStoreResponse200>;
    public tenantControllerStoreSpace(id: number, systemSpaceCreateRequestSchema: SystemSpaceCreateRequestSchema, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<BaseStoreResponse200>>;
    public tenantControllerStoreSpace(id: number, systemSpaceCreateRequestSchema: SystemSpaceCreateRequestSchema, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<BaseStoreResponse200>>;
    public tenantControllerStoreSpace(id: number, systemSpaceCreateRequestSchema: SystemSpaceCreateRequestSchema, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<any> {
        if (id === null || id === undefined) {
            throw new Error('Required parameter id was null or undefined when calling tenantControllerStoreSpace.');
        }
        if (systemSpaceCreateRequestSchema === null || systemSpaceCreateRequestSchema === undefined) {
            throw new Error('Required parameter systemSpaceCreateRequestSchema was null or undefined when calling tenantControllerStoreSpace.');
        }

        let localVarHeaders = this.defaultHeaders;

        // authentication (api_http_auth) required
        localVarHeaders = this.configuration.addCredentialToHeaders('api_http_auth', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        const localVarHttpContext: HttpContext = options?.context ?? new HttpContext();

        const localVarTransferCache: boolean = options?.transferCache ?? true;


        // to determine the Content-Type header
        const consumes: string[] = [
            'application/json'
        ];
        const httpContentTypeSelected: string | undefined = this.configuration.selectHeaderContentType(consumes);
        if (httpContentTypeSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Content-Type', httpContentTypeSelected);
        }

        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/system/tenant/${this.configuration.encodeParam({name: "id", value: id, in: "path", style: "simple", explode: false, dataType: "number", dataFormat: undefined})}/space`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<BaseStoreResponse200>('post', `${basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                body: systemSpaceCreateRequestSchema,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Store a new Sub Tenant to a given tenant id
     * Super admin, platform admins have permission \&#39;paManageTenant\&#39; shall pass.&lt;br&gt;     Tenant admins with permission \&#39;taManageTenant\&#39; shall pass.&lt;br&gt;     Tenant admin permission is based on the policy setting \&#39;scaffold.tenant_admin_to_tenant_resource_policy.tenant.manage\&#39;.&lt;br&gt;     The tenant admin with permission \&#39;taManageTenant\&#39; is able to create a tenant to the given $tenantId when: &lt;br&gt;     &lt;b&gt;self&lt;/b&gt;: The $parentTenantId equals to $loginAccount-&gt;accountable-&gt;tenant_id.&lt;br&gt;     &lt;b&gt;cascade&lt;/b&gt;: The $parentTenantId is one of cascade tenant of $loginAccount-&gt;accountable-&gt;tenant
     * @param id Parent Tenant id
     * @param systemTenantCreateSchema Payload to create a tenant
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public tenantControllerStoreSubTenant(id: number, systemTenantCreateSchema: SystemTenantCreateSchema, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<BaseStoreResponse200>;
    public tenantControllerStoreSubTenant(id: number, systemTenantCreateSchema: SystemTenantCreateSchema, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<BaseStoreResponse200>>;
    public tenantControllerStoreSubTenant(id: number, systemTenantCreateSchema: SystemTenantCreateSchema, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<BaseStoreResponse200>>;
    public tenantControllerStoreSubTenant(id: number, systemTenantCreateSchema: SystemTenantCreateSchema, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<any> {
        if (id === null || id === undefined) {
            throw new Error('Required parameter id was null or undefined when calling tenantControllerStoreSubTenant.');
        }
        if (systemTenantCreateSchema === null || systemTenantCreateSchema === undefined) {
            throw new Error('Required parameter systemTenantCreateSchema was null or undefined when calling tenantControllerStoreSubTenant.');
        }

        let localVarHeaders = this.defaultHeaders;

        // authentication (api_http_auth) required
        localVarHeaders = this.configuration.addCredentialToHeaders('api_http_auth', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        const localVarHttpContext: HttpContext = options?.context ?? new HttpContext();

        const localVarTransferCache: boolean = options?.transferCache ?? true;


        // to determine the Content-Type header
        const consumes: string[] = [
            'application/json'
        ];
        const httpContentTypeSelected: string | undefined = this.configuration.selectHeaderContentType(consumes);
        if (httpContentTypeSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Content-Type', httpContentTypeSelected);
        }

        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/system/tenant/${this.configuration.encodeParam({name: "id", value: id, in: "path", style: "simple", explode: false, dataType: "number", dataFormat: undefined})}/sub_tenant`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<BaseStoreResponse200>('post', `${basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                body: systemTenantCreateSchema,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Store a new Tenant admin for a tenant
     * Super admin shall pass. &lt;br&gt;Tenant admins own the $tenantId and have permission \&#39;taManageTenantAdmin\&#39; shall pass.&lt;br&gt; Other can not
     * @param id Tenant id
     * @param systemTenantAdminCreateSchema Payload to create TenantAdmin
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public tenantControllerStoreTenantAdmin(id: number, systemTenantAdminCreateSchema: SystemTenantAdminCreateSchema, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<BaseStoreResponse200>;
    public tenantControllerStoreTenantAdmin(id: number, systemTenantAdminCreateSchema: SystemTenantAdminCreateSchema, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<BaseStoreResponse200>>;
    public tenantControllerStoreTenantAdmin(id: number, systemTenantAdminCreateSchema: SystemTenantAdminCreateSchema, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<BaseStoreResponse200>>;
    public tenantControllerStoreTenantAdmin(id: number, systemTenantAdminCreateSchema: SystemTenantAdminCreateSchema, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<any> {
        if (id === null || id === undefined) {
            throw new Error('Required parameter id was null or undefined when calling tenantControllerStoreTenantAdmin.');
        }
        if (systemTenantAdminCreateSchema === null || systemTenantAdminCreateSchema === undefined) {
            throw new Error('Required parameter systemTenantAdminCreateSchema was null or undefined when calling tenantControllerStoreTenantAdmin.');
        }

        let localVarHeaders = this.defaultHeaders;

        // authentication (api_http_auth) required
        localVarHeaders = this.configuration.addCredentialToHeaders('api_http_auth', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        const localVarHttpContext: HttpContext = options?.context ?? new HttpContext();

        const localVarTransferCache: boolean = options?.transferCache ?? true;


        // to determine the Content-Type header
        const consumes: string[] = [
            'application/json'
        ];
        const httpContentTypeSelected: string | undefined = this.configuration.selectHeaderContentType(consumes);
        if (httpContentTypeSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Content-Type', httpContentTypeSelected);
        }

        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/system/tenant/${this.configuration.encodeParam({name: "id", value: id, in: "path", style: "simple", explode: false, dataType: "number", dataFormat: undefined})}/tenant_admin`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<BaseStoreResponse200>('post', `${basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                body: systemTenantAdminCreateSchema,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Store a new Tenant Role
     * Super admin shall pass. &lt;br&gt;Tenant admins belong to the $tenantId and have permission \&#39;taManageRole\&#39; shall pass.&lt;br&gt; Other can not
     * @param id Tenant id
     * @param systemRole Payload to create Tenant Role
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public tenantControllerStoreTenantRole(id: number, systemRole: SystemRole, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<BaseStoreResponse200>;
    public tenantControllerStoreTenantRole(id: number, systemRole: SystemRole, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<BaseStoreResponse200>>;
    public tenantControllerStoreTenantRole(id: number, systemRole: SystemRole, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<BaseStoreResponse200>>;
    public tenantControllerStoreTenantRole(id: number, systemRole: SystemRole, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<any> {
        if (id === null || id === undefined) {
            throw new Error('Required parameter id was null or undefined when calling tenantControllerStoreTenantRole.');
        }
        if (systemRole === null || systemRole === undefined) {
            throw new Error('Required parameter systemRole was null or undefined when calling tenantControllerStoreTenantRole.');
        }

        let localVarHeaders = this.defaultHeaders;

        // authentication (api_http_auth) required
        localVarHeaders = this.configuration.addCredentialToHeaders('api_http_auth', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        const localVarHttpContext: HttpContext = options?.context ?? new HttpContext();

        const localVarTransferCache: boolean = options?.transferCache ?? true;


        // to determine the Content-Type header
        const consumes: string[] = [
            'application/json'
        ];
        const httpContentTypeSelected: string | undefined = this.configuration.selectHeaderContentType(consumes);
        if (httpContentTypeSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Content-Type', httpContentTypeSelected);
        }

        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/system/tenant/${this.configuration.encodeParam({name: "id", value: id, in: "path", style: "simple", explode: false, dataType: "number", dataFormat: undefined})}/role`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<BaseStoreResponse200>('post', `${basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                body: systemRole,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Get strategy template categories of the tenant
     * Super admin &amp; Platform admins will load any tenant to the $id. &lt;br&gt;     Tenant admin will load any tenant based on the policy setting \&#39;scaffold.tenant_admin_to_tenant_resource_policy.tenant.view\&#39;:&lt;br&gt;     &lt;b&gt;self&lt;/b&gt;: Able to load tenant from the tenant only of current user. No cascade.&lt;br&gt;     &lt;b&gt;cascade&lt;/b&gt;: Able to load tenants from the tenant and sub-tenants of current user.&lt;br&gt;     &lt;b&gt;top-cascade&lt;/b&gt;: Able to load tenants from the top tenant and sub-tenants of current user.
     * @param id Tenant id
     * @param _with Get relationships.
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public tenantControllerStrategyTemplateCategories(id: number, _with?: Array<string>, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<StrategyTemplateCategoryControllerIndex200Response>;
    public tenantControllerStrategyTemplateCategories(id: number, _with?: Array<string>, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<StrategyTemplateCategoryControllerIndex200Response>>;
    public tenantControllerStrategyTemplateCategories(id: number, _with?: Array<string>, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<StrategyTemplateCategoryControllerIndex200Response>>;
    public tenantControllerStrategyTemplateCategories(id: number, _with?: Array<string>, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<any> {
        if (id === null || id === undefined) {
            throw new Error('Required parameter id was null or undefined when calling tenantControllerStrategyTemplateCategories.');
        }

        let localVarQueryParameters = new HttpParams({encoder: this.encoder});
        if (_with) {
            localVarQueryParameters = this.addToHttpParams(localVarQueryParameters,
                [..._with].join(COLLECTION_FORMATS['csv']), 'with');
        }

        let localVarHeaders = this.defaultHeaders;

        // authentication (api_http_auth) required
        localVarHeaders = this.configuration.addCredentialToHeaders('api_http_auth', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        const localVarHttpContext: HttpContext = options?.context ?? new HttpContext();

        const localVarTransferCache: boolean = options?.transferCache ?? true;


        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/system/tenant/${this.configuration.encodeParam({name: "id", value: id, in: "path", style: "simple", explode: false, dataType: "number", dataFormat: undefined})}/strategy_template_categories`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<StrategyTemplateCategoryControllerIndex200Response>('get', `${basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                params: localVarQueryParameters,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Update one Tenant
     * Super admin, platform admins have permission \&#39;paManageTenant\&#39; shall pass.     Tenant admins with permission \&#39;taManageTenant\&#39; can update any cascade tenants belong to them.&lt;br&gt;     Tenant admin permission is based on the policy setting \&#39;scaffold.tenant_admin_to_tenant_resource_policy.tenant.manage\&#39;.&lt;br&gt;     The tenant admin with permission \&#39;taManageTenant\&#39; is able to update a tenant to the given $tenantId when: &lt;br&gt;     &lt;b&gt;self&lt;/b&gt;: The $tenantId equals to $loginAccount-&gt;accountable-&gt;tenant_id.&lt;br&gt;     &lt;b&gt;cascade&lt;/b&gt;: The $tenantId is one of cascade tenant of $loginAccount-&gt;accountable-&gt;tenant&lt;br&gt;     Super admin and platform admin can modify tenant Name, Active, Plan and Expire_at; While tenant admin can only change tenant Name.
     * @param id Tenant id
     * @param systemTenantUpdateSchema Payload to update a tenant
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public tenantControllerUpdate(id: number, systemTenantUpdateSchema: SystemTenantUpdateSchema, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: undefined, context?: HttpContext, transferCache?: boolean}): Observable<any>;
    public tenantControllerUpdate(id: number, systemTenantUpdateSchema: SystemTenantUpdateSchema, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: undefined, context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<any>>;
    public tenantControllerUpdate(id: number, systemTenantUpdateSchema: SystemTenantUpdateSchema, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: undefined, context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<any>>;
    public tenantControllerUpdate(id: number, systemTenantUpdateSchema: SystemTenantUpdateSchema, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: undefined, context?: HttpContext, transferCache?: boolean}): Observable<any> {
        if (id === null || id === undefined) {
            throw new Error('Required parameter id was null or undefined when calling tenantControllerUpdate.');
        }
        if (systemTenantUpdateSchema === null || systemTenantUpdateSchema === undefined) {
            throw new Error('Required parameter systemTenantUpdateSchema was null or undefined when calling tenantControllerUpdate.');
        }

        let localVarHeaders = this.defaultHeaders;

        // authentication (api_http_auth) required
        localVarHeaders = this.configuration.addCredentialToHeaders('api_http_auth', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        const localVarHttpContext: HttpContext = options?.context ?? new HttpContext();

        const localVarTransferCache: boolean = options?.transferCache ?? true;


        // to determine the Content-Type header
        const consumes: string[] = [
            'application/json'
        ];
        const httpContentTypeSelected: string | undefined = this.configuration.selectHeaderContentType(consumes);
        if (httpContentTypeSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Content-Type', httpContentTypeSelected);
        }

        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/system/tenant/${this.configuration.encodeParam({name: "id", value: id, in: "path", style: "simple", explode: false, dataType: "number", dataFormat: undefined})}`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<any>('put', `${basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                body: systemTenantUpdateSchema,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

}
