/**
 * IoT Platform Backend API
 *
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
/* tslint:disable:no-unused-variable member-ordering */

import { Inject, Injectable, Optional }                      from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams,
         HttpResponse, HttpEvent, HttpParameterCodec, HttpContext 
        }       from '@angular/common/http';
import { CustomHttpParameterCodec }                          from '../encoder';
import { Observable }                                        from 'rxjs';

// @ts-ignore
import { TenantControllerStreamAuth200Response } from '../model/tenantControllerStreamAuth200Response';
// @ts-ignore
import { TenantControllerStreamAuth400Response } from '../model/tenantControllerStreamAuth400Response';
// @ts-ignore
import { TenantControllerStreamAuth401Response } from '../model/tenantControllerStreamAuth401Response';
// @ts-ignore
import { TenantControllerStreamAuthRequest } from '../model/tenantControllerStreamAuthRequest';

// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS }                     from '../variables';
import { Configuration }                                     from '../configuration';
import { BaseService } from '../api.base.service';



@Injectable({
  providedIn: 'root'
})
export class SystemStreamingService extends BaseService {

    constructor(protected httpClient: HttpClient, @Optional() @Inject(BASE_PATH) basePath: string|string[], @Optional() configuration?: Configuration) {
        super(basePath, configuration);
    }

    /**
     * Stream authentication for Oryx/SRS integration
     * Validates streaming authentication tokens from Oryx/SRS for device streaming access. Called automatically by SRS when a client attempts to play a stream. Returns SRS-compatible response format.
     * @param tenantControllerStreamAuthRequest Stream authentication request from Oryx/SRS
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public tenantControllerStreamAuth(tenantControllerStreamAuthRequest: TenantControllerStreamAuthRequest, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<TenantControllerStreamAuth200Response>;
    public tenantControllerStreamAuth(tenantControllerStreamAuthRequest: TenantControllerStreamAuthRequest, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<TenantControllerStreamAuth200Response>>;
    public tenantControllerStreamAuth(tenantControllerStreamAuthRequest: TenantControllerStreamAuthRequest, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<TenantControllerStreamAuth200Response>>;
    public tenantControllerStreamAuth(tenantControllerStreamAuthRequest: TenantControllerStreamAuthRequest, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<any> {
        if (tenantControllerStreamAuthRequest === null || tenantControllerStreamAuthRequest === undefined) {
            throw new Error('Required parameter tenantControllerStreamAuthRequest was null or undefined when calling tenantControllerStreamAuth.');
        }

        let localVarHeaders = this.defaultHeaders;

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        const localVarHttpContext: HttpContext = options?.context ?? new HttpContext();

        const localVarTransferCache: boolean = options?.transferCache ?? true;


        // to determine the Content-Type header
        const consumes: string[] = [
            'application/json'
        ];
        const httpContentTypeSelected: string | undefined = this.configuration.selectHeaderContentType(consumes);
        if (httpContentTypeSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Content-Type', httpContentTypeSelected);
        }

        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/api/system/stream/auth`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<TenantControllerStreamAuth200Response>('post', `${basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                body: tenantControllerStreamAuthRequest,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

}
