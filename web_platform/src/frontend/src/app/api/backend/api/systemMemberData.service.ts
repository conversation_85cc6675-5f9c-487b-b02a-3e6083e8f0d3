/**
 * IoT Platform Backend API
 *
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
/* tslint:disable:no-unused-variable member-ordering */

import { Inject, Injectable, Optional }                      from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams,
         HttpResponse, HttpEvent, HttpParameterCodec, HttpContext 
        }       from '@angular/common/http';
import { CustomHttpParameterCodec }                          from '../encoder';
import { Observable }                                        from 'rxjs';

// @ts-ignore
import { MemberDataControllerGetAllMembersTelemetryData200Response } from '../model/memberDataControllerGetAllMembersTelemetryData200Response';
// @ts-ignore
import { MemberDataControllerGetMemberActiveDevices200Response } from '../model/memberDataControllerGetMemberActiveDevices200Response';
// @ts-ignore
import { MemberDataControllerGetMemberTelemetryData200Response } from '../model/memberDataControllerGetMemberTelemetryData200Response';
// @ts-ignore
import { MemberDataControllerProcessDataStream200Response } from '../model/memberDataControllerProcessDataStream200Response';
// @ts-ignore
import { MemberDataControllerProcessDataStreamRequest } from '../model/memberDataControllerProcessDataStreamRequest';

// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS }                     from '../variables';
import { Configuration }                                     from '../configuration';
import { BaseService } from '../api.base.service';



@Injectable({
  providedIn: 'root'
})
export class SystemMemberDataService extends BaseService {

    constructor(protected httpClient: HttpClient, @Optional() @Inject(BASE_PATH) basePath: string|string[], @Optional() configuration?: Configuration) {
        super(basePath, configuration);
    }

    /**
     * Get telemetry data for all members in a tenant
     * Returns telemetry data for all members in a tenant. Can return data within specified time range or just the latest values for each field when latest_only&#x3D;true. Includes each member\&#39;s own data and associated device data.
     * @param tenantId Tenant ID
     * @param startTime Start time for data query (ISO 8601 format). Required when latest_only&#x3D;false
     * @param endTime End time for data query (ISO 8601 format). Required when latest_only&#x3D;false
     * @param latestOnly Whether to retrieve only the latest data for each field (ignores time range if true)
     * @param includeSubTenants Whether to include sub-tenants in query
     * @param memberIds Member IDs (database id field) to filter results. Can be comma-separated string. If not provided, returns data for all members in tenant.
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public memberDataControllerGetAllMembersTelemetryData(tenantId: number, startTime?: string, endTime?: string, latestOnly?: boolean, includeSubTenants?: boolean, memberIds?: string, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<MemberDataControllerGetAllMembersTelemetryData200Response>;
    public memberDataControllerGetAllMembersTelemetryData(tenantId: number, startTime?: string, endTime?: string, latestOnly?: boolean, includeSubTenants?: boolean, memberIds?: string, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<MemberDataControllerGetAllMembersTelemetryData200Response>>;
    public memberDataControllerGetAllMembersTelemetryData(tenantId: number, startTime?: string, endTime?: string, latestOnly?: boolean, includeSubTenants?: boolean, memberIds?: string, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<MemberDataControllerGetAllMembersTelemetryData200Response>>;
    public memberDataControllerGetAllMembersTelemetryData(tenantId: number, startTime?: string, endTime?: string, latestOnly?: boolean, includeSubTenants?: boolean, memberIds?: string, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<any> {
        if (tenantId === null || tenantId === undefined) {
            throw new Error('Required parameter tenantId was null or undefined when calling memberDataControllerGetAllMembersTelemetryData.');
        }

        let localVarQueryParameters = new HttpParams({encoder: this.encoder});
        localVarQueryParameters = this.addToHttpParams(localVarQueryParameters,
          <any>startTime, 'start_time');
        localVarQueryParameters = this.addToHttpParams(localVarQueryParameters,
          <any>endTime, 'end_time');
        localVarQueryParameters = this.addToHttpParams(localVarQueryParameters,
          <any>latestOnly, 'latest_only');
        localVarQueryParameters = this.addToHttpParams(localVarQueryParameters,
          <any>includeSubTenants, 'include_sub_tenants');
        localVarQueryParameters = this.addToHttpParams(localVarQueryParameters,
          <any>memberIds, 'member_ids');

        let localVarHeaders = this.defaultHeaders;

        // authentication (api_http_auth) required
        localVarHeaders = this.configuration.addCredentialToHeaders('api_http_auth', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        const localVarHttpContext: HttpContext = options?.context ?? new HttpContext();

        const localVarTransferCache: boolean = options?.transferCache ?? true;


        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/system/tenant/${this.configuration.encodeParam({name: "tenantId", value: tenantId, in: "path", style: "simple", explode: false, dataType: "number", dataFormat: undefined})}/member-data/all-telemetry`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<MemberDataControllerGetAllMembersTelemetryData200Response>('get', `${basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                params: localVarQueryParameters,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Get member\&#39;s currently active devices
     * Returns list of devices currently associated with a specific member
     * @param tenantId Tenant ID
     * @param memberId Member ID (card_id)
     * @param includeSubTenants Whether to include sub-tenants in query
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public memberDataControllerGetMemberActiveDevices(tenantId: number, memberId: string, includeSubTenants?: boolean, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<MemberDataControllerGetMemberActiveDevices200Response>;
    public memberDataControllerGetMemberActiveDevices(tenantId: number, memberId: string, includeSubTenants?: boolean, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<MemberDataControllerGetMemberActiveDevices200Response>>;
    public memberDataControllerGetMemberActiveDevices(tenantId: number, memberId: string, includeSubTenants?: boolean, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<MemberDataControllerGetMemberActiveDevices200Response>>;
    public memberDataControllerGetMemberActiveDevices(tenantId: number, memberId: string, includeSubTenants?: boolean, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<any> {
        if (tenantId === null || tenantId === undefined) {
            throw new Error('Required parameter tenantId was null or undefined when calling memberDataControllerGetMemberActiveDevices.');
        }
        if (memberId === null || memberId === undefined) {
            throw new Error('Required parameter memberId was null or undefined when calling memberDataControllerGetMemberActiveDevices.');
        }

        let localVarQueryParameters = new HttpParams({encoder: this.encoder});
        localVarQueryParameters = this.addToHttpParams(localVarQueryParameters,
          <any>memberId, 'member_id');
        localVarQueryParameters = this.addToHttpParams(localVarQueryParameters,
          <any>includeSubTenants, 'include_sub_tenants');

        let localVarHeaders = this.defaultHeaders;

        // authentication (api_http_auth) required
        localVarHeaders = this.configuration.addCredentialToHeaders('api_http_auth', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        const localVarHttpContext: HttpContext = options?.context ?? new HttpContext();

        const localVarTransferCache: boolean = options?.transferCache ?? true;


        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/system/tenant/${this.configuration.encodeParam({name: "tenantId", value: tenantId, in: "path", style: "simple", explode: false, dataType: "number", dataFormat: undefined})}/member-data/active-devices`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<MemberDataControllerGetMemberActiveDevices200Response>('get', `${basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                params: localVarQueryParameters,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Get telemetry data for a specific member
     * Returns telemetry data for a specific member. Can return data within specified time range or just the latest values for each field when latest_only&#x3D;true. Includes member\&#39;s own data and associated device data.
     * @param tenantId Tenant ID
     * @param memberId Member ID (card_id)
     * @param startTime Start time for data query (ISO 8601 format). Required when latest_only&#x3D;false
     * @param endTime End time for data query (ISO 8601 format). Required when latest_only&#x3D;false
     * @param latestOnly Whether to retrieve only the latest data for each field (ignores time range if true)
     * @param includeSubTenants Whether to include sub-tenants in query
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public memberDataControllerGetMemberTelemetryData(tenantId: number, memberId: string, startTime?: string, endTime?: string, latestOnly?: boolean, includeSubTenants?: boolean, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<MemberDataControllerGetMemberTelemetryData200Response>;
    public memberDataControllerGetMemberTelemetryData(tenantId: number, memberId: string, startTime?: string, endTime?: string, latestOnly?: boolean, includeSubTenants?: boolean, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<MemberDataControllerGetMemberTelemetryData200Response>>;
    public memberDataControllerGetMemberTelemetryData(tenantId: number, memberId: string, startTime?: string, endTime?: string, latestOnly?: boolean, includeSubTenants?: boolean, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<MemberDataControllerGetMemberTelemetryData200Response>>;
    public memberDataControllerGetMemberTelemetryData(tenantId: number, memberId: string, startTime?: string, endTime?: string, latestOnly?: boolean, includeSubTenants?: boolean, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<any> {
        if (tenantId === null || tenantId === undefined) {
            throw new Error('Required parameter tenantId was null or undefined when calling memberDataControllerGetMemberTelemetryData.');
        }
        if (memberId === null || memberId === undefined) {
            throw new Error('Required parameter memberId was null or undefined when calling memberDataControllerGetMemberTelemetryData.');
        }

        let localVarQueryParameters = new HttpParams({encoder: this.encoder});
        localVarQueryParameters = this.addToHttpParams(localVarQueryParameters,
          <any>memberId, 'member_id');
        localVarQueryParameters = this.addToHttpParams(localVarQueryParameters,
          <any>startTime, 'start_time');
        localVarQueryParameters = this.addToHttpParams(localVarQueryParameters,
          <any>endTime, 'end_time');
        localVarQueryParameters = this.addToHttpParams(localVarQueryParameters,
          <any>latestOnly, 'latest_only');
        localVarQueryParameters = this.addToHttpParams(localVarQueryParameters,
          <any>includeSubTenants, 'include_sub_tenants');

        let localVarHeaders = this.defaultHeaders;

        // authentication (api_http_auth) required
        localVarHeaders = this.configuration.addCredentialToHeaders('api_http_auth', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        const localVarHttpContext: HttpContext = options?.context ?? new HttpContext();

        const localVarTransferCache: boolean = options?.transferCache ?? true;


        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/system/tenant/${this.configuration.encodeParam({name: "tenantId", value: tenantId, in: "path", style: "simple", explode: false, dataType: "number", dataFormat: undefined})}/member-data/telemetry`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<MemberDataControllerGetMemberTelemetryData200Response>('get', `${basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                params: localVarQueryParameters,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Process incoming data stream
     * Webhook endpoint for processing incoming member and device telemetry data stream from Go service
     * @param tenantId Tenant ID
     * @param memberDataControllerProcessDataStreamRequest Parsed telemetry data payload
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public memberDataControllerProcessDataStream(tenantId: number, memberDataControllerProcessDataStreamRequest: MemberDataControllerProcessDataStreamRequest, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<MemberDataControllerProcessDataStream200Response>;
    public memberDataControllerProcessDataStream(tenantId: number, memberDataControllerProcessDataStreamRequest: MemberDataControllerProcessDataStreamRequest, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<MemberDataControllerProcessDataStream200Response>>;
    public memberDataControllerProcessDataStream(tenantId: number, memberDataControllerProcessDataStreamRequest: MemberDataControllerProcessDataStreamRequest, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<MemberDataControllerProcessDataStream200Response>>;
    public memberDataControllerProcessDataStream(tenantId: number, memberDataControllerProcessDataStreamRequest: MemberDataControllerProcessDataStreamRequest, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<any> {
        if (tenantId === null || tenantId === undefined) {
            throw new Error('Required parameter tenantId was null or undefined when calling memberDataControllerProcessDataStream.');
        }
        if (memberDataControllerProcessDataStreamRequest === null || memberDataControllerProcessDataStreamRequest === undefined) {
            throw new Error('Required parameter memberDataControllerProcessDataStreamRequest was null or undefined when calling memberDataControllerProcessDataStream.');
        }

        let localVarHeaders = this.defaultHeaders;

        // authentication (api_http_auth) required
        localVarHeaders = this.configuration.addCredentialToHeaders('api_http_auth', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        const localVarHttpContext: HttpContext = options?.context ?? new HttpContext();

        const localVarTransferCache: boolean = options?.transferCache ?? true;


        // to determine the Content-Type header
        const consumes: string[] = [
            'application/json'
        ];
        const httpContentTypeSelected: string | undefined = this.configuration.selectHeaderContentType(consumes);
        if (httpContentTypeSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Content-Type', httpContentTypeSelected);
        }

        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/system/tenant/${this.configuration.encodeParam({name: "tenantId", value: tenantId, in: "path", style: "simple", explode: false, dataType: "number", dataFormat: undefined})}/member-data/process-stream`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<MemberDataControllerProcessDataStream200Response>('post', `${basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                body: memberDataControllerProcessDataStreamRequest,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

}
