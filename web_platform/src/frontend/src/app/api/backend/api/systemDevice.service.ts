/**
 * IoT Platform Backend API
 *
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
/* tslint:disable:no-unused-variable member-ordering */

import { Inject, Injectable, Optional }                      from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams,
         HttpResponse, HttpEvent, HttpParameterCodec, HttpContext 
        }       from '@angular/common/http';
import { CustomHttpParameterCodec }                          from '../encoder';
import { Observable }                                        from 'rxjs';

// @ts-ignore
import { BaseStoreResponse200 } from '../model/baseStoreResponse200';
// @ts-ignore
import { BuildingControllerDestroyListRequest } from '../model/buildingControllerDestroyListRequest';
// @ts-ignore
import { DeviceControllerIndex200Response } from '../model/deviceControllerIndex200Response';
// @ts-ignore
import { DeviceControllerOverview200Response } from '../model/deviceControllerOverview200Response';
// @ts-ignore
import { DeviceControllerShow200Response } from '../model/deviceControllerShow200Response';
// @ts-ignore
import { DeviceControllerTelemetryValue200Response } from '../model/deviceControllerTelemetryValue200Response';
// @ts-ignore
import { IndexQuerySchema } from '../model/indexQuerySchema';
// @ts-ignore
import { SystemCallDeviceActionParameterItem } from '../model/systemCallDeviceActionParameterItem';
// @ts-ignore
import { SystemDevice } from '../model/systemDevice';
// @ts-ignore
import { SystemDeviceAssignTenantAdminRequestSchema } from '../model/systemDeviceAssignTenantAdminRequestSchema';
// @ts-ignore
import { SystemDeviceCheckRequestSchema } from '../model/systemDeviceCheckRequestSchema';
// @ts-ignore
import { SystemDeviceCommandRequestSchema } from '../model/systemDeviceCommandRequestSchema';
// @ts-ignore
import { SystemDeviceTelemetryValueRequestSchema } from '../model/systemDeviceTelemetryValueRequestSchema';
// @ts-ignore
import { SystemDeviceUpdatePropertyFieldValueSchema } from '../model/systemDeviceUpdatePropertyFieldValueSchema';

// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS }                     from '../variables';
import { Configuration }                                     from '../configuration';
import { BaseService } from '../api.base.service';



@Injectable({
  providedIn: 'root'
})
export class SystemDeviceService extends BaseService {

    constructor(protected httpClient: HttpClient, @Optional() @Inject(BASE_PATH) basePath: string|string[], @Optional() configuration?: Configuration) {
        super(basePath, configuration);
    }

    /**
     * Request an action to a device.
     * Request an action to a device.&lt;br&gt;super admin shall pass.&lt;br&gt;Platform admins shall NOT.&lt;br&gt;     Tenant admin with permission \&#39;taManageDevice\&#39; is validated based on the policy setting \&#39;scaffold.tenant_admin_to_tenant_resource_policy.device.manage\&#39;&lt;br&gt;     &lt;b&gt;self&lt;/b&gt;: Able to update devices belong to the tenant of current user.&lt;br&gt;     &lt;b&gt;cascade&lt;/b&gt;: Able to update devices belong to the tenant and sub-tenants of current user.&lt;br&gt;     If the Device $instance is public, then tenant admins of the space of the device shall pass&lt;br&gt;     If the Device $instance is NOT public, then tenant admins of the device with can_use&#x3D;&#x3D;true shall pass&lt;br&gt;
     * @param deviceId Device Id
     * @param actionId Action Id
     * @param systemCallDeviceActionParameterItem Payload to call a device action.
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public deviceControllerAction(deviceId: number, actionId: number, systemCallDeviceActionParameterItem: Array<SystemCallDeviceActionParameterItem>, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: undefined, context?: HttpContext, transferCache?: boolean}): Observable<any>;
    public deviceControllerAction(deviceId: number, actionId: number, systemCallDeviceActionParameterItem: Array<SystemCallDeviceActionParameterItem>, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: undefined, context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<any>>;
    public deviceControllerAction(deviceId: number, actionId: number, systemCallDeviceActionParameterItem: Array<SystemCallDeviceActionParameterItem>, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: undefined, context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<any>>;
    public deviceControllerAction(deviceId: number, actionId: number, systemCallDeviceActionParameterItem: Array<SystemCallDeviceActionParameterItem>, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: undefined, context?: HttpContext, transferCache?: boolean}): Observable<any> {
        if (deviceId === null || deviceId === undefined) {
            throw new Error('Required parameter deviceId was null or undefined when calling deviceControllerAction.');
        }
        if (actionId === null || actionId === undefined) {
            throw new Error('Required parameter actionId was null or undefined when calling deviceControllerAction.');
        }
        if (systemCallDeviceActionParameterItem === null || systemCallDeviceActionParameterItem === undefined) {
            throw new Error('Required parameter systemCallDeviceActionParameterItem was null or undefined when calling deviceControllerAction.');
        }

        let localVarHeaders = this.defaultHeaders;

        // authentication (api_http_auth) required
        localVarHeaders = this.configuration.addCredentialToHeaders('api_http_auth', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        const localVarHttpContext: HttpContext = options?.context ?? new HttpContext();

        const localVarTransferCache: boolean = options?.transferCache ?? true;


        // to determine the Content-Type header
        const consumes: string[] = [
            'application/json'
        ];
        const httpContentTypeSelected: string | undefined = this.configuration.selectHeaderContentType(consumes);
        if (httpContentTypeSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Content-Type', httpContentTypeSelected);
        }

        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/system/device/${this.configuration.encodeParam({name: "deviceId", value: deviceId, in: "path", style: "simple", explode: false, dataType: "number", dataFormat: undefined})}/action/${this.configuration.encodeParam({name: "actionId", value: actionId, in: "path", style: "simple", explode: false, dataType: "number", dataFormat: undefined})}`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<any>('put', `${basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                body: systemCallDeviceActionParameterItem,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * 
     * TODO DeviceController::assignTenantAdmin 
     * @param id Device ID
     * @param systemDeviceAssignTenantAdminRequestSchema Payload to method &#x60;DeviceController::assignTenantAdmin&#x60;
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public deviceControllerAssignTenantAdmin(id: number, systemDeviceAssignTenantAdminRequestSchema: SystemDeviceAssignTenantAdminRequestSchema, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: undefined, context?: HttpContext, transferCache?: boolean}): Observable<any>;
    public deviceControllerAssignTenantAdmin(id: number, systemDeviceAssignTenantAdminRequestSchema: SystemDeviceAssignTenantAdminRequestSchema, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: undefined, context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<any>>;
    public deviceControllerAssignTenantAdmin(id: number, systemDeviceAssignTenantAdminRequestSchema: SystemDeviceAssignTenantAdminRequestSchema, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: undefined, context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<any>>;
    public deviceControllerAssignTenantAdmin(id: number, systemDeviceAssignTenantAdminRequestSchema: SystemDeviceAssignTenantAdminRequestSchema, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: undefined, context?: HttpContext, transferCache?: boolean}): Observable<any> {
        if (id === null || id === undefined) {
            throw new Error('Required parameter id was null or undefined when calling deviceControllerAssignTenantAdmin.');
        }
        if (systemDeviceAssignTenantAdminRequestSchema === null || systemDeviceAssignTenantAdminRequestSchema === undefined) {
            throw new Error('Required parameter systemDeviceAssignTenantAdminRequestSchema was null or undefined when calling deviceControllerAssignTenantAdmin.');
        }

        let localVarHeaders = this.defaultHeaders;

        // authentication (api_http_auth) required
        localVarHeaders = this.configuration.addCredentialToHeaders('api_http_auth', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        const localVarHttpContext: HttpContext = options?.context ?? new HttpContext();

        const localVarTransferCache: boolean = options?.transferCache ?? true;


        // to determine the Content-Type header
        const consumes: string[] = [
            'application/json'
        ];
        const httpContentTypeSelected: string | undefined = this.configuration.selectHeaderContentType(consumes);
        if (httpContentTypeSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Content-Type', httpContentTypeSelected);
        }

        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/system/device/${this.configuration.encodeParam({name: "id", value: id, in: "path", style: "simple", explode: false, dataType: "number", dataFormat: undefined})}/assign_tenant_admin`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<any>('put', `${basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                body: systemDeviceAssignTenantAdminRequestSchema,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * 已经接近下次校准时间或已经逾期下次校准时间的设备列表
     * 已经接近下次校准时间或已经逾期下次校准时间的设备列表
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public deviceControllerCalibration(observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<BaseStoreResponse200>;
    public deviceControllerCalibration(observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<BaseStoreResponse200>>;
    public deviceControllerCalibration(observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<BaseStoreResponse200>>;
    public deviceControllerCalibration(observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<any> {

        let localVarHeaders = this.defaultHeaders;

        // authentication (api_http_auth) required
        localVarHeaders = this.configuration.addCredentialToHeaders('api_http_auth', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        const localVarHttpContext: HttpContext = options?.context ?? new HttpContext();

        const localVarTransferCache: boolean = options?.transferCache ?? true;


        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/system/device/calibration`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<BaseStoreResponse200>('get', `${basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * 
     * 用于设备端上传数据前的设备确认，此过程会确保\&#39;保存遥测数据所依赖的数据\&#39;提前已创建好
     * @param systemDeviceCheckRequestSchema Payload to method &#x60;DeviceController::check&#x60;
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public deviceControllerCheck(systemDeviceCheckRequestSchema: SystemDeviceCheckRequestSchema, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<BaseStoreResponse200>;
    public deviceControllerCheck(systemDeviceCheckRequestSchema: SystemDeviceCheckRequestSchema, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<BaseStoreResponse200>>;
    public deviceControllerCheck(systemDeviceCheckRequestSchema: SystemDeviceCheckRequestSchema, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<BaseStoreResponse200>>;
    public deviceControllerCheck(systemDeviceCheckRequestSchema: SystemDeviceCheckRequestSchema, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<any> {
        if (systemDeviceCheckRequestSchema === null || systemDeviceCheckRequestSchema === undefined) {
            throw new Error('Required parameter systemDeviceCheckRequestSchema was null or undefined when calling deviceControllerCheck.');
        }

        let localVarHeaders = this.defaultHeaders;

        // authentication (api_http_auth) required
        localVarHeaders = this.configuration.addCredentialToHeaders('api_http_auth', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        const localVarHttpContext: HttpContext = options?.context ?? new HttpContext();

        const localVarTransferCache: boolean = options?.transferCache ?? true;


        // to determine the Content-Type header
        const consumes: string[] = [
            'application/json'
        ];
        const httpContentTypeSelected: string | undefined = this.configuration.selectHeaderContentType(consumes);
        if (httpContentTypeSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Content-Type', httpContentTypeSelected);
        }

        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/system/device/check`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<BaseStoreResponse200>('post', `${basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                body: systemDeviceCheckRequestSchema,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Delete one Device
     * Delete one Device by id&lt;br&gt;Super admin shall pass.&lt;br&gt;Platform admins shall NOT.&lt;br&gt;     Tenant admin with permission \&#39;taManageDevice\&#39; is validated based on the policy setting \&#39;scaffold.tenant_admin_to_tenant_resource_policy.device.manage\&#39;&lt;br&gt;     &lt;b&gt;self&lt;/b&gt;: Able to delete devices belong to the tenant of current user.&lt;br&gt;     &lt;b&gt;cascade&lt;/b&gt;: Able to delete devices belong to the tenant and sub-tenants of current user.
     * @param id Device id
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public deviceControllerDestroy(id: number, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: undefined, context?: HttpContext, transferCache?: boolean}): Observable<any>;
    public deviceControllerDestroy(id: number, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: undefined, context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<any>>;
    public deviceControllerDestroy(id: number, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: undefined, context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<any>>;
    public deviceControllerDestroy(id: number, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: undefined, context?: HttpContext, transferCache?: boolean}): Observable<any> {
        if (id === null || id === undefined) {
            throw new Error('Required parameter id was null or undefined when calling deviceControllerDestroy.');
        }

        let localVarHeaders = this.defaultHeaders;

        // authentication (api_http_auth) required
        localVarHeaders = this.configuration.addCredentialToHeaders('api_http_auth', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        const localVarHttpContext: HttpContext = options?.context ?? new HttpContext();

        const localVarTransferCache: boolean = options?.transferCache ?? true;


        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/system/device/${this.configuration.encodeParam({name: "id", value: id, in: "path", style: "simple", explode: false, dataType: "number", dataFormat: undefined})}`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<any>('delete', `${basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * 批量删除设备
     * 批量删除设备
     * @param buildingControllerDestroyListRequest Request body for delete multiple devices
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public deviceControllerDestroyList(buildingControllerDestroyListRequest: BuildingControllerDestroyListRequest, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: undefined, context?: HttpContext, transferCache?: boolean}): Observable<any>;
    public deviceControllerDestroyList(buildingControllerDestroyListRequest: BuildingControllerDestroyListRequest, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: undefined, context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<any>>;
    public deviceControllerDestroyList(buildingControllerDestroyListRequest: BuildingControllerDestroyListRequest, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: undefined, context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<any>>;
    public deviceControllerDestroyList(buildingControllerDestroyListRequest: BuildingControllerDestroyListRequest, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: undefined, context?: HttpContext, transferCache?: boolean}): Observable<any> {
        if (buildingControllerDestroyListRequest === null || buildingControllerDestroyListRequest === undefined) {
            throw new Error('Required parameter buildingControllerDestroyListRequest was null or undefined when calling deviceControllerDestroyList.');
        }

        let localVarHeaders = this.defaultHeaders;

        // authentication (api_http_auth) required
        localVarHeaders = this.configuration.addCredentialToHeaders('api_http_auth', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        const localVarHttpContext: HttpContext = options?.context ?? new HttpContext();

        const localVarTransferCache: boolean = options?.transferCache ?? true;


        // to determine the Content-Type header
        const consumes: string[] = [
            'application/json'
        ];
        const httpContentTypeSelected: string | undefined = this.configuration.selectHeaderContentType(consumes);
        if (httpContentTypeSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Content-Type', httpContentTypeSelected);
        }

        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/system/device/destroy_list`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<any>('post', `${basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                body: buildingControllerDestroyListRequest,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Submit an plc import
     * Submit an plc import. tenant admin shall pass.
     * @param file The file
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public deviceControllerImport(file: Blob, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: undefined, context?: HttpContext, transferCache?: boolean}): Observable<any>;
    public deviceControllerImport(file: Blob, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: undefined, context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<any>>;
    public deviceControllerImport(file: Blob, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: undefined, context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<any>>;
    public deviceControllerImport(file: Blob, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: undefined, context?: HttpContext, transferCache?: boolean}): Observable<any> {
        if (file === null || file === undefined) {
            throw new Error('Required parameter file was null or undefined when calling deviceControllerImport.');
        }

        let localVarHeaders = this.defaultHeaders;

        // authentication (api_http_auth) required
        localVarHeaders = this.configuration.addCredentialToHeaders('api_http_auth', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        const localVarHttpContext: HttpContext = options?.context ?? new HttpContext();

        const localVarTransferCache: boolean = options?.transferCache ?? true;

        // to determine the Content-Type header
        const consumes: string[] = [
            'multipart/form-data'
        ];

        const canConsumeForm = this.canConsumeForm(consumes);

        let localVarFormParams: { append(param: string, value: any): any; };
        let localVarUseForm = false;
        let localVarConvertFormParamsToString = false;
        // use FormData to transmit files using content-type "multipart/form-data"
        // see https://stackoverflow.com/questions/4007969/application-x-www-form-urlencoded-or-multipart-form-data
        localVarUseForm = canConsumeForm;
        if (localVarUseForm) {
            localVarFormParams = new FormData();
        } else {
            localVarFormParams = new HttpParams({encoder: this.encoder});
        }

        if (file !== undefined) {
            localVarFormParams = localVarFormParams.append('file', <any>file) as any || localVarFormParams;
        }

        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/system/device/import`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<any>('post', `${basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                body: localVarConvertFormParamsToString ? localVarFormParams.toString() : localVarFormParams,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Get device list based on current user tenant resource policy.
     * Get device list based on current user tenant resource policy.&lt;br&gt;Super admin &amp; Platform admins will load all devices. &lt;br&gt;     Tenant admin will load all devices based on the policy setting \&#39;scaffold.tenant_admin_to_tenant_resource_policy.device.view\&#39;:&lt;br&gt;     &lt;b&gt;self&lt;/b&gt;: Load devices from the tenant of current user. No cascade.&lt;br&gt;     &lt;b&gt;cascade&lt;/b&gt;:  Load devices from the tenant and sub-tenants of current user.&lt;br&gt;     &lt;b&gt;top-cascade&lt;/b&gt;: Load devices from the top tenant and sub-tenants of current user.
     * @param isGateway is gateway
     * @param status device online status
     * @param indexQuery Index query parameters. Please check IndexQuerySchema.
     * @param includeSubTenants Include sub tenants
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public deviceControllerIndex(isGateway?: boolean, status?: number, indexQuery?: IndexQuerySchema, includeSubTenants?: boolean, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<DeviceControllerIndex200Response>;
    public deviceControllerIndex(isGateway?: boolean, status?: number, indexQuery?: IndexQuerySchema, includeSubTenants?: boolean, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<DeviceControllerIndex200Response>>;
    public deviceControllerIndex(isGateway?: boolean, status?: number, indexQuery?: IndexQuerySchema, includeSubTenants?: boolean, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<DeviceControllerIndex200Response>>;
    public deviceControllerIndex(isGateway?: boolean, status?: number, indexQuery?: IndexQuerySchema, includeSubTenants?: boolean, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<any> {

        let localVarQueryParameters = new HttpParams({encoder: this.encoder});
        localVarQueryParameters = this.addToHttpParams(localVarQueryParameters,
          <any>isGateway, 'is_gateway');
        localVarQueryParameters = this.addToHttpParams(localVarQueryParameters,
          <any>status, 'status');
        localVarQueryParameters = this.addToHttpParams(localVarQueryParameters,
          <any>indexQuery, 'IndexQuery');
        localVarQueryParameters = this.addToHttpParams(localVarQueryParameters,
          <any>includeSubTenants, '_includeSubTenants');

        let localVarHeaders = this.defaultHeaders;

        // authentication (api_http_auth) required
        localVarHeaders = this.configuration.addCredentialToHeaders('api_http_auth', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        const localVarHttpContext: HttpContext = options?.context ?? new HttpContext();

        const localVarTransferCache: boolean = options?.transferCache ?? true;


        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/system/device`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<DeviceControllerIndex200Response>('get', `${basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                params: localVarQueryParameters,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * 总览
     * 总览
     * @param tenantId tenant id
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public deviceControllerOverview(tenantId?: number, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<DeviceControllerOverview200Response>;
    public deviceControllerOverview(tenantId?: number, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<DeviceControllerOverview200Response>>;
    public deviceControllerOverview(tenantId?: number, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<DeviceControllerOverview200Response>>;
    public deviceControllerOverview(tenantId?: number, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<any> {

        let localVarQueryParameters = new HttpParams({encoder: this.encoder});
        localVarQueryParameters = this.addToHttpParams(localVarQueryParameters,
          <any>tenantId, 'tenant_id');

        let localVarHeaders = this.defaultHeaders;

        // authentication (api_http_auth) required
        localVarHeaders = this.configuration.addCredentialToHeaders('api_http_auth', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        const localVarHttpContext: HttpContext = options?.context ?? new HttpContext();

        const localVarTransferCache: boolean = options?.transferCache ?? true;


        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/system/device/overview/overview`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<DeviceControllerOverview200Response>('get', `${basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                params: localVarQueryParameters,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * 
     * TODO DeviceController::removeTenantAdmin 
     * @param id Device ID
     * @param tenantAdminId tenantAdminId
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public deviceControllerRemoveTenantAdmin(id: number, tenantAdminId: number, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: undefined, context?: HttpContext, transferCache?: boolean}): Observable<any>;
    public deviceControllerRemoveTenantAdmin(id: number, tenantAdminId: number, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: undefined, context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<any>>;
    public deviceControllerRemoveTenantAdmin(id: number, tenantAdminId: number, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: undefined, context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<any>>;
    public deviceControllerRemoveTenantAdmin(id: number, tenantAdminId: number, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: undefined, context?: HttpContext, transferCache?: boolean}): Observable<any> {
        if (id === null || id === undefined) {
            throw new Error('Required parameter id was null or undefined when calling deviceControllerRemoveTenantAdmin.');
        }
        if (tenantAdminId === null || tenantAdminId === undefined) {
            throw new Error('Required parameter tenantAdminId was null or undefined when calling deviceControllerRemoveTenantAdmin.');
        }

        let localVarHeaders = this.defaultHeaders;

        // authentication (api_http_auth) required
        localVarHeaders = this.configuration.addCredentialToHeaders('api_http_auth', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        const localVarHttpContext: HttpContext = options?.context ?? new HttpContext();

        const localVarTransferCache: boolean = options?.transferCache ?? true;


        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/system/device/${this.configuration.encodeParam({name: "id", value: id, in: "path", style: "simple", explode: false, dataType: "number", dataFormat: undefined})}/tenant_admin/${this.configuration.encodeParam({name: "tenantAdminId", value: tenantAdminId, in: "path", style: "simple", explode: false, dataType: "number", dataFormat: undefined})}`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<any>('delete', `${basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * 
     * send command to device
     * @param systemDeviceCommandRequestSchema Payload to method &#x60;DeviceController::overview&#x60;
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public deviceControllerSendCommand(systemDeviceCommandRequestSchema: SystemDeviceCommandRequestSchema, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: undefined, context?: HttpContext, transferCache?: boolean}): Observable<any>;
    public deviceControllerSendCommand(systemDeviceCommandRequestSchema: SystemDeviceCommandRequestSchema, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: undefined, context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<any>>;
    public deviceControllerSendCommand(systemDeviceCommandRequestSchema: SystemDeviceCommandRequestSchema, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: undefined, context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<any>>;
    public deviceControllerSendCommand(systemDeviceCommandRequestSchema: SystemDeviceCommandRequestSchema, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: undefined, context?: HttpContext, transferCache?: boolean}): Observable<any> {
        if (systemDeviceCommandRequestSchema === null || systemDeviceCommandRequestSchema === undefined) {
            throw new Error('Required parameter systemDeviceCommandRequestSchema was null or undefined when calling deviceControllerSendCommand.');
        }

        let localVarHeaders = this.defaultHeaders;

        // authentication (api_http_auth) required
        localVarHeaders = this.configuration.addCredentialToHeaders('api_http_auth', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        const localVarHttpContext: HttpContext = options?.context ?? new HttpContext();

        const localVarTransferCache: boolean = options?.transferCache ?? true;


        // to determine the Content-Type header
        const consumes: string[] = [
            'application/json'
        ];
        const httpContentTypeSelected: string | undefined = this.configuration.selectHeaderContentType(consumes);
        if (httpContentTypeSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Content-Type', httpContentTypeSelected);
        }

        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/system/device/send_command`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<any>('post', `${basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                body: systemDeviceCommandRequestSchema,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Get one Device
     * Get one Device by id&lt;br&gt;Super admin &amp; Platform admins shall pass.&lt;br&gt;     Tenant admin permission is based on the policy setting \&#39;scaffold.tenant_admin_to_tenant_resource_policy.device.view\&#39;:&lt;br&gt;     &lt;b&gt;self&lt;/b&gt;: View a device from the tenant of current user. No cascade.&lt;br&gt;     &lt;b&gt;cascade&lt;/b&gt;:  View a device from the tenant and sub-tenants of current user.&lt;br&gt;     &lt;b&gt;top-cascade&lt;/b&gt;: View a device from the top tenant and sub-tenants of current user.&lt;br&gt;&lt;br&gt;     &lt;b&gt;Real-time Data:&lt;/b&gt; When include_latest_data&#x3D;true, the device will include a latest_data field containing real-time telemetry, online status, and gateway/member relationships from the device-data-router cache.
     * @param id Device id
     * @param _with Get relationships.
     * @param includeLatestData Include real-time device data from cache
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public deviceControllerShow(id: number, _with?: Array<string>, includeLatestData?: boolean, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<DeviceControllerShow200Response>;
    public deviceControllerShow(id: number, _with?: Array<string>, includeLatestData?: boolean, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<DeviceControllerShow200Response>>;
    public deviceControllerShow(id: number, _with?: Array<string>, includeLatestData?: boolean, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<DeviceControllerShow200Response>>;
    public deviceControllerShow(id: number, _with?: Array<string>, includeLatestData?: boolean, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<any> {
        if (id === null || id === undefined) {
            throw new Error('Required parameter id was null or undefined when calling deviceControllerShow.');
        }

        let localVarQueryParameters = new HttpParams({encoder: this.encoder});
        if (_with) {
            localVarQueryParameters = this.addToHttpParams(localVarQueryParameters,
                [..._with].join(COLLECTION_FORMATS['csv']), 'with');
        }
        localVarQueryParameters = this.addToHttpParams(localVarQueryParameters,
          <any>includeLatestData, 'include_latest_data');

        let localVarHeaders = this.defaultHeaders;

        // authentication (api_http_auth) required
        localVarHeaders = this.configuration.addCredentialToHeaders('api_http_auth', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        const localVarHttpContext: HttpContext = options?.context ?? new HttpContext();

        const localVarTransferCache: boolean = options?.transferCache ?? true;


        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/system/device/${this.configuration.encodeParam({name: "id", value: id, in: "path", style: "simple", explode: false, dataType: "number", dataFormat: undefined})}`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<DeviceControllerShow200Response>('get', `${basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                params: localVarQueryParameters,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Get one Device by identifier
     * Get one Device by identifier&lt;br&gt;Super admin &amp; Platform admins shall pass.&lt;br&gt;     Tenant admin permission is based on the policy setting \&#39;scaffold.tenant_admin_to_tenant_resource_policy.device.view\&#39;:&lt;br&gt;     &lt;b&gt;self&lt;/b&gt;: View a device from the tenant of current user. No cascade.&lt;br&gt;     &lt;b&gt;cascade&lt;/b&gt;:  View a device from the tenant and sub-tenants of current user.&lt;br&gt;     &lt;b&gt;top-cascade&lt;/b&gt;: View a device from the top tenant and sub-tenants of current user.&lt;br&gt;&lt;br&gt;     &lt;b&gt;Real-time Data:&lt;/b&gt; When include_latest_data&#x3D;true, the device will include a latest_data field containing real-time telemetry, online status, and gateway/member relationships from the device-data-router cache.
     * @param identifier Device identifier
     * @param _with Get relationships.
     * @param includeLatestData Include real-time device data from cache
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public deviceControllerShowByIdentifier(identifier: string, _with?: Array<string>, includeLatestData?: boolean, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<DeviceControllerShow200Response>;
    public deviceControllerShowByIdentifier(identifier: string, _with?: Array<string>, includeLatestData?: boolean, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<DeviceControllerShow200Response>>;
    public deviceControllerShowByIdentifier(identifier: string, _with?: Array<string>, includeLatestData?: boolean, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<DeviceControllerShow200Response>>;
    public deviceControllerShowByIdentifier(identifier: string, _with?: Array<string>, includeLatestData?: boolean, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<any> {
        if (identifier === null || identifier === undefined) {
            throw new Error('Required parameter identifier was null or undefined when calling deviceControllerShowByIdentifier.');
        }

        let localVarQueryParameters = new HttpParams({encoder: this.encoder});
        if (_with) {
            localVarQueryParameters = this.addToHttpParams(localVarQueryParameters,
                [..._with].join(COLLECTION_FORMATS['csv']), 'with');
        }
        localVarQueryParameters = this.addToHttpParams(localVarQueryParameters,
          <any>includeLatestData, 'include_latest_data');

        let localVarHeaders = this.defaultHeaders;

        // authentication (api_http_auth) required
        localVarHeaders = this.configuration.addCredentialToHeaders('api_http_auth', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        const localVarHttpContext: HttpContext = options?.context ?? new HttpContext();

        const localVarTransferCache: boolean = options?.transferCache ?? true;


        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/system/device/identifier/${this.configuration.encodeParam({name: "identifier", value: identifier, in: "path", style: "simple", explode: false, dataType: "string", dataFormat: undefined})}`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<DeviceControllerShow200Response>('get', `${basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                params: localVarQueryParameters,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Retrieve telemetry values from a device
     * Retrieve telemetry values from a device&lt;br&gt;super admin shall pass.&lt;br&gt;Platform admins shall NOT.&lt;br&gt;     Tenant admin with permission \&#39;taManageDevice\&#39; is validated based on the policy setting \&#39;scaffold.tenant_admin_to_tenant_resource_policy.device.manage\&#39;&lt;br&gt;     &lt;b&gt;self&lt;/b&gt;: Able to update devices belong to the tenant of current user.&lt;br&gt;     &lt;b&gt;cascade&lt;/b&gt;: Able to update devices belong to the tenant and sub-tenants of current user.
     * @param id Device ID
     * @param systemDeviceTelemetryValueRequestBody Please check &#x60;System.DeviceTelemetryValueRequestSchema&#x60;. Payload to method &#x60;DeviceMoldController::transmissionStrategy&#x60;
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public deviceControllerTelemetryValue(id: number, systemDeviceTelemetryValueRequestBody: SystemDeviceTelemetryValueRequestSchema, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<DeviceControllerTelemetryValue200Response>;
    public deviceControllerTelemetryValue(id: number, systemDeviceTelemetryValueRequestBody: SystemDeviceTelemetryValueRequestSchema, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<DeviceControllerTelemetryValue200Response>>;
    public deviceControllerTelemetryValue(id: number, systemDeviceTelemetryValueRequestBody: SystemDeviceTelemetryValueRequestSchema, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<DeviceControllerTelemetryValue200Response>>;
    public deviceControllerTelemetryValue(id: number, systemDeviceTelemetryValueRequestBody: SystemDeviceTelemetryValueRequestSchema, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<any> {
        if (id === null || id === undefined) {
            throw new Error('Required parameter id was null or undefined when calling deviceControllerTelemetryValue.');
        }
        if (systemDeviceTelemetryValueRequestBody === null || systemDeviceTelemetryValueRequestBody === undefined) {
            throw new Error('Required parameter systemDeviceTelemetryValueRequestBody was null or undefined when calling deviceControllerTelemetryValue.');
        }

        let localVarQueryParameters = new HttpParams({encoder: this.encoder});
        localVarQueryParameters = this.addToHttpParams(localVarQueryParameters,
          <any>systemDeviceTelemetryValueRequestBody, 'System.DeviceTelemetryValueRequestBody');

        let localVarHeaders = this.defaultHeaders;

        // authentication (api_http_auth) required
        localVarHeaders = this.configuration.addCredentialToHeaders('api_http_auth', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        const localVarHttpContext: HttpContext = options?.context ?? new HttpContext();

        const localVarTransferCache: boolean = options?.transferCache ?? true;


        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/system/device/${this.configuration.encodeParam({name: "id", value: id, in: "path", style: "simple", explode: false, dataType: "number", dataFormat: undefined})}/telemetry_value`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<DeviceControllerTelemetryValue200Response>('get', `${basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                params: localVarQueryParameters,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Update one Device
     * Update one Device by id&lt;br&gt; \&#39;device_mold_id\&#39;, \&#39;tenant_id\&#39; can not be updated.&lt;br&gt;     Super admin shall pass.&lt;br&gt;Platform admins shall NOT.&lt;br&gt;     Tenant admin with permission \&#39;taManageDevice\&#39; is validated based on the policy setting \&#39;scaffold.tenant_admin_to_tenant_resource_policy.device.manage\&#39;&lt;br&gt;     &lt;b&gt;self&lt;/b&gt;: Able to update devices belong to the tenant of current user.&lt;br&gt;     &lt;b&gt;cascade&lt;/b&gt;: Able to update devices belong to the tenant and sub-tenants of current user.
     * @param id Device id
     * @param systemDevice Payload to update Device
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public deviceControllerUpdate(id: number, systemDevice: SystemDevice, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: undefined, context?: HttpContext, transferCache?: boolean}): Observable<any>;
    public deviceControllerUpdate(id: number, systemDevice: SystemDevice, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: undefined, context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<any>>;
    public deviceControllerUpdate(id: number, systemDevice: SystemDevice, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: undefined, context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<any>>;
    public deviceControllerUpdate(id: number, systemDevice: SystemDevice, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: undefined, context?: HttpContext, transferCache?: boolean}): Observable<any> {
        if (id === null || id === undefined) {
            throw new Error('Required parameter id was null or undefined when calling deviceControllerUpdate.');
        }
        if (systemDevice === null || systemDevice === undefined) {
            throw new Error('Required parameter systemDevice was null or undefined when calling deviceControllerUpdate.');
        }

        let localVarHeaders = this.defaultHeaders;

        // authentication (api_http_auth) required
        localVarHeaders = this.configuration.addCredentialToHeaders('api_http_auth', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        const localVarHttpContext: HttpContext = options?.context ?? new HttpContext();

        const localVarTransferCache: boolean = options?.transferCache ?? true;


        // to determine the Content-Type header
        const consumes: string[] = [
            'application/json'
        ];
        const httpContentTypeSelected: string | undefined = this.configuration.selectHeaderContentType(consumes);
        if (httpContentTypeSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Content-Type', httpContentTypeSelected);
        }

        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/system/device/${this.configuration.encodeParam({name: "id", value: id, in: "path", style: "simple", explode: false, dataType: "number", dataFormat: undefined})}`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<any>('put', `${basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                body: systemDevice,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Update a property value by given device ID and property field ID.
     * Super admin shall pass.&lt;br&gt;Platform admins shall NOT.&lt;br&gt;     Tenant admin with permission \&#39;taManageDevice\&#39; is validated based on the policy setting \&#39;scaffold.tenant_admin_to_tenant_resource_policy.device.manage\&#39;&lt;br&gt;     &lt;b&gt;self&lt;/b&gt;: Able to update devices belong to the tenant of current user.&lt;br&gt;     &lt;b&gt;cascade&lt;/b&gt;: Able to update devices belong to the tenant and sub-tenants of current user.
     * @param id id
     * @param fieldId fieldId
     * @param systemDeviceUpdatePropertyFieldValueSchema Payload to update a property value by given device ID and property field ID.
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public deviceControllerUpdatePropertyFieldValue(id: number, fieldId: number, systemDeviceUpdatePropertyFieldValueSchema: SystemDeviceUpdatePropertyFieldValueSchema, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: undefined, context?: HttpContext, transferCache?: boolean}): Observable<any>;
    public deviceControllerUpdatePropertyFieldValue(id: number, fieldId: number, systemDeviceUpdatePropertyFieldValueSchema: SystemDeviceUpdatePropertyFieldValueSchema, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: undefined, context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<any>>;
    public deviceControllerUpdatePropertyFieldValue(id: number, fieldId: number, systemDeviceUpdatePropertyFieldValueSchema: SystemDeviceUpdatePropertyFieldValueSchema, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: undefined, context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<any>>;
    public deviceControllerUpdatePropertyFieldValue(id: number, fieldId: number, systemDeviceUpdatePropertyFieldValueSchema: SystemDeviceUpdatePropertyFieldValueSchema, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: undefined, context?: HttpContext, transferCache?: boolean}): Observable<any> {
        if (id === null || id === undefined) {
            throw new Error('Required parameter id was null or undefined when calling deviceControllerUpdatePropertyFieldValue.');
        }
        if (fieldId === null || fieldId === undefined) {
            throw new Error('Required parameter fieldId was null or undefined when calling deviceControllerUpdatePropertyFieldValue.');
        }
        if (systemDeviceUpdatePropertyFieldValueSchema === null || systemDeviceUpdatePropertyFieldValueSchema === undefined) {
            throw new Error('Required parameter systemDeviceUpdatePropertyFieldValueSchema was null or undefined when calling deviceControllerUpdatePropertyFieldValue.');
        }

        let localVarHeaders = this.defaultHeaders;

        // authentication (api_http_auth) required
        localVarHeaders = this.configuration.addCredentialToHeaders('api_http_auth', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        const localVarHttpContext: HttpContext = options?.context ?? new HttpContext();

        const localVarTransferCache: boolean = options?.transferCache ?? true;


        // to determine the Content-Type header
        const consumes: string[] = [
            'application/json'
        ];
        const httpContentTypeSelected: string | undefined = this.configuration.selectHeaderContentType(consumes);
        if (httpContentTypeSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Content-Type', httpContentTypeSelected);
        }

        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/system/device/${this.configuration.encodeParam({name: "id", value: id, in: "path", style: "simple", explode: false, dataType: "number", dataFormat: undefined})}/property_field/${this.configuration.encodeParam({name: "fieldId", value: fieldId, in: "path", style: "simple", explode: false, dataType: "number", dataFormat: undefined})}`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<any>('put', `${basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                body: systemDeviceUpdatePropertyFieldValueSchema,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * Upload device image by identifier
     * Upload device image by identifier in one step. This method combines file upload with device association.
     * @param identifier Device identifier
     * @param image Device image file
     * @param description Optional image description
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public deviceControllerUploadImageByIdentifier(identifier: string, image: Blob, description?: string, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<BaseStoreResponse200>;
    public deviceControllerUploadImageByIdentifier(identifier: string, image: Blob, description?: string, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<BaseStoreResponse200>>;
    public deviceControllerUploadImageByIdentifier(identifier: string, image: Blob, description?: string, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<BaseStoreResponse200>>;
    public deviceControllerUploadImageByIdentifier(identifier: string, image: Blob, description?: string, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<any> {
        if (identifier === null || identifier === undefined) {
            throw new Error('Required parameter identifier was null or undefined when calling deviceControllerUploadImageByIdentifier.');
        }
        if (image === null || image === undefined) {
            throw new Error('Required parameter image was null or undefined when calling deviceControllerUploadImageByIdentifier.');
        }

        let localVarHeaders = this.defaultHeaders;

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        const localVarHttpContext: HttpContext = options?.context ?? new HttpContext();

        const localVarTransferCache: boolean = options?.transferCache ?? true;

        // to determine the Content-Type header
        const consumes: string[] = [
            'multipart/form-data'
        ];

        const canConsumeForm = this.canConsumeForm(consumes);

        let localVarFormParams: { append(param: string, value: any): any; };
        let localVarUseForm = false;
        let localVarConvertFormParamsToString = false;
        // use FormData to transmit files using content-type "multipart/form-data"
        // see https://stackoverflow.com/questions/4007969/application-x-www-form-urlencoded-or-multipart-form-data
        localVarUseForm = canConsumeForm;
        if (localVarUseForm) {
            localVarFormParams = new FormData();
        } else {
            localVarFormParams = new HttpParams({encoder: this.encoder});
        }

        if (identifier !== undefined) {
            localVarFormParams = localVarFormParams.append('identifier', <any>identifier) as any || localVarFormParams;
        }
        if (image !== undefined) {
            localVarFormParams = localVarFormParams.append('image', <any>image) as any || localVarFormParams;
        }
        if (description !== undefined) {
            localVarFormParams = localVarFormParams.append('description', <any>description) as any || localVarFormParams;
        }

        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/system/device/upload_image_by_identifier`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<BaseStoreResponse200>('post', `${basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                body: localVarConvertFormParamsToString ? localVarFormParams.toString() : localVarFormParams,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

}
