export * from './default.service';
import { DefaultService } from './default.service';
export * from './systemAccount.service';
import { SystemAccountService } from './systemAccount.service';
export * from './systemActivityLog.service';
import { SystemActivityLogService } from './systemActivityLog.service';
export * from './systemAlarmLog.service';
import { SystemAlarmLogService } from './systemAlarmLog.service';
export * from './systemAuth.service';
import { SystemAuthService } from './systemAuth.service';
export * from './systemBuilding.service';
import { SystemBuildingService } from './systemBuilding.service';
export * from './systemChannelType.service';
import { SystemChannelTypeService } from './systemChannelType.service';
export * from './systemCommonFieldDescriptions.service';
import { SystemCommonFieldDescriptionsService } from './systemCommonFieldDescriptions.service';
export * from './systemDevice.service';
import { SystemDeviceService } from './systemDevice.service';
export * from './systemDeviceAction.service';
import { SystemDeviceActionService } from './systemDeviceAction.service';
export * from './systemDeviceCalibrationRecord.service';
import { SystemDeviceCalibrationRecordService } from './systemDeviceCalibrationRecord.service';
export * from './systemDeviceMold.service';
import { SystemDeviceMoldService } from './systemDeviceMold.service';
export * from './systemDeviceOnlineRecord.service';
import { SystemDeviceOnlineRecordService } from './systemDeviceOnlineRecord.service';
export * from './systemDevicePropertyField.service';
import { SystemDevicePropertyFieldService } from './systemDevicePropertyField.service';
export * from './systemDevicePropertyValue.service';
import { SystemDevicePropertyValueService } from './systemDevicePropertyValue.service';
export * from './systemDeviceTelemetryField.service';
import { SystemDeviceTelemetryFieldService } from './systemDeviceTelemetryField.service';
export * from './systemDeviceType.service';
import { SystemDeviceTypeService } from './systemDeviceType.service';
export * from './systemDeviceTypeCommonField.service';
import { SystemDeviceTypeCommonFieldService } from './systemDeviceTypeCommonField.service';
export * from './systemEmailCode.service';
import { SystemEmailCodeService } from './systemEmailCode.service';
export * from './systemEvent.service';
import { SystemEventService } from './systemEvent.service';
export * from './systemExport.service';
import { SystemExportService } from './systemExport.service';
export * from './systemMember.service';
import { SystemMemberService } from './systemMember.service';
export * from './systemMemberData.service';
import { SystemMemberDataService } from './systemMemberData.service';
export * from './systemMemberDeviceUsage.service';
import { SystemMemberDeviceUsageService } from './systemMemberDeviceUsage.service';
export * from './systemNotification.service';
import { SystemNotificationService } from './systemNotification.service';
export * from './systemOperatorDeviceUsages.service';
import { SystemOperatorDeviceUsagesService } from './systemOperatorDeviceUsages.service';
export * from './systemPermission.service';
import { SystemPermissionService } from './systemPermission.service';
export * from './systemPinCodes.service';
import { SystemPinCodesService } from './systemPinCodes.service';
export * from './systemPlatformAdmin.service';
import { SystemPlatformAdminService } from './systemPlatformAdmin.service';
export * from './systemRegister.service';
import { SystemRegisterService } from './systemRegister.service';
export * from './systemReportPlan.service';
import { SystemReportPlanService } from './systemReportPlan.service';
export * from './systemRole.service';
import { SystemRoleService } from './systemRole.service';
export * from './systemRule.service';
import { SystemRuleService } from './systemRule.service';
export * from './systemRuleAction.service';
import { SystemRuleActionService } from './systemRuleAction.service';
export * from './systemRuleTemplate.service';
import { SystemRuleTemplateService } from './systemRuleTemplate.service';
export * from './systemRuleTemplateAction.service';
import { SystemRuleTemplateActionService } from './systemRuleTemplateAction.service';
export * from './systemRuleTemplateCriteria.service';
import { SystemRuleTemplateCriteriaService } from './systemRuleTemplateCriteria.service';
export * from './systemSetting.service';
import { SystemSettingService } from './systemSetting.service';
export * from './systemSmsCode.service';
import { SystemSmsCodeService } from './systemSmsCode.service';
export * from './systemSpace.service';
import { SystemSpaceService } from './systemSpace.service';
export * from './systemStrategy.service';
import { SystemStrategyService } from './systemStrategy.service';
export * from './systemStrategyTemplate.service';
import { SystemStrategyTemplateService } from './systemStrategyTemplate.service';
export * from './systemStrategyTemplateCategory.service';
import { SystemStrategyTemplateCategoryService } from './systemStrategyTemplateCategory.service';
export * from './systemStreaming.service';
import { SystemStreamingService } from './systemStreaming.service';
export * from './systemTeam.service';
import { SystemTeamService } from './systemTeam.service';
export * from './systemTelemetryValue.service';
import { SystemTelemetryValueService } from './systemTelemetryValue.service';
export * from './systemTelemetryValueCommon.service';
import { SystemTelemetryValueCommonService } from './systemTelemetryValueCommon.service';
export * from './systemTelemetryValueMapping.service';
import { SystemTelemetryValueMappingService } from './systemTelemetryValueMapping.service';
export * from './systemTenant.service';
import { SystemTenantService } from './systemTenant.service';
export * from './systemTenantAdmin.service';
import { SystemTenantAdminService } from './systemTenantAdmin.service';
export * from './systemTenantOperatorEvacuated.service';
import { SystemTenantOperatorEvacuatedService } from './systemTenantOperatorEvacuated.service';
export * from './systemThirdParty.service';
import { SystemThirdPartyService } from './systemThirdParty.service';
export * from './systemUpload.service';
import { SystemUploadService } from './systemUpload.service';
export * from './systemVersion.service';
import { SystemVersionService } from './systemVersion.service';
export * from './systemWatchPoint.service';
import { SystemWatchPointService } from './systemWatchPoint.service';
export const APIS = [DefaultService, SystemAccountService, SystemActivityLogService, SystemAlarmLogService, SystemAuthService, SystemBuildingService, SystemChannelTypeService, SystemCommonFieldDescriptionsService, SystemDeviceService, SystemDeviceActionService, SystemDeviceCalibrationRecordService, SystemDeviceMoldService, SystemDeviceOnlineRecordService, SystemDevicePropertyFieldService, SystemDevicePropertyValueService, SystemDeviceTelemetryFieldService, SystemDeviceTypeService, SystemDeviceTypeCommonFieldService, SystemEmailCodeService, SystemEventService, SystemExportService, SystemMemberService, SystemMemberDataService, SystemMemberDeviceUsageService, SystemNotificationService, SystemOperatorDeviceUsagesService, SystemPermissionService, SystemPinCodesService, SystemPlatformAdminService, SystemRegisterService, SystemReportPlanService, SystemRoleService, SystemRuleService, SystemRuleActionService, SystemRuleTemplateService, SystemRuleTemplateActionService, SystemRuleTemplateCriteriaService, SystemSettingService, SystemSmsCodeService, SystemSpaceService, SystemStrategyService, SystemStrategyTemplateService, SystemStrategyTemplateCategoryService, SystemStreamingService, SystemTeamService, SystemTelemetryValueService, SystemTelemetryValueCommonService, SystemTelemetryValueMappingService, SystemTenantService, SystemTenantAdminService, SystemTenantOperatorEvacuatedService, SystemThirdPartyService, SystemUploadService, SystemVersionService, SystemWatchPointService];
