<div class="more-setting-container" [odxLoadingSpinner]="loading">
  <div class="page-content">
    <div class="header">
      <div class="title">{{ title | translate }}</div>
    </div>
    <odx-content-box style="height: 100%;" class="content-box">
      <nz-tabset  [(nzSelectedIndex)]="selectedIndex" [nzTabPosition]="'left'" (nzSelectChange)="selectChange()">
        <nz-tab [nzTitle]="'setting.account' | translate">
          <div class="system-setting">
            <form class="form-item" [formGroup]="memberForm">
              <odx-form-field [label]="'app.login.userName' | translate">
                <input formControlName="name" [placeholder]="'app.input-placeholder' | translate"
                odxFormFieldControl [maxlength]="20" type="text" />
                <ng-template odxFormFieldError="required" let-context>
                  {{ "app.login.required.error" | translate }}
                </ng-template>
              </odx-form-field>
              <odx-form-field [label]="'app.phone' | translate">
                <input formControlName="phone" [placeholder]="'app.input-placeholder' | translate"
                odxFormFieldControl [maxlength]="20" type="text" />
                <ng-template odxFormFieldError="required" let-context>
                  {{ "app.login.required.error" | translate }}
                </ng-template>
              </odx-form-field>
              <odx-form-field [label]="'app.email' | translate">
                <input formControlName="email" [placeholder]="'app.input-placeholder' | translate"
                odxFormFieldControl [maxlength]="20" type="text" />
                <ng-template odxFormFieldError="required" let-context>
                  {{ "app.login.required.error" | translate }}
                </ng-template>
              </odx-form-field>
            </form>
          </div>
          <button
            [odxLoadingSpinner]="loading"
            odxButton
            variant="primary"
            (click)="submit()"
          >
            {{ "confirm" | translate }}
          </button>
        </nz-tab>
        <nz-tab [nzTitle]="'app.login.password.placeholder' | translate">
          <div class="system-setting">
            <form class="form-item" [formGroup]="memberPasswordForm">
              <odx-form-field [label]="'app.login.newPassword' | translate">
                <input formControlName="password" [placeholder]="'app.input-placeholder' | translate"
                odxFormFieldControl [maxlength]="20" type="password" />
                <ng-template odxFormFieldError="required" let-context>
                  {{ "app.login.required.error" | translate }}
                </ng-template>
              </odx-form-field>
              <odx-form-field [label]="'app.login.confirmPassword' | translate">
                <input formControlName="confirmPassword" [placeholder]="'app.input-placeholder' | translate"
                odxFormFieldControl [maxlength]="20" type="password" />
                <ng-template odxFormFieldError="required" let-context>
                  {{ "app.login.required.error" | translate }}
                </ng-template>
              </odx-form-field>
              <ng-container
                *ngIf="memberPasswordForm.controls['confirmPassword'].errors?.['notEqual']"
              >
                <div class="error-info"> 
                  {{ "app.login.confirmPassword.error" | translate }}
                </div>
              </ng-container>
            </form>
          </div>
          <button
            [odxLoadingSpinner]="loading"
            odxButton
            variant="primary"
            (click)="submit('password')"
          >
            {{ "confirm" | translate }}
        </button>
        </nz-tab>
        <nz-tab [nzTitle]="'setting.systemSetting' | translate">
          <div class="system-setting">
            <odx-form-field [label]="'common.language' | translate">
              <odx-select [(ngModel)]="language" (ngModelChange)="selectLang()">
                <odx-select-option *ngFor="let item of langConfigs" [value]="item.name">
                  {{ 'setting.' + item.name | translate}}
                </odx-select-option>
              </odx-select>
            </odx-form-field>
            <odx-form-field [label]="'setting.themeChange' | translate">
              <odx-select [(ngModel)]="theme" (ngModelChange)="toggleThemeChange()">
                <odx-select-option *ngFor="let item of themConfig" [value]="item.value">
                  {{ 'setting.' + item.name | translate}}
                </odx-select-option>
              </odx-select>
            </odx-form-field>
            <odx-form-field [label]="'setting.version' | translate">
              <span class="version-item">{{version}}</span>
            </odx-form-field>
          </div>
        </nz-tab>
        <!-- <nz-tab [nzTitle]="'common.help' | translate">Content of Tab Pane 3</nz-tab> -->
      </nz-tabset>
      <button class="logout-item" odxButton (click)="logout()" variant="ghost">
        <odx-icon name="arrow-right" size="small" class="mr-10" alignRight></odx-icon>
        <span>{{'app.logout'|translate}}</span> 
      </button>
    </odx-content-box >
  </div>
</div>