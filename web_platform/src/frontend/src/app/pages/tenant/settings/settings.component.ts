
import { Component } from '@angular/core';
import { SettingsService, LangConfig } from '@services/settings.service';
import { Router, ActivatedRoute } from '@angular/router';
import { UtilService } from '@services/util.service';
import { ModalService } from '@odx/angular/components/modal';
import { CurrentUserService } from '@services/current-user.service';
import { Theme, ThemingService } from '@odx/angular/theming';
import {
  FormGroup,
  FormControl,
  Validators,
  ValidationErrors,
  ValidatorFn,
  AbstractControl
} from '@angular/forms';
import { SystemAccountService, SystemTenantAdminService, SystemTenantAdminUpdateSchema} from 'src/app/api/backend';
import { OdxToastCustomService } from '@services/odx-toast-custom.service';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-settings',
  templateUrl: './settings.component.html',
  styleUrl: './settings.component.scss'
})
export class SettingsComponent {
  public langConfigs: LangConfig[];
  public title: string = '';
  public language: string | undefined = '';
  public theme: string = '';
  public version: string = 'V1.0.1'
  public loading: boolean = false;
  public currentLanguage: string | undefined = '';
  public selectedIndex: number = 0;
  public themConfig = [
    {
      name: 'darkMode',
      value: 'dark'
    },
    {
      name: 'lightMode',
      value: 'light'
    }
  ]
  memberForm: FormGroup = new FormGroup({
    name: new FormControl(null, [Validators.required]),
    phone: new FormControl(null, [Validators.required]),
    email: new FormControl(null, [Validators.required])
  });
  memberPasswordForm: FormGroup = new FormGroup({
    password: new FormControl(null, [Validators.required]),
    confirmPassword: new FormControl(null, [Validators.required, this.confirmValidator()])
  });
  constructor(
    private settingsService: SettingsService,
    private route: ActivatedRoute,
    private utilService: UtilService,
    private router: Router,
    private modalService: ModalService,
    private currentService: CurrentUserService,
    public themingService: ThemingService,
    private systemAccountService: SystemAccountService,
    private msgSrv: OdxToastCustomService,
    private translateService: TranslateService,
    private systemTenantAdminService: SystemTenantAdminService
  ) {
    this.title = this.route.snapshot.data['titleI18n'];
    this.langConfigs = settingsService.langConfigs;
    this.language = settingsService.langConfigs.find(lang => lang.name === settingsService.lang)?.name
    this.currentLanguage = this.language;
    this.theme = this.themingService.getSelectedTheme();
    this.utilService.changeShareData({ pageTitle: this.route.snapshot.data['titleI18n'] });
  }
  ngOnInit() {
    this.selectedIndex = localStorage.getItem('setting-page-selected-index') ? Number(localStorage.getItem('setting-page-selected-index')) : 0;
    let user = this.settingsService.User
    console.log(user)
    this.memberForm.reset({
      name: user.name,
      phone: user.accountable?.phone,
      email: user.email,
    })
  }

  public submit(type = 'account') {
    console.log(type)
    console.log(this.settingsService.User)
    if( type == 'account') {
      const formValue = this.memberForm.value;
      // let request = [];
      this.memberForm.markAllAsTouched()
      if (!this.memberForm.valid) {
        return;
      }
      const params: SystemTenantAdminUpdateSchema = {
        name: formValue.name,
        phone: formValue.phone,
        email: formValue.email
      }
      this.systemTenantAdminService.tenantAdminControllerUpdate(this.settingsService.User.accountable?.id ?? 0, params).subscribe(res => {
        if (res.success) {
          this.translateService.get('success.save').subscribe((msg) => {
            this.msgSrv.success(msg);
            let user = this.settingsService.User
            user.name = formValue.name
            user.accountable?.phone = formValue.phone
            user.email = formValue.email
            this.settingsService.setUser(user)
          });
        }
      })
    } else {
      const formValue = this.memberPasswordForm.value;
      // let request = [];
      this.memberPasswordForm.markAllAsTouched()
      if (!this.memberPasswordForm.valid) {
        return;
      }
      this.systemAccountService.accountControllerPassword({
        password: formValue.password
      }).subscribe(res => {
        if (res.success) {
          this.translateService.get('success.save').subscribe((msg) => {
            this.msgSrv.success(msg);
          });
          this.memberPasswordForm.reset();
        }
      })
    }
  }

    // 校验确认密码
   public confirmValidator(): ValidatorFn {
      return (control: AbstractControl): ValidationErrors | null => {
        if (!control.value) {
          return { required: true, error: true, };
        } else if (control.value !== this.memberPasswordForm.value.password) {
          return { notEqual: true, error: true };
        }
        return {};
      };
    }

  public toggleThemeChange(): void {
    let currentTheme;
    if (this.theme === 'light') {
      currentTheme = Theme.LIGHT;
    } else {
      currentTheme = Theme.DARK;
    }
    // 主题设置应用选择的主题（odx的theme）
    this.themingService.selectTheme(currentTheme);
    this.refreshPage()
  }

  public selectChange() {
    localStorage.setItem('setting-page-selected-index', String(this.selectedIndex))
  }
  public selectLang(): void {
    if (this.language != this.currentLanguage && this.language) {
      this.settingsService.setLanguage(this.language)
      this.currentLanguage = this.language
      this.refreshPage()
    }
  }

  public refreshPage(): void {
    // header点击切换主题，切换系统语言后，页面显示loading状态，重新加载
    this.loading = true;
    setTimeout(()=>{
      this.loading = false;
    },2000)
  }
  // 用户退出登录
  public logout(): void {
    this.currentService.afterLogout();
    // 跳转到用户登录页
    this.router.navigate(['/passport/login']);
  }
}
