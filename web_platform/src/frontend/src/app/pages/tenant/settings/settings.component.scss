@import "../../../../styles.scss";

.more-setting-container {
  .header {
    padding: 0 $marginBase;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .title {
    color: var(--odx-c-primary);
    font-weight: bolder;
    height: calc($marginBase * 72 / 24);
    line-height: calc($marginBase * 72 / 24);
    font-family: var(--semiBoldFont);
  }
  ::ng-deep .ant-tabs {
    background-color: var(--table-background-color);
    padding: calc($marginBase * 24 / 24);
    height: calc(100vh - $marginBase * 144 / 24);
    .ant-tabs-nav {
      width: 30%;
    }
    .ant-tabs-tab {
      margin-right: calc($marginBase * 10 / 24);
    }
    .ant-tabs-tab-active  {
      background-color: #E9EEF4;
      .ant-tabs-tab-btn {
        font-family: var(--semiBoldFont);
        color: var(--odx-c-primary);
        font-weight: 600;
      }
    }
    .ant-tabs-ink-bar {
      background: none;
    }
    .ant-tabs-content, .ant-tabs-tabpane {
      height: 100%;
      text-align: right;
    }
  }
  .system-setting {
    height: calc(100% - $marginBase * 60 / 24);
    width: 80%;
    margin: 0 auto;
    ::ng-deep .odx-select  {
      width: 100%;
      padding: calc($marginBase * 5 / 24) calc($marginBase * 8 / 24);
     
    }
    ::ng-deep .odx-select-option  {
      text-align: left;
    }
    .version-item {
      padding: calc($marginBase * 5 / 24) calc($marginBase * 8 / 24);
      line-height: calc($marginBase * 30 / 24);
    }
    .error-info {
      font-size: calc($baseFont * 13/24);
      color: var(--odx-input-control-color-error);;
      margin-left: $indent-10;
    }
  }
  .logout-item {
    position: absolute;
    bottom: calc($marginBase * 48 / 24);
    left: calc($marginBase * 24 / 24);
    cursor: pointer;
    color: var(--odx-c-ghost-text)
  }
}