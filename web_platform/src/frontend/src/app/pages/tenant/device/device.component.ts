import { Component, ViewChild, TemplateRef } from '@angular/core';
import { SettingsService, User } from '@services/settings.service';
import { Router, ActivatedRoute } from '@angular/router';
import { UtilService } from '@services/util.service';
import { ContextService } from '@services/context.service';
import { DeviceEditComponent } from '../../../shared/components/device-edit/device-edit.component';
import { ModalService } from '@odx/angular/components/modal';
import { DataItem } from '../../../shared/components/stack-bar/stack-bar.component';
import { Data } from 'src/app/shared/components/right-pop-up/right-pop-up.component';
import { ModalRef } from '@odx/angular/components/modal';
import { TranslateService } from '@ngx-translate/core';
import { ImportActionComponent } from 'src/app/shared/components/import-action/import-action.component';
import { OdxToastCustomService } from '@services/odx-toast-custom.service';

@Component({
  selector: 'app-device',
  templateUrl: './device.component.html',
  styleUrl: './device.component.scss'
})
export class DeviceComponent {
  @ViewChild('reportFaultTpl') reportFaultTpl!: TemplateRef<any>;
  private reportFaultModal?: ModalRef<Data, boolean>;
  public modalLoading?: boolean;
  public typeList: string[] = ['all'];
  public total: any = {
    all: 0,
    pam: 0,
    ba: 0,
    tic: 0,
    fixed: 0,
    controller: 0,
    ttt: 0,
    other: 0
  };
  public deviceSummaryList: summaryData[] = [
    {
      name: 'PAM',
      icon: 'devices',
      total: 90,
      items: [
        {
          name: 'Xam-2000(10units)',
          value: 10
        },
        {
          name: 'Xam-2800 (30units)',
          value: 30
        },
        {
          name: 'Pam-2000 (50units)',
          value: 30
        },
      ]
    },
    {
      name: 'BA',
      icon: 'scba',
      total: 26,
      items: [
        {
          name: 'BG pro air(2units)',
          value: 2
        },
        {
          name: 'BG (4units)',
          value: 4
        },
        {
          name: 'Air boss (10units)',
          value: 10
        },
        {
          name: 'Pass-3600 (10units)',
          value: 10
        }
      ]
    },
    {
      name: 'TIC',
      icon: 'ucf-6000-9000',
      total: 26,
      items: [
        {
          name: 'BG pro air(2units)',
          value: 2
        },
        {
          name: 'BG (4units)',
          value: 4
        },
        {
          name: 'Air boss (10units)',
          value: 10
        },
        {
          name: 'Pass-3600 (10units)',
          value: 10
        }
      ]
    },
    {
      name: 'Fixed',
      icon: 'polytron-7000',
      total: 24,
      items: [
        {
          name: 'Pass-3600 (10units)',
          value: 10
        },
        {
          name: 'Polytron(10units)',
          value: 10
        },
        {
          name: 'BG pro air (2units)',
          value: 2
        },
        {
          name: 'Pam-2000 (50units)',
          value: 2
        }
      ]
    },
    {
      name: 'Controller',
      icon: 'controlbox',
      total: 5,
      items: [
        {
          name: 'Regard 3000 (1units)',
          value: 1
        },
        {
          name: 'Regard 7000(1units)',
          value: 1
        },
        {
          name: 'Iot 4G  (3units)',
          value: 3
        },
      ]
    },
    {
      name: 'TTT',
      icon: 'communication-tool',
      total: 100,
      items: [
        {
          name: 'TTT (100units)',
          value: 100
        }
      ]
    },
    {
      name: 'Smart Hub',
      icon: 'bodyguard',
      total: 9,
      items: [
        {
          name: 'Smart Hub (9units)',
          value: 9
        }
      ]
    }
  ]
  createTime: any[] = [];
  createBtnContent: string = 'teams.add';
  title: string;
  showDeviceSummary: boolean = false;
  refreshMemberTrigger: number = 0;
  constructor(
    private settingsService: SettingsService,
    private route: ActivatedRoute,
    private utilService: UtilService,
    private router: Router,
    private modalService: ModalService,
    private translateService: TranslateService,
    private msgSrv: OdxToastCustomService,
    private contextService: ContextService
    
  ) {
    this.title = this.route.snapshot.data['titleI18n'];
    this.utilService.changeShareData({ pageTitle: this.title });
  }
  ngOnInit() {
    this.contextService.getDeviceTypes();
  }
  public changeList(type: string) {
    if (type != 'all') {
      this.typeList = this.typeList.filter(item => item !== 'all');
      if (this.typeList.indexOf(type) === -1) {
        this.typeList.push(type);
      } else if (this.typeList.length > 1 ) {
        this.typeList = this.typeList.filter(item => item !== type);
      }
    } else {
      this.typeList = ['all'];
    }
    // this.typeList = [type];
    console.log(this.typeList)
  }

  public selectedChange(event: any, field: string | null) {
    let start = null;
    let end = null;
  }

  public deviceSummary() {
    this.showDeviceSummary = !this.showDeviceSummary;
  }
  public create() {
    let createModal = this.modalService.open(DeviceEditComponent, {
        data:  {
          deviceIds: [],
          deviceType: this.typeList?.length ? this.typeList[0] : null
        },
        size: 'small'
      } 
    );
    createModal.onClose$.subscribe((res) => {
      if (res) {
        this.refreshMemberTrigger++;
      }
    });
  }
   // 显示ReportFaultModal
  public showReportFaultModal() {
    const data: Data = {
      name: this.translateService.instant('device.reportFault'),
      type: '',
      userCount: 0,
      deviceCount: 0,
      content: this.reportFaultTpl,
      customClass: '',
      customHeaderClass: '',
    };

    if (this.reportFaultModal?.isActive()) {
      this.modalLoading = true;
      this.reportFaultModal.update({ data });
      setTimeout(() => {
        this.modalLoading = false;
      }, 500);
    } else {
      // 报错代码--todo
      this.reportFaultModal = this.utilService.openCenterPopup(
        data,
        'report-a-fault'
      );
    }
  }
  
  public importDevice() {
    if (this.typeList.length == 1) {
      const importController = this.modalService.open(ImportActionComponent, {
        data: { templateName: this.typeList[0] + '_template' },
       });
       importController.onClose$.subscribe((res) => {
        if (res) {
          this.changeList(this.typeList[0]);
          this.msgSrv.success(this.translateService.instant('common.importSuccess'));
        }
      });
    }
  }
}
export type summaryData = {
  name: string,
  icon: string,
  total: number,
  items: DataItem[]
};

