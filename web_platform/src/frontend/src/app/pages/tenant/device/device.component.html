<div class="device-list-container page-with-padding">
  <div class="page-content">
    <div class="header">
      <div class="title">{{ title | translate }}</div>
      <div class="device-data" (click)="deviceSummary()">
        <app-icon class="action-icon" *ngIf="!showDeviceSummary" name="chevron-down"></app-icon>
        <app-icon class="action-icon" *ngIf="showDeviceSummary" name="chevron-up"></app-icon>
        {{ 'device.headerTitle' | translate }}
      </div>
    </div>
    <div *ngIf="showDeviceSummary">
      <div class="device-summary-item" *ngFor="let deviceSummary of deviceSummaryList">
        <div>
          <app-icon type="built-in" [name]="deviceSummary.icon" iconSet="safety"></app-icon>
          {{deviceSummary.name}} ({{deviceSummary.total}})
        </div>
        <div class="chart-item">
          <stack-bar [data]="deviceSummary"></stack-bar>
        </div>
      </div>
    </div>
    <div class="header-item">
      <div class="left-item">
        <button [ngClass]="{'action-item':true, 'active-btn': typeList.indexOf('all') !== -1}" odxButton
          (click)="changeList('all')">
          {{ 'device.all' | translate }} ({{ total.all}})
        </button>
        <button [ngClass]="{'action-item':true, 'active-btn': typeList.indexOf('pam') !== -1}" odxButton
          (click)="changeList('pam')">
          <app-icon type="built-in" name="devices" iconSet="safety"></app-icon> {{ 'device.pam' | translate }} ({{
          total.pam}})
        </button>
        <button [ngClass]="{'action-item':true, 'active-btn': typeList.indexOf('ba') !== -1}" odxButton
          (click)="changeList('ba')">
          <app-icon type="built-in" name="scba" iconSet="safety"></app-icon> {{ 'device.ba' | translate }} ({{
          total.ba}})
        </button>
        <button [ngClass]="{'action-item':true, 'active-btn': typeList.indexOf('tic') !== -1}" odxButton
          (click)="changeList('tic')">
          <app-icon type="built-in" name="ucf-6000-9000" iconSet="safety"></app-icon> {{ 'device.tic' | translate }} ({{
          total.tic}})
        </button>
        <button [ngClass]="{'action-item':true, 'active-btn': typeList.indexOf('fixed') !== -1}" odxButton
          (click)="changeList('fixed')">
          <app-icon type="built-in" name="polytron-7000" iconSet="safety"></app-icon> {{ 'device.fixed' | translate }}
          ({{ total.fixed}})
        </button>
        <button [ngClass]="{'action-item':true, 'active-btn': typeList.indexOf('controller') !== -1}" odxButton
          (click)="changeList('controller')">
          <app-icon type="built-in" name="controlbox" iconSet="safety"></app-icon> {{ 'device.controller' | translate }}
          ({{ total.controller}})
        </button>
        <button [ngClass]="{'action-item':true, 'active-btn': typeList.indexOf('ttt') !== -1}" odxButton
          (click)="changeList('ttt')">
          <app-icon type="built-in" name="communication-tool" iconSet="safety"></app-icon> {{ 'device.WirelessModule' | translate
          }} ({{ total.ttt}})
        </button>
        <button [ngClass]="{'action-item':true, 'active-btn': typeList.indexOf('other') !== -1}" odxButton
          (click)="changeList('other')">
          <app-icon type="built-in" name="dashboard" iconSet="core"></app-icon> {{ 'device.other' | translate }} ({{
          total.other}})
        </button>
      </div>
      <div class="right-item">
        <div class="date-filter-item">
          <span>{{ 'teams.create_time' | translate}}:</span>
          <nz-range-picker [(ngModel)]="createTime" (ngModelChange)="selectedChange($event, 'next_calibration')"
            nzFormat="yyyy-MM-dd"></nz-range-picker>
        </div>
        <app-icon (click)="importDevice()" class="action-icon" *ngIf="typeList.length == 1 && (typeList.indexOf('fixed') !== -1 || typeList.indexOf('ttt') !== -1)" type="built-in"
          name="upload" iconSet="core"></app-icon>
        <app-icon (click)="showReportFaultModal()" class="action-icon" type="built-in" name="config" iconSet="core"></app-icon>
        <button *ngIf="typeList.length == 1 && (typeList.indexOf('other') !== -1 || typeList.indexOf('controller') !== -1 || typeList.indexOf('ttt') !== -1 || typeList.indexOf('tic') !== -1)" class="action-item"
          odxButton variant="primary" (click)="create()">
          <odx-icon name="plus" alignLeft></odx-icon>
          {{ 'device.addDevice' | translate }}
        </button>
      </div>
    </div>
    <device-list [typeList]="typeList" [refreshTrigger]="refreshMemberTrigger"></device-list>
  </div>
</div>
<ng-template #reportFaultTpl>
  <div class="report-fault-image">
    <p>{{"device.reportFaultDesc" | translate}}</p>
    <img class="img" src="../../../../assets/img/reportFault.png">
  </div>
</ng-template>