import { Component } from '@angular/core';
import { SettingsService, User } from '@services/settings.service';
import { Router, ActivatedRoute } from '@angular/router';
import { UtilService } from '@services/util.service';
import { TeamEditComponent } from '../../../shared/components/team-edit/team-edit.component';
import { ModalService } from '@odx/angular/components/modal';
import { MemberEditComponent } from '../../../shared/components/member-edit/member-edit.component';
import { ImportActionComponent } from 'src/app/shared/components/import-action/import-action.component';
import { OdxToastCustomService } from '@services/odx-toast-custom.service';
import { TranslateService } from '@ngx-translate/core';
import { ContextService } from '@services/context.service';
@Component({
  selector: 'app-teams',
  templateUrl: './teams.component.html',
  styleUrl: './teams.component.scss'
})
export class TeamsComponent {
  public type: string = 'teams';

  createTime: any[] = [];
  start: any;
  end: any;
  createBtnContent: string = 'teams.add';
  refreshTeamTrigger: number = 0;
  refreshMemberTrigger: number = 0;

  constructor(
    private settingsService: SettingsService,
    private route: ActivatedRoute,
    private utilService: UtilService,
    private router: Router,
    private modalService: ModalService,
    private msgSrv: OdxToastCustomService,
    private translateService: TranslateService,
    private contextService: ContextService,
  ) {

  }
  ngOnInit() {
    this.route.params.subscribe(params => {
      this.type = params['type'];
      // 根据不同的 type 加载不同的内容
      if (this.type === 'group') {
        // 加载管理员相关内容
      } else {
        // 加载普通用户相关内容
      }
    });
  }
  public changeList(type: string) {
    this.start = null;
    this.end = null;
    this.createTime = [];
    if (type === 'group') {
      this.router.navigate(['tenant/teams/group']);
    } else {
      this.router.navigate(['tenant/teams/member']);
    }
  }

  public selectedChange(event: any) {
    this.start = event && event.length ? event[0] : null;
    this.end = event && event.length ? event[1] : null;
  }

  public create() {
    if (this.type === 'group') {
      let createModal = this.modalService.open(TeamEditComponent, {
        data: null,
        size: 'small'
      },
      );
      createModal.onClose$.subscribe((res) => {
        if (res) {
          // 刷新团队选项和列表
          this.contextService.getTeamOptions();
          this.refreshTeamTrigger++;
        }
      });
    } else {
      let createModal = this.modalService.open(MemberEditComponent, {
        data: null,
        size: 'small'
      });
      createModal.onClose$.subscribe((res) => {
        if (res) {
          this.refreshMemberTrigger++;
        }
      });
    }
  }
  public importAction() {
    const importController = this.modalService.open(ImportActionComponent, {
      data: { templateName: this.type ? 'team_template' : 'member_template' },
    });
    importController.onClose$.subscribe((res) => {
      if (res) {
        this.changeList(this.type);
        this.msgSrv.success(this.translateService.instant('common.importSuccess'));
      }
    });
  }
}
