<div class="teams-list-container page-with-padding">
  <div class="page-content">
   <app-new-content-header [showTitle]="true"></app-new-content-header>
   <div class="header-item">
      <div class="left-item">
        <button [ngClass]="{'action-item':true, 'active-btn': type === 'group'}" odxButton (click)="changeList('group')">
          {{ 'teams.team' | translate }}
        </button>
        <button [ngClass]="{'action-item':true, 'active-btn': type === 'member'}" odxButton (click)="changeList('member')">
          {{ 'teams.member' | translate }}
        </button>
      </div>
      <div class="right-item">
        <div class="date-filter-item">
          <span>{{ 'teams.create_time' | translate}}:</span>
          <nz-range-picker  [(ngModel)]="createTime" (ngModelChange)="selectedChange($event)" nzFormat="yyyy-MM-dd"></nz-range-picker>
        </div>
         <app-icon (click)="importAction()" class="action-icon"type="built-in"
          name="upload" iconSet="core"></app-icon>

        <button class="action-item" odxButton variant="primary" (click)="create()">
          <odx-icon name="plus" alignLeft></odx-icon>
          {{ 'teams.addTeamAndAccount'  | translate }}
        </button>
      </div>
   </div>
   <team-list *ngIf="type === 'group'" [refreshTrigger]="refreshTeamTrigger" [start]="start" [end]="end"></team-list>
   <member-list *ngIf="type === 'member'" [refreshTrigger]="refreshMemberTrigger" [start]="start" [end]="end"></member-list>
  </div>
</div>