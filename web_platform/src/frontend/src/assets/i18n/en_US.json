{"app": {"title": "Login Platform", "companyname": "Draeger safety equipment (china)co.,ltd", "num": {"1": "One", "2": "Two", "3": "Three", "4": "Four", "5": "Five", "6": "Six", "7": "Seven", "8": "Eight", "9": "Nine", "10": "Ten", "11": "Eleven", "12": "Twelve", "13": "Thirteen", "14": "Fourteen", "15": "Fifteen", "16": "Sixteen"}, "login": {"forgetPassword": "Forget Password", "noRegister": "No account yet?", "login": "<PERSON><PERSON>", "loginNow": "Login In Now", "userName.placeholder": "Phone/Email", "password.placeholder": "Password", "error": "Account or password incorrect, please retype!", "find-password": "Reset Password", "register": "Registration", "newPassword": "New Password", "confirmPassword": "Confirm New Password", "confirmPassword.error": "The confirmed password is not the same as the new password!", "confirmPassword.placeholder": "Please enter 3-10 characters.", "password.placeholder.long": "Please enter 3-10 characters.", "haveAccount": "Already have an account?", "phoneVerification": "Phone Verification Code", "emailVerification": "Email Verification Code", "phone.error": "Phone number is incorrect, please retype", "verification.error": "Verification code is incorrect", "pinCode.error": "Pin Code has been registered", "email.error": "Email is not valid, please retype", "toLogin": "to login", "backLogin": "Return to login", "userName": "User Name", "adminName": "Admin Name", "company": "Company", "checkPolicyNote": "I agree to comply with the FGDC", "and": "and", "maxlength20.error": "Your input content length must not exceed 30 characters.", "maxlengthNum.error": "Your input content length must not exceed {{num}} characters.", "phoneOrEmail.error": "Phone number or email format is invalid, please retype.", "required.error": "This field is required. Please fill it in.", "pin_code.info": "Please enter the PIN code obtained from Dräger Sales.", "pin_code.error": "PIN code is not valid, please retype"}, "phone": "Phone Number", "email": "Email", "phoneOrEmail": "Phone / Email", "input-placeholder": "Please enter", "verificationCode": "Verification Code", "verificationCode.placeholder": "Please enter code recieved", "emailOrPhoneCode.placeholder": "Please enter code recieved", "email.verificationCode.placeholder": "Please enter code recieved", "resend": "Reacquire({{time}})s", "itemsSelected": "{{num}} items selected", "sendCode": "Get verification code", "resendText": "Resend", "logout": "Logout", "personalSettings": "Profile", "updatedPassword": "Password Reset successfully", "updatedPasswordDesc": "The new password has been set successfully, please return to log in!", "registered": "Registered successfully", "registeredDesc": "Registered successfully,password already sent to your email, click the link in the email and login.", "qrCode": "Scan QR code download APP", "androidQrcode": "Download Android APP", "iosQrcode": "Download IOS APP ", "youWant": "You may want to ", "or": " or ", "action": "Action", "systemNotice": "System Notice", "equipmentCalibrationReminder": "Equipment Calibration Reminder", "completeEquipmentCalibration": "Please fulfil calibration as soon as possible.", "eventReminder": "Event Reminder", "overdueDeviceNotice": "The calibration date for your device {{deviceInfo}} has expired by {{days}} days.", "daysNearExpireDeviceNotice": "Your device {{deviceInfo}} has {{days}} days remaining for calibration.", "dayNearExpireDeviceNotice": "Your device {{deviceInfo}} has {{days}} day remaining for calibration."}, "confirm": "Confirm", "cancel": "Cancel", "finish": "Finish", "save": "Save", "submit": "Submit", "user": {"userInfo": "User Information", "default": "admin", "userName": "User Name", "loginName": "User ID", "companyCode": "Company Code", "avatar": "profile picture", "company": "Company", "address": "Address", "role": "Role", "concatWay": "Concat", "key": "Key", "confirmPassword": "Confirm Password", "addUser": "Add User", "importUser": "Import User", "editUser": "Edit User", "viewUser": "View User", "list": "User List", "delete": "Delete User", "active": "Active", "inactive": "Inactive", "passwordNotice": "Reset password, or leave blank to keep the original password."}, "role": {"roleName": "Role Name", "permissions": "Role priorty", "delete": "Delete Role", "addRole": "Add Role"}, "menu": {"login": "System Login", "register": "Register", "findPassword": "Reset Password", "map": "Map", "oneClickExitReset": "Reset evacuation", "alarmRecord": "Alarm Record", "reportSetting": "Report Settings", "userManagement": "Account", "companyManagement": "Company Manage", "permissions": "<PERSON><PERSON>", "deviceRecord": "Device Record", "PINCodeManage": "PIN Code Manage", "menu": "FGDC Platform", "deviceMap": "Device Map", "events": "Events", "deviceOverview": "Overview", "buildings": "Buildings", "controller": "Controller", "reports": "Reports", "account": "Account", "resetEvacuationText": "One-button evacuation cancellation, continue?", "deviceCenter": "Device Center", "teams": "Teams", "teamsList": "Teams List", "membersList": "Member List", "realTimeVideo": "Real-time Video Transmission", "moreSetting": "More Setting", "realVideo": "Real-Time Video", "eventCenter": "Event Center"}, "avatar": {"update-text": "Update profile"}, "map": {"map": "Map", "satellite": "Satellite", "filter": {"controller": "Controller", "device": "Device Status", "select": "Select", "reset": "Reset", "apply": "Apply", "normal": "Normal", "alarm": "Alarm", "warning": "Warning", "fault": "<PERSON><PERSON>"}, "sendSuccess": "Command sent successfully", "sendFail": "Command sent failed, please try again", "device": "devices", "buildings": "buildings", "deviceIntoBuliding": "Devices in the Building", "innerView": "Building Inner View"}, "createdAt": "Created At", "show": "Show", "more": "More", "fold": "Fold", "export": "Export Data", "import": "Import", "template": "Template", "reset": "Reset", "filter": "Filter", "time": "Time", "search": "Search", "add": "Add", "back": "Back", "view": "View", "edit": "Edit", "delete": "Delete", "termsOfUse": "Terms of Use", "privacy": "Privacy Policy", "the selected itmes": "the selected items", "items selected": "items selected", "events": {"all": "All", "resolvedTime": "Resolved Time", "submissionTime": "Submission Time", "status": "Event Status", "type": "Event Type", "detail": "Event Detail", "submittingPerson": "Submitting Person", "pending": "Pending", "resolved": "Resolved", "toResolved": "To Resolved", "resolveDetail": "Resolved Details", "configurationType": "Configuration Issue", "faultType": "Fault Issue", "otherType": "Other Issue"}, "device": {"headerTitle": "Device Data", "title": "Device Overview", "name": "Device Name", "identifier": "Device ID/SN", "type": "Type", "tag": "<PERSON><PERSON>", "channel": "Channel Quantity", "network": "Network", "anomaly": "Anomaly", "notice": "Inhibit", "lastCalibration": "Last Calibration", "lastCalibrationOperator": "Last Calibration Operator", "nextCalibration": "Next Calibration", "lastCalibrationTime": "Last Calibration Time", "nextCalibrationTime": "Next Calibration/Maintenance Time", "gas": "Gas Channel", "measurement": "Measurement Range", "lastConnection": "Last Connection", "assign": "Assign Operator", "assignName": "Assign Operator Name", "record": "Record", "noType": "No type found", "searchType": "Search for type ...", "history": "History", "edit": "<PERSON>", "info": "Device Information", "measurementInfo": "Measurement Information", "channelId": "Channel ID", "delete": "Delete Device", "deleteOnlineNote": "You can't delete online device", "all": "All", "pam": "PAM", "ba": "BA", "tic": "TIC", "fixed": "Fixed", "controller": "Controller", "ttt": "TTT", "other": "Other", "addDevice": "Add <PERSON>", "lastUsageTime": "Last Usage Time", "lastMember": "Last Member", "team": "Team", "realValue": "Real Value", "remark": "Remarks", "subDevice": "SubDevice", "status": "Status", "moreData": "More Data", "reportFault": "Report a Fault", "reportFaultDesc": "Please scan the QR code to fill in the relevant information for equipment maintenance", "deviceList": "Device List", "version": "Version", "DateOfManufacture": "Date Of Manufacture", "SIMExpired": "SIM Expired Date", "WirelessModule": "Wireless Module"}, "alarm": {"eventType": "Event Type", "alarmType": "Alarm Type", "startTime": "Alarm Start Time", "peakValue": "Peak value", "duration": "Duration", "alarmStartPos": "Alarm Start Position", "desc": "Description", "desContent": "{{value}} alarm", "error": "Error", "all": "All", "gas": "Alarm Gas", "alarm": "Alarm", "warning": "Warning", "fault": "<PERSON><PERSON>", "info": "Info"}, "common": {"apply": "Apply", "search": "Seach", "tagNo": "Tag No.", "gps": "GPS Location", "locationTag": "Location Tag", "building": "Building", "controller": "Controller", "online": "Online", "offline": "Offline", "selectHolder": "Please select", "dragFile": "Drag file here or", "clickUpload": " click to upload", "summary": "Summary", "importData": "Import Data", "importDescFirst": "If you are importing data for the first time, please", "importDownload": " Download The Template", "importDescLast": "and import the file according to the template prompts.", "offlineFilter": "You can input on(online) or off(offline) to filter", "anomalyFilter": "You can input A1(A1) or A2(A2), w(Warning), i(Info), f(Fault) to filter", "noticeFilter": "You can input 1(Inhibit) or 0(No Inhibit) to filter", "scheduleFilter": "You can input d(daily) or w(weekly) or m(monthly) to filter", "cycleTypeFilter": "You can input i(interval) or d(duration), c(custom) to filter", "activeFilter": "You can enter r(Run) or s(Stopped)  to filter", "durationFilter": "You can enter the number of seconds to filter", "roleFilter": "You can enter d(Default Operator) or s(Senior Operator) role names to filter", "eventTypeFilter": "You can enter c(Configuration Issue) or f(Fault Issue) or o(Other Issue) to filter", "help": "Help Center", "language": "Language", "fullscreen": "Full Screen", "themeChange": "Theme Change", "notify": "Notification", "currentLocation": "Current Location", "gpsSet": "GPS Setting", "notFond": "Not found", "updateGps": "Update GPS", "setGPSNote": "Click to set GPS location", "location": "Location", "deleteEmptyNote": "Please select the items you want to delete", "itemsPage": " Items/Page", "serviceError": "The server encountered an error, please check the server.", "504Error": "Network timeout, please check the network.", "image": "Image", "uploadTip": "xls/.csv files with a size less than 500KB.", "floor": "Floor", "member": "Member", "device": "<PERSON><PERSON>", "groupModel": "Group Model", "teamModel": "Team Model", "viewTic": "View TIC", "request": "Request", "liveFeed": "live feed", "requestInProgress": "request in progress...", "requestFailed": "Connection Failure"}, "day": "Day", "copy": "Copy", "close": "Close", "success": {"create": "Add successfully", "copy": "Copy successfully", "edit": "Update successfully", "delete": "Delete successfully", "save": "Save successfully", "update": "Update successfully"}, "failed": {"create": "Add failed", "copy": "Co<PERSON> failed", "edit": "Update failed", "delete": "Delete failed", "save": "Save Failed", "update": "Update Failed"}, "confirmDelete": "Are you sure you want to delete {{item}}?", "update": {"title": "Gas Detection Version Updates"}, "select.placeholder": "Please select", "copyright": "Drager Safety Equipment (china)co.,ltd All Rights Reserved", "remind": "Remind", "system": {"index": {"title": "System Index", "empty.content": "No content available"}}, "teams": {"team": "Team", "member": "Member", "title": "Team Management", "name": "Team Name", "person": "Person in Charge", "phone": "Phone Number", "email": "Email", "create_time": "Creation Time", "memberCount": "Team Member", "delete": "Delete Group", "edit": "Edit Team", "add": "Add Team", "addMember": "Add Member", "editMember": "Edit Member", "editSubAccount": "Edit Sub-account", "addSubAccount": "Add Sub-account", "addTeamAndAccount": "Add Team & Account", "memberName": "Member Name", "memberNFC": "NFC Card Number", "deleteMember": "Delete Member", "accountName": "Account Name", "addNew": "Add New", "memberList": "Member List", "memberInfo": "Member Info", "noTeamTypeFond": "No team found", "addMemberToTeam": "Add Member to Team"}, "setting": {"account": "Account", "systemSetting": "System Setting", "zh_CN": "中文", "en_US": "English", "themeChange": "Mode Switch", "darkMode": "Dark Mode", "lightMode": "Light Mode", "version": "Product Version"}}