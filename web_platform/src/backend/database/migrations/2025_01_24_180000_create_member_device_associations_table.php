<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateMemberDeviceAssociationsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Member Device Associations - records when a member uses which devices
        Schema::create('member_device_associations', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('member_id')->index()->comment('Member ID from data stream');
            $table->string('device_identifier')->index()->comment('Device identifier from data stream');
            $table->string('device_type', 50)->index()->comment('Device type: wristband, pressure_gauge, gas_detector, tic, etc.');
            $table->string('gateway_device_identifier')->nullable()->index()->comment('Gateway device identifier');
            $table->timestamp('association_start')->index()->comment('When this association started');
            $table->timestamp('association_end')->nullable()->index()->comment('When this association ended');
            $table->boolean('is_active')->default(true)->index()->comment('Whether this association is currently active');
            $table->json('device_metadata')->nullable()->comment('Device metadata from data stream');
            $table->timestamps();
            
            // Composite indexes for efficient queries
            $table->index(['member_id', 'device_type', 'association_start']);
            $table->index(['member_id', 'is_active']);
            $table->index(['device_identifier', 'association_start']);
            
            // // Ensure no overlapping active associations for same member-device pair
            // $table->unique(['member_id', 'device_identifier', 'is_active'], 'unique_active_member_device');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('member_device_associations');
    }
}