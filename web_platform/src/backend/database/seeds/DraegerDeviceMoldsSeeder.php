<?php

use Carbon\Carbon;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class DraegerDeviceMoldsSeeder extends Seeder
{
    // Field classification constants
    const FIELD_TYPE_IDENTITY = 'identity';        // Device identity fields (MAC, ID, etc.)
    const FIELD_TYPE_RELATIONSHIP = 'relationship'; // Device relationship fields
    const FIELD_TYPE_CONFIG = 'config';            // Configuration fields (relatively stable)
    const FIELD_TYPE_TELEMETRY = 'telemetry';      // Real-time telemetry fields

    /**
     * Run the database seeds.
     * 创建 SMART、TIC、TTT 三种设备系统的设备型号和字段
     * 通用外设设备（手环、压力表、气体检测器）共享使用
     *
     * @return void
     */
    public function run()
    {
        // 创建设备类型
        $this->createDeviceTypes();
        
        // 创建设备类型通用字段（按分类组织）
        $this->createDeviceTypeCommonFields();
        
        // 创建通用外设设备模型
        $this->createCommonPeripheralDeviceMolds();
        
        // 创建 SMART 系统设备模型
        $this->createSmartDeviceMolds();
        
        // 创建 TIC 系统设备模型
        $this->createTicDeviceMolds();
        
        // 创建 TTT 系统设备模型
        $this->createTttDeviceMolds();
    }

    private function createDeviceTypes()
    {
        // SMART 系统设备类型
        DB::table('device_types')->insertOrIgnore([
            ['name' => 'SMART基站', 'code' => 'smart_base_station', 'created_at' => Carbon::now()],
            ['name' => 'SMART成员', 'code' => 'member', 'created_at' => Carbon::now()],
        ]);

        // TIC 系统设备类型
        DB::table('device_types')->insertOrIgnore([
            ['name' => 'TIC热成像设备', 'code' => 'tic', 'created_at' => Carbon::now()],
        ]);

        // TTT 系统设备类型
        DB::table('device_types')->insertOrIgnore([
            ['name' => 'TTT个人设备', 'code' => 'ttt', 'created_at' => Carbon::now()],
        ]);

        // 通用外设设备类型
        DB::table('device_types')->insertOrIgnore([
            ['name' => '手环', 'code' => 'wristband', 'created_at' => Carbon::now()],
            ['name' => '压力表', 'code' => 'pressure_gauge', 'created_at' => Carbon::now()],
            ['name' => '气体检测器', 'code' => 'gas_detector', 'created_at' => Carbon::now()],
        ]);
    }

    private function createDeviceTypeCommonFields()
    {
        // 按设备类型分别定义字段，避免冗余
        $deviceTypeFields = [
            // SMART基站字段
            'smart_base_station' => [
                'identity' => [
                    ['name' => '设备ID', 'code' => 'DeviceID', 'value_type' => 'string', 'precision' => null, 'default_value' => null, 'unit_symbol' => null],
                    ['name' => '设备名称', 'code' => 'DeviceName', 'value_type' => 'string', 'precision' => null, 'default_value' => null, 'unit_symbol' => null],
                    ['name' => '设备MAC地址', 'code' => 'DeviceMac', 'value_type' => 'string', 'precision' => null, 'default_value' => null, 'unit_symbol' => null],
                ],
                'config' => [
                    ['name' => '设备版本', 'code' => 'DeviceVersion', 'value_type' => 'string', 'precision' => null, 'default_value' => null, 'unit_symbol' => null],
                ],
                'telemetry' => [
                    ['name' => '上传时间', 'code' => 'UploadTime', 'value_type' => 'integer', 'precision' => 0, 'default_value' => null, 'unit_symbol' => null],
                    ['name' => '任务开始时间', 'code' => 'TaskStartTime', 'value_type' => 'string', 'precision' => null, 'default_value' => null, 'unit_symbol' => null],
                    ['name' => '基站电池电量', 'code' => 'BaseBatteryLevel', 'value_type' => 'integer', 'precision' => 0, 'default_value' => null, 'unit_symbol' => '%'],
                    ['name' => 'PC电池电量', 'code' => 'PCBatteryLevel', 'value_type' => 'integer', 'precision' => 0, 'default_value' => null, 'unit_symbol' => '%'],
                    ['name' => '设备信号', 'code' => 'DeviceSignal', 'value_type' => 'integer', 'precision' => 0, 'default_value' => null, 'unit_symbol' => null],
                    ['name' => '平板经度', 'code' => 'DeviceGPSLongitude', 'value_type' => 'string', 'precision' => null, 'default_value' => null, 'unit_symbol' => null],
                    ['name' => '平板纬度', 'code' => 'DeviceGPSLatitude', 'value_type' => 'string', 'precision' => null, 'default_value' => null, 'unit_symbol' => null],
                ]
            ],

            // SMART成员字段
            'member' => [
                'identity' => [
                    ['name' => '人员ID', 'code' => 'UserID', 'value_type' => 'string', 'precision' => null, 'default_value' => null, 'unit_symbol' => null],
                ],
                'config' => [
                    ['name' => '人员姓名', 'code' => 'UserName', 'value_type' => 'string', 'precision' => null, 'default_value' => null, 'unit_symbol' => null],
                    ['name' => '人员类型', 'code' => 'UserType', 'value_type' => 'integer', 'precision' => 0, 'default_value' => null, 'unit_symbol' => null],
                    ['name' => '人员设备号', 'code' => 'UserDevice', 'value_type' => 'string', 'precision' => null, 'default_value' => null, 'unit_symbol' => null],
                    ['name' => '人员网络', 'code' => 'UserNetwork', 'value_type' => 'string', 'precision' => null, 'default_value' => null, 'unit_symbol' => null],
                    ['name' => '人员频段', 'code' => 'UserBand', 'value_type' => 'string', 'precision' => null, 'default_value' => null, 'unit_symbol' => null],
                    ['name' => '是否队长', 'code' => 'IsLeader', 'value_type' => 'integer', 'precision' => 0, 'default_value' => null, 'unit_symbol' => null],
                    ['name' => '人员职务', 'code' => 'UserDuty', 'value_type' => 'string', 'precision' => null, 'default_value' => null, 'unit_symbol' => null],
                    ['name' => '人员电话', 'code' => 'UserTel', 'value_type' => 'string', 'precision' => null, 'default_value' => null, 'unit_symbol' => null],
                ],
                'telemetry' => [
                    ['name' => '进场时间', 'code' => 'EnterTime', 'value_type' => 'string', 'precision' => null, 'default_value' => null, 'unit_symbol' => null],
                    ['name' => 'GPS经度', 'code' => 'Longitude', 'value_type' => 'string', 'precision' => null, 'default_value' => null, 'unit_symbol' => null],
                    ['name' => 'GPS纬度', 'code' => 'Latitude', 'value_type' => 'string', 'precision' => null, 'default_value' => null, 'unit_symbol' => null],
                    ['name' => '撤离信号', 'code' => 'EvacuateSignal', 'value_type' => 'integer', 'precision' => 0, 'default_value' => null, 'unit_symbol' => null],
                    ['name' => '跌倒报警信号', 'code' => 'FallAlarmSignal', 'value_type' => 'integer', 'precision' => 0, 'default_value' => null, 'unit_symbol' => null],
                    ['name' => '主动报警', 'code' => 'ActivealarmSignal', 'value_type' => 'integer', 'precision' => 0, 'default_value' => null, 'unit_symbol' => null],
                    ['name' => '低气压报警', 'code' => 'PressureAlarm', 'value_type' => 'integer', 'precision' => 0, 'default_value' => null, 'unit_symbol' => null],
                    ['name' => '逃生信号', 'code' => 'EscapeSignal', 'value_type' => 'integer', 'precision' => 0, 'default_value' => null, 'unit_symbol' => null],
                    ['name' => '压力表通讯状态', 'code' => 'PressureCommunication', 'value_type' => 'integer', 'precision' => 0, 'default_value' => null, 'unit_symbol' => null],
                    ['name' => '报警信号', 'code' => 'AlarmSignal', 'value_type' => 'integer', 'precision' => 0, 'default_value' => null, 'unit_symbol' => null],
                    ['name' => 'GPS信号', 'code' => 'GPSSignal', 'value_type' => 'integer', 'precision' => 0, 'default_value' => null, 'unit_symbol' => null],
                    ['name' => '低电量信号', 'code' => 'LowBatterySignal', 'value_type' => 'integer', 'precision' => 0, 'default_value' => null, 'unit_symbol' => null],
                    ['name' => '离线信号', 'code' => 'OfflineSignal', 'value_type' => 'integer', 'precision' => 0, 'default_value' => null, 'unit_symbol' => null],
                    ['name' => 'SOS信号', 'code' => 'SOSSignal', 'value_type' => 'integer', 'precision' => 0, 'default_value' => null, 'unit_symbol' => null],
                ]
            ],

            // TIC热成像设备字段
            'tic' => [
                'identity' => [
                    ['name' => '设备MAC地址', 'code' => 'DeviceMac', 'value_type' => 'string', 'precision' => null, 'default_value' => null, 'unit_symbol' => null],
                    ['name' => '设备序列号', 'code' => 'DeviceSN', 'value_type' => 'string', 'precision' => null, 'default_value' => null, 'unit_symbol' => null],
                ],
                'config' => [
                    ['name' => '视频模式', 'code' => 'VideoMode', 'value_type' => 'integer', 'precision' => 0, 'default_value' => null, 'unit_symbol' => null],
                    ['name' => '显示模式', 'code' => 'DisplayMode', 'value_type' => 'integer', 'precision' => 0, 'default_value' => null, 'unit_symbol' => null],
                ],
                'telemetry' => [
                    ['name' => '上传时间', 'code' => 'Time', 'value_type' => 'integer', 'precision' => 0, 'default_value' => null, 'unit_symbol' => null],
                    ['name' => '开机时间', 'code' => 'StartTime', 'value_type' => 'string', 'precision' => null, 'default_value' => null, 'unit_symbol' => null],
                    ['name' => '最高温度', 'code' => 'HighTemperature', 'value_type' => 'string', 'precision' => null, 'default_value' => null, 'unit_symbol' => '℃'],
                    ['name' => '中心温度', 'code' => 'CenterTemperature', 'value_type' => 'string', 'precision' => null, 'default_value' => null, 'unit_symbol' => '℃'],
                    ['name' => '电池电量', 'code' => 'Battery', 'value_type' => 'integer', 'precision' => 0, 'default_value' => null, 'unit_symbol' => '%'],
                    ['name' => '图片数据', 'code' => 'Picture', 'value_type' => 'string', 'precision' => null, 'default_value' => null, 'unit_symbol' => null],
                ]
            ],
            
            // TTT个人设备字段
            'ttt' => [
                'identity' => [
                    ['name' => '设备ID', 'code' => 'DeviceID', 'value_type' => 'string', 'precision' => null, 'default_value' => null, 'unit_symbol' => null],
                    ['name' => '人员ID', 'code' => 'UserID', 'value_type' => 'string', 'precision' => null, 'default_value' => null, 'unit_symbol' => null],
                ],
                'config' => [
                    ['name' => '设备版本', 'code' => 'DeviceVersion', 'value_type' => 'string', 'precision' => null, 'default_value' => null, 'unit_symbol' => null],
                    ['name' => '人员姓名', 'code' => 'UserName', 'value_type' => 'string', 'precision' => null, 'default_value' => null, 'unit_symbol' => null],
                    ['name' => '人员类型', 'code' => 'UserType', 'value_type' => 'integer', 'precision' => 0, 'default_value' => null, 'unit_symbol' => null],
                    ['name' => '是否队长', 'code' => 'IsLeader', 'value_type' => 'integer', 'precision' => 0, 'default_value' => null, 'unit_symbol' => null],
                    ['name' => '人员职务', 'code' => 'UserDuty', 'value_type' => 'string', 'precision' => null, 'default_value' => null, 'unit_symbol' => null],
                    ['name' => '人员电话', 'code' => 'UserTel', 'value_type' => 'string', 'precision' => null, 'default_value' => null, 'unit_symbol' => null],
                    ['name' => '人员网络', 'code' => 'UserNetwork', 'value_type' => 'string', 'precision' => null, 'default_value' => null, 'unit_symbol' => null],
                ],
                'telemetry' => [
                    ['name' => '上传时间', 'code' => 'UploadTime', 'value_type' => 'integer', 'precision' => 0, 'default_value' => null, 'unit_symbol' => null],
                    ['name' => '设备电池电量', 'code' => 'BatteryLevel', 'value_type' => 'integer', 'precision' => 0, 'default_value' => null, 'unit_symbol' => '%'],
                    ['name' => 'TTT温度', 'code' => 'Temperature', 'value_type' => 'string', 'precision' => null, 'default_value' => null, 'unit_symbol' => '℃'],
                    ['name' => '报警状态', 'code' => 'AlarmStatus', 'value_type' => 'integer', 'precision' => 0, 'default_value' => null, 'unit_symbol' => null],
                    ['name' => 'TTT温度报警', 'code' => 'DeviceAlarm', 'value_type' => 'integer', 'precision' => 0, 'default_value' => null, 'unit_symbol' => null],
                    ['name' => 'TTT温度报警', 'code' => 'TemperatureAlarm', 'value_type' => 'integer', 'precision' => 0, 'default_value' => null, 'unit_symbol' => null],
                    ['name' => 'GPS经度', 'code' => 'Longitude', 'value_type' => 'string', 'precision' => null, 'default_value' => null, 'unit_symbol' => null],
                    ['name' => 'GPS纬度', 'code' => 'Latitude', 'value_type' => 'string', 'precision' => null, 'default_value' => null, 'unit_symbol' => null],
                    ['name' => '主动报警', 'code' => 'ActivealarmSignal', 'value_type' => 'integer', 'precision' => 0, 'default_value' => null, 'unit_symbol' => null],
                    ['name' => '确认信号', 'code' => 'Acknowledge', 'value_type' => 'integer', 'precision' => 0, 'default_value' => null, 'unit_symbol' => null],
                    ['name' => '逃生信号', 'code' => 'EscapeSignal', 'value_type' => 'integer', 'precision' => 0, 'default_value' => null, 'unit_symbol' => null],
                    ['name' => '撤离信号', 'code' => 'EvacuateSignal', 'value_type' => 'integer', 'precision' => 0, 'default_value' => null, 'unit_symbol' => null],
                    ['name' => '跌倒报警信号', 'code' => 'FallAlarmSignal', 'value_type' => 'integer', 'precision' => 0, 'default_value' => null, 'unit_symbol' => null],
                    ['name' => '压力表通讯状态', 'code' => 'PressureCommunication', 'value_type' => 'integer', 'precision' => 0, 'default_value' => null, 'unit_symbol' => null],
                ]
            ],
            
            // 手环字段
            'wristband' => [
                'identity' => [
                    ['name' => '手环序列号', 'code' => 'WristbandID', 'value_type' => 'string', 'precision' => null, 'default_value' => null, 'unit_symbol' => null],
                    ['name' => '手环MAC地址', 'code' => 'WristbandMac', 'value_type' => 'string', 'precision' => null, 'default_value' => null, 'unit_symbol' => null],
                ],
                'telemetry' => [
                    ['name' => '心率', 'code' => 'HeartRate', 'value_type' => 'integer', 'precision' => 0, 'default_value' => null, 'unit_symbol' => 'bpm'],
                ]
            ],
            
            // 压力表字段
            'pressure_gauge' => [
                'identity' => [
                    ['name' => '压力表序列号', 'code' => 'GaugeID', 'value_type' => 'string', 'precision' => null, 'default_value' => null, 'unit_symbol' => null],
                    ['name' => '压力表MAC地址', 'code' => 'GaugeMac', 'value_type' => 'string', 'precision' => null, 'default_value' => null, 'unit_symbol' => null],
                ],
                'config' => [
                    ['name' => '压力表类型', 'code' => 'PressureGaugeType', 'value_type' => 'integer', 'precision' => 0, 'default_value' => null, 'unit_symbol' => null],
                    ['name' => '固件版本', 'code' => 'GaugeFirmwareVersion', 'value_type' => 'string', 'precision' => null, 'default_value' => null, 'unit_symbol' => null],
                ],
                'telemetry' => [
                    ['name' => '气体压力值', 'code' => 'GaugePressure', 'value_type' => 'string', 'precision' => null, 'default_value' => null, 'unit_symbol' => null],
                    ['name' => '压力单位', 'code' => 'GaugeUnit', 'value_type' => 'string', 'precision' => null, 'default_value' => null, 'unit_symbol' => null],
                    ['name' => '压力表电池电量', 'code' => 'GaugeBatteryLevel', 'value_type' => 'integer', 'precision' => 0, 'default_value' => null, 'unit_symbol' => '%'],
                    ['name' => '压力表电量报警', 'code' => 'GaugeBatteryAlarm', 'value_type' => 'integer', 'precision' => 0, 'default_value' => null, 'unit_symbol' => null],
                    ['name' => '压力表温度', 'code' => 'Temperature', 'value_type' => 'string', 'precision' => null, 'default_value' => null, 'unit_symbol' => null],
                    ['name' => '剩余时间', 'code' => 'RemainingTime', 'value_type' => 'integer', 'precision' => 0, 'default_value' => null, 'unit_symbol' => 's'],
                    ['name' => '使用时间', 'code' => 'UseTime', 'value_type' => 'integer', 'precision' => 0, 'default_value' => null, 'unit_symbol' => 's'],
                    ['name' => '温度报警', 'code' => 'TemperatureAlarm', 'value_type' => 'integer', 'precision' => 0, 'default_value' => null, 'unit_symbol' => null],
                    ['name' => '气量报警', 'code' => 'VolumeAlarm', 'value_type' => 'integer', 'precision' => 0, 'default_value' => null, 'unit_symbol' => null],
                    ['name' => '低气压报警', 'code' => 'PressureAlarm', 'value_type' => 'integer', 'precision' => 0, 'default_value' => null, 'unit_symbol' => null],
                    ['name' => '上次维护时间', 'code' => 'LastMaintainDate', 'value_type' => 'string', 'precision' => null, 'default_value' => null, 'unit_symbol' => null],
                ]
            ],
            
            // 气体检测器字段
            'gas_detector' => [
                'identity' => [
                    ['name' => '气检表序列号', 'code' => 'GasDetectorID', 'value_type' => 'string', 'precision' => null, 'default_value' => null, 'unit_symbol' => null],
                    ['name' => '气检表MAC地址', 'code' => 'GasDetectorMac', 'value_type' => 'string', 'precision' => null, 'default_value' => null, 'unit_symbol' => null],
                ],
                'config' => [
                    ['name' => '气检表类型', 'code' => 'GasDetectorName', 'value_type' => 'string', 'precision' => null, 'default_value' => null, 'unit_symbol' => null],
                    ['name' => '固件版本', 'code' => 'GasDetectorFirmwareVersion', 'value_type' => 'string', 'precision' => null, 'default_value' => null, 'unit_symbol' => null],
                ],
                'telemetry' => [
                    ['name' => '气检表电池电量', 'code' => 'GasDetectorBatteryLevel', 'value_type' => 'integer', 'precision' => 0, 'default_value' => null, 'unit_symbol' => '%'],
                    ['name' => '气检表电量报警', 'code' => 'GasDetectorBatteryAlarm', 'value_type' => 'integer', 'precision' => 0, 'default_value' => null, 'unit_symbol' => null],
                    // 气体浓度（直接使用气体名称作为字段代码，unit/alarm/fault存储在对应列中）
                    ['name' => 'CH4浓度', 'code' => 'CH4', 'value_type' => 'float', 'precision' => 2, 'default_value' => null, 'unit_symbol' => null],
                    ['name' => 'O2浓度', 'code' => 'O2', 'value_type' => 'float', 'precision' => 2, 'default_value' => null, 'unit_symbol' => null],
                    ['name' => 'CO浓度', 'code' => 'CO', 'value_type' => 'float', 'precision' => 2, 'default_value' => null, 'unit_symbol' => null],
                    ['name' => 'H2S浓度', 'code' => 'H2S', 'value_type' => 'float', 'precision' => 2, 'default_value' => null, 'unit_symbol' => null],
                ]
            ],
        ];

        // 获取所有设备类型
        $deviceTypes = DB::table('device_types')->get();
        
        foreach ($deviceTypes as $deviceType) {
            // 只为该设备类型创建相应的字段
            if (isset($deviceTypeFields[$deviceType->code])) {
                foreach ($deviceTypeFields[$deviceType->code] as $classification => $fields) {
                    foreach ($fields as $fieldData) {
                        DB::table('device_type_common_fields')->insertOrIgnore([
                            'name' => $fieldData['name'],
                            'code' => $fieldData['code'],
                            'device_type_id' => $deviceType->id,
                            'value_type' => $fieldData['value_type'],
                            'precision' => $fieldData['precision'],
                            'default_value' => $fieldData['default_value'],
                            'unit_symbol' => $fieldData['unit_symbol'],
                            'field_classification' => $classification,
                            'created_at' => Carbon::now()
                        ]);
                    }
                }
            }
            
            // 气体检测器的动态字段由 Go 服务处理，这里不预定义
        }
    }

    private function getCommonFieldId($deviceTypeId, $commonCode)
    {
        $commonField = DB::table('device_type_common_fields')
            ->where('device_type_id', $deviceTypeId)
            ->where('code', $commonCode)
            ->first();
        
        return $commonField ? $commonField->id : null;
    }

    private function createCommonPeripheralDeviceMolds()
    {
        // 1. 通用手环设备模型
        $wristbandType = DB::table('device_types')->where('code', 'wristband')->first();
        $wristbandId = DB::table('device_molds')->insertGetId([
            'name' => '手环',
            'manufacturer' => 'Draeger',
            'model_code' => 'wristband',
            'device_type_id' => $wristbandType->id,
            'tenant_id' => 0,
            'is_gateway' => false,
            'need_gateway' => true,
            'default_gateway_mold_id' => null,
            'transmission_strategy' => 'via-gateway',
            'authentication_strategy' => 'default',
            'third_party_data_strategy' => 'none',
            'created_at' => Carbon::now()
        ]);

        // 手环字段 - 只定义该设备实际使用的字段
        $wristbandFields = ['WristbandID', 'WristbandMac', 'HeartRate'];
        foreach ($wristbandFields as $fieldCode) {
            $commonFieldId = $this->getCommonFieldId($wristbandType->id, $fieldCode);
            if ($commonFieldId) {
                $commonField = DB::table('device_type_common_fields')->find($commonFieldId);
                DB::table('device_telemetry_fields')->insertOrIgnore([
                    'name' => $commonField->name,
                    'code' => $fieldCode,
                    'device_mold_id' => $wristbandId,
                    'common_code' => $fieldCode,
                    'device_type_common_field_id' => $commonFieldId,
                    'value_type' => $commonField->value_type,
                    'precision' => $commonField->precision,
                    'default_value' => $commonField->default_value,
                    'unit_symbol' => $commonField->unit_symbol,
                    'created_at' => Carbon::now()
                ]);
            }
        }

        // 2. 通用压力表设备模型
        $pressureGaugeType = DB::table('device_types')->where('code', 'pressure_gauge')->first();
        $pressureGaugeId = DB::table('device_molds')->insertGetId([
            'name' => '压力表',
            'manufacturer' => 'Draeger',
            'model_code' => 'pressure_gauge',
            'device_type_id' => $pressureGaugeType->id,
            'tenant_id' => 0,
            'is_gateway' => false,
            'need_gateway' => true,
            'default_gateway_mold_id' => null,
            'transmission_strategy' => 'via-gateway',
            'authentication_strategy' => 'default',
            'third_party_data_strategy' => 'none',
            'created_at' => Carbon::now()
        ]);

        // 压力表字段
        $pressureGaugeFields = [
            'PressureGaugeType', 'GaugeID', 'GaugeMac', 'GaugePressure', 'GaugeUnit',
            'GaugeBatteryLevel', 'Temperature', 'RemainingTime', 'UseTime',
            'TemperatureAlarm', 'VolumeAlarm', 'PressureAlarm', 'LastMaintainDate'
        ];
        
        foreach ($pressureGaugeFields as $fieldCode) {
            $commonFieldId = $this->getCommonFieldId($pressureGaugeType->id, $fieldCode);
            if ($commonFieldId) {
                $commonField = DB::table('device_type_common_fields')->find($commonFieldId);
                DB::table('device_telemetry_fields')->insertOrIgnore([
                    'name' => $commonField->name,
                    'code' => $fieldCode,
                    'device_mold_id' => $pressureGaugeId,
                    'common_code' => $fieldCode,
                    'device_type_common_field_id' => $commonFieldId,
                    'value_type' => $commonField->value_type,
                    'precision' => $commonField->precision,
                    'default_value' => $commonField->default_value,
                    'unit_symbol' => $commonField->unit_symbol,
                    'created_at' => Carbon::now()
                ]);
            }
        }

        // 3. 通用气体检测器设备模型
        $gasDetectorType = DB::table('device_types')->where('code', 'gas_detector')->first();
        $gasDetectorId = DB::table('device_molds')->insertGetId([
            'name' => '气体检测器',
            'manufacturer' => 'Draeger',
            'model_code' => 'gas_detector',
            'device_type_id' => $gasDetectorType->id,
            'tenant_id' => 0,
            'is_gateway' => false,
            'need_gateway' => true,
            'default_gateway_mold_id' => null,
            'transmission_strategy' => 'via-gateway',
            'authentication_strategy' => 'default',
            'third_party_data_strategy' => 'none',
            'created_at' => Carbon::now()
        ]);

        // 气体检测器基础字段 - 动态气体字段由 Go 服务处理
        $gasDetectorFields = ['GasDetectorID', 'GasDetectorMac', 'GasDetectorName', 'GasDetectorBatteryLevel'];
        
        foreach ($gasDetectorFields as $fieldCode) {
            $commonFieldId = $this->getCommonFieldId($gasDetectorType->id, $fieldCode);
            if ($commonFieldId) {
                $commonField = DB::table('device_type_common_fields')->find($commonFieldId);
                DB::table('device_telemetry_fields')->insertOrIgnore([
                    'name' => $commonField->name,
                    'code' => $fieldCode,
                    'device_mold_id' => $gasDetectorId,
                    'common_code' => $fieldCode,
                    'device_type_common_field_id' => $commonFieldId,
                    'value_type' => $commonField->value_type,
                    'precision' => $commonField->precision,
                    'default_value' => $commonField->default_value,
                    'unit_symbol' => $commonField->unit_symbol,
                    'created_at' => Carbon::now()
                ]);
            }
        }
    }

    private function createSmartDeviceMolds()
    {
        // 1. SMART基站设备模型
        $smartBaseStationType = DB::table('device_types')->where('code', 'smart_base_station')->first();
        $smartBaseStationId = DB::table('device_molds')->insertGetId([
            'name' => 'SMART基站',
            'manufacturer' => 'Draeger',
            'model_code' => 'smart_base_station',
            'device_type_id' => $smartBaseStationType->id,
            'tenant_id' => 0,
            'is_gateway' => true,
            'need_gateway' => false,
            'transmission_strategy' => 'mqtt',
            'authentication_strategy' => 'default',
            'third_party_data_strategy' => 'mqtt',
            'created_at' => Carbon::now()
        ]);

        // SMART基站字段
        $smartBaseStationFields = [
            'DeviceID', 'DeviceName', 'DeviceMac', 'DeviceVersion', 'BaseBatteryLevel',
            'PCBatteryLevel', 'DeviceSignal', 'TaskStartTime', 'UploadTime', 'DeviceGPSLongitude',
            'DeviceGPSLatitude',
        ];
        
        foreach ($smartBaseStationFields as $fieldCode) {
            $commonFieldId = $this->getCommonFieldId($smartBaseStationType->id, $fieldCode);
            if ($commonFieldId) {
                $commonField = DB::table('device_type_common_fields')->find($commonFieldId);
                DB::table('device_telemetry_fields')->insertOrIgnore([
                    'name' => $commonField->name,
                    'code' => $fieldCode,
                    'device_mold_id' => $smartBaseStationId,
                    'common_code' => $fieldCode,
                    'device_type_common_field_id' => $commonFieldId,
                    'value_type' => $commonField->value_type,
                    'precision' => $commonField->precision,
                    'default_value' => $commonField->default_value,
                    'unit_symbol' => $commonField->unit_symbol,
                    'created_at' => Carbon::now()
                ]);
            }
        }

        // 2. SMART成员设备模型 - 使用 member 作为 model_code
        $smartMemberType = DB::table('device_types')->where('code', 'member')->first();
        $smartMemberId = DB::table('device_molds')->insertGetId([
            'name' => 'SMART成员',
            'manufacturer' => 'Draeger',
            'model_code' => 'member', // 匹配数据中的 device_mold_code
            'device_type_id' => $smartMemberType->id,
            'tenant_id' => 0,
            'is_gateway' => false,
            'need_gateway' => true,
            'default_gateway_mold_id' => $smartBaseStationId,
            'transmission_strategy' => 'via-gateway',
            'authentication_strategy' => 'default',
            'third_party_data_strategy' => 'none',
            'created_at' => Carbon::now()
        ]);

        // SMART成员字段 - 根据文档完整定义
        $smartMemberFields = [
            'UserID', 'UserName', 'UserType', 'UserDevice', 'UserNetwork', 'UserBand',
            'IsLeader', 'UserDuty', 'UserTel', 'EnterTime', 'Longitude', 'Latitude',
            'EvacuateSignal', 'FallAlarmSignal', 'ActivealarmSignal', 'PressureAlarm',
            'EscapeSignal', 'PressureCommunication', 'AlarmSignal', 'GPSSignal',
            'LowBatterySignal', 'OfflineSignal', 'SOSSignal'
        ];
        
        foreach ($smartMemberFields as $fieldCode) {
            $commonFieldId = $this->getCommonFieldId($smartMemberType->id, $fieldCode);
            if ($commonFieldId) {
                $commonField = DB::table('device_type_common_fields')->find($commonFieldId);
                DB::table('device_telemetry_fields')->insertOrIgnore([
                    'name' => $commonField->name,
                    'code' => $fieldCode,
                    'device_mold_id' => $smartMemberId,
                    'common_code' => $fieldCode,
                    'device_type_common_field_id' => $commonFieldId,
                    'value_type' => $commonField->value_type,
                    'precision' => $commonField->precision,
                    'default_value' => $commonField->default_value,
                    'unit_symbol' => $commonField->unit_symbol,
                    'created_at' => Carbon::now()
                ]);
            }
        }
    }

    private function createTicDeviceMolds()
    {
        // TIC热成像设备模型 - 使用 tic 作为 model_code
        $ticThermalCameraType = DB::table('device_types')->where('code', 'tic')->first();
        $ticThermalCameraId = DB::table('device_molds')->insertGetId([
            'name' => 'TIC热成像设备',
            'manufacturer' => 'Draeger',
            'model_code' => 'tic', // 匹配数据中的 device_mold_code
            'device_type_id' => $ticThermalCameraType->id,
            'tenant_id' => 0,
            'is_gateway' => false,
            'need_gateway' => true, // TIC devices need TTT gateway
            'transmission_strategy' => 'mqtt',
            'authentication_strategy' => 'default',
            'third_party_data_strategy' => 'mqtt',
            'created_at' => Carbon::now(),
            'mold_properties' => '{"driver_class":"FlatJsonDriver","third_party_data_strategy_props":{"port":1883,"qos":0,"password":null,"host":null,"topic":null,"username":null}}'
        ]);

        // TIC设备字段 - 根据实际数据示例
        $ticFields = [
            'DeviceMac', 'DeviceSN', 'Time', 'StartTime', 'HighTemperature',
            'CenterTemperature', 'Battery', 'VideoMode', 'DisplayMode', 'Picture'
        ];
        
        foreach ($ticFields as $fieldCode) {
            $commonFieldId = $this->getCommonFieldId($ticThermalCameraType->id, $fieldCode);
            if ($commonFieldId) {
                $commonField = DB::table('device_type_common_fields')->find($commonFieldId);
                DB::table('device_telemetry_fields')->insertOrIgnore([
                    'name' => $commonField->name,
                    'code' => $fieldCode,
                    'device_mold_id' => $ticThermalCameraId,
                    'common_code' => $fieldCode,
                    'device_type_common_field_id' => $commonFieldId,
                    'value_type' => $commonField->value_type,
                    'precision' => $commonField->precision,
                    'default_value' => $commonField->default_value,
                    'unit_symbol' => $commonField->unit_symbol,
                    'created_at' => Carbon::now()
                ]);
            }
        }
    }

    private function createTttDeviceMolds()
    {
        // TTT个人设备模型 - 使用 ttt 作为 model_code
        $tttPersonalDeviceType = DB::table('device_types')->where('code', 'ttt')->first();
        $tttPersonalDeviceId = DB::table('device_molds')->insertGetId([
            'name' => 'TTT个人设备',
            'manufacturer' => 'Draeger',
            'model_code' => 'ttt', // 匹配数据中的 device_mold_code
            'device_type_id' => $tttPersonalDeviceType->id,
            'tenant_id' => 0,
            'is_gateway' => true,
            'need_gateway' => false,
            'transmission_strategy' => 'mqtt',
            'authentication_strategy' => 'default',
            'third_party_data_strategy' => 'mqtt',
            'created_at' => Carbon::now(),
            'mold_properties' => '{"driver_class":"FlatJsonDriver","third_party_data_strategy_props":{"port":1883,"qos":0,"password":null,"host":null,"topic":null,"username":null}}'
        ]);

        // TTT个人设备字段 - 根据实际数据示例
        $tttPersonalDeviceFields = [
            'DeviceID', 'DeviceVersion', 'BatteryLevel', 'UploadTime',
            'Temperature', 'AlarmStatus', 'DeviceAlarm', 'TemperatureAlarm', 'UserID', 'UserName', 'UserDuty',
            'UserTel', 'UserType', 'IsLeader', 'UserNetwork', 'Longitude', 'Latitude',
            'ActivealarmSignal', 'Acknowledge', 'EscapeSignal', 'EvacuateSignal', 'FallAlarmSignal',
            'PressureCommunication'
        ];
        
        foreach ($tttPersonalDeviceFields as $fieldCode) {
            $commonFieldId = $this->getCommonFieldId($tttPersonalDeviceType->id, $fieldCode);
            if ($commonFieldId) {
                $commonField = DB::table('device_type_common_fields')->find($commonFieldId);
                DB::table('device_telemetry_fields')->insertOrIgnore([
                    'name' => $commonField->name,
                    'code' => $fieldCode,
                    'device_mold_id' => $tttPersonalDeviceId,
                    'common_code' => $fieldCode,
                    'device_type_common_field_id' => $commonFieldId,
                    'value_type' => $commonField->value_type,
                    'precision' => $commonField->precision,
                    'default_value' => $commonField->default_value,
                    'unit_symbol' => $commonField->unit_symbol,
                    'created_at' => Carbon::now()
                ]);
            }
        }
    }

    // 注意：传感器设备模型已删除，因为传感器数据现在直接使用现有的设备模型
    // (wristband, pressure_gauge, gas_detector) 并通过Go服务进行数据映射处理
}