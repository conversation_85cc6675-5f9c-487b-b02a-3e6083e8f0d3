<?php

namespace Modules\System;

use Modules\System\Observers\TeamObserver;
use Modules\System\Models\Team;
use Modules\System\Observers\MemberDeviceUsageObserver;
use Modules\System\Models\MemberDeviceUsage;
use Modules\System\Observers\MemberObserver;
use Modules\System\Models\Member;
use Modules\System\Observers\DeviceCalibrationRecordObserver;
use Modules\System\Models\DeviceCalibrationRecord;
use Modules\System\Observers\ChannelTypeObserver;
use Modules\System\Models\ChannelType;
use Modules\System\Observers\EventObserver;
use Modules\System\Models\Event;
use Modules\System\Observers\BuildingObserver;
use Modules\System\Models\Building;
use Modules\System\Observers\OperatorDeviceUsagesObserver;
use Modules\System\Models\OperatorDeviceUsages;
use Modules\System\Observers\PinCodesObserver;
use Modules\System\Models\PinCodes;
use Modules\System\Commands\SystemCheckAndSendReport;
use Modules\System\Commands\SyncMemberDeviceUsages;
use Modules\System\Observers\ReportPlanObserver;
use Modules\System\Models\ReportPlan;
use Modules\System\Observers\VersionObserver;
use Modules\System\Models\Version;
use Modules\System\Commands\SystemRefreshTelemetryFieldsMapping;
use Modules\System\Extensions\DeviceMoldCacheManager\DeviceMoldCacheRedisStore;
use Modules\System\Extensions\DeviceRulesCacheManager\DeviceRulesCacheRedisStore;
use Modules\System\Commands\SystemAmqpSubscriberWebUp;
use Modules\System\Commands\SystemRealtimeBroadcastConsumer;
use Modules\System\Observers\TelemetryValueMappingObserver;
use Modules\System\Models\TelemetryValueMapping;
use Modules\System\Observers\StrategyTemplateCategoryObserver;
use Modules\System\Models\StrategyTemplateCategory;
use Modules\System\Observers\CommonFieldDescriptionsObserver;
use Modules\System\Models\CommonFieldDescriptions;
use Modules\System\Observers\TelemetryValueCommonObserver;
use Modules\System\Models\TelemetryValueCommon;
use Modules\System\Observers\StrategyObserver;
use Modules\System\Models\Strategy;
use Modules\System\Observers\StrategyTemplateObserver;
use Modules\System\Models\StrategyTemplate;
use Modules\System\Observers\RuleTemplateObserver;
use Modules\System\Models\RuleTemplate;
use Modules\System\Observers\RuleTemplateCriteriaObserver;
use Modules\System\Models\RuleTemplateCriteria;
use Modules\System\Observers\RuleTemplateActionObserver;
use Modules\System\Models\RuleTemplateAction;
use Modules\System\Observers\DeviceTypeCommonFieldObserver;
use Modules\System\Models\DeviceTypeCommonField;
use Modules\System\Observers\AlarmLogObserver;
use Modules\System\Models\AlarmLog;
use Modules\System\Commands\SystemUpdateDeviceStatus;
use Modules\System\Observers\DeviceOnlineRecordObserver;
use Modules\System\Models\DeviceOnlineRecord;
use Modules\System\Commands\SystemCommandTest;
use Modules\System\Observers\TelemetryRawPerMoldObserver;
use Modules\System\Models\TelemetryRawPerMold;
use Modules\System\Commands\SystemWasm;
use Modules\System\Commands\SystemAmqpProducter;
use Modules\System\Commands\SystemSendVersionToController;
use Modules\System\Observers\SpaceObserver;
use Modules\System\Models\Space;
use Illuminate\Support\Facades\Cache;
use Modules\System\Commands\SystemNotificationTest;
use Modules\System\Commands\SystemCalibrationNotification;
use Modules\System\Extensions\ReactiveDevice\ReactiveDeviceManager;
use Modules\System\Observers\NotificationObserver;
use Modules\System\Models\Notification;
use Modules\System\Observers\ActivityLogObserver;
use Modules\System\Models\ActivityLog;
use Modules\System\Commands\SystemBroadcastDeviceTelemetry;
use Modules\System\Extensions\DeviceMoldDrivers\DeviceMoldDriverManager;
use Modules\System\Extensions\DeviceMoldStrategies\StrategyManager;
use Modules\System\Extensions\RuleActionActuators\RuleActionActuatorManager;
use Modules\System\Extensions\RuleCacheManager\RuleCacheManager;
use Modules\System\Extensions\RuleCacheManager\RuleCacheManagerInterface;
use Modules\System\Extensions\RuleCacheManager\RuleCacheRedisStore;
use Modules\System\Observers\DeviceActionObserver;
use Modules\System\Models\DeviceAction;
use Modules\System\Commands\SystemCleanUploads;
use Modules\System\Commands\SystemDumpLanguages;
use Modules\System\Observers\RuleActionObserver;
use Modules\System\Models\RuleAction;
use Modules\System\Observers\RuleCriteriaObserver;
use Modules\System\Models\RuleCriteria;
use Modules\System\Observers\RuleObserver;
use Modules\System\Models\Rule;
use Modules\System\Observers\TelemetryValueObserver;
use Modules\System\Models\TelemetryValue;
use Modules\System\Observers\DevicePropertyValueObserver;
use Modules\System\Models\DevicePropertyValue;
use Modules\System\Observers\DeviceObserver;
use Modules\System\Models\Device;
use Modules\System\Observers\DeviceTelemetryFieldObserver;
use Modules\System\Models\DeviceTelemetryField;
use Modules\System\Observers\DevicePropertyFieldObserver;
use Modules\System\Models\DevicePropertyField;
use Modules\System\Observers\DeviceMoldObserver;
use Modules\System\Models\DeviceMold;
use Modules\System\Observers\DeviceTypeObserver;
use Modules\System\Models\DeviceType;
use Modules\System\Contracts\TenantPolicyManagerInterface;
use Modules\System\Observers\WatchPointObserver;
use Modules\System\Models\WatchPoint;
use Modules\System\Commands\SystemPermissionRefresh;
use Modules\System\Models\PlatformAdmin;
use Modules\System\Observers\PlatformAdminObserver;
use Modules\System\Observers\TenantObserver;
use Modules\System\Models\Tenant;
use Modules\System\Observers\TenantAdminObserver;
use Modules\System\Models\TenantAdmin;
use Modules\System\Commands\SystemPermission;
use Modules\System\Observers\RoleObserver;
use Modules\System\Models\Role;
use Modules\System\Observers\PermissionObserver;
use Modules\System\Models\Permission;
use Modules\System\Commands\SystemSetting;
use Modules\System\Observers\SettingObserver;
use Modules\System\Models\Setting;
use Modules\System\Observers\AccountObserver;
use Modules\System\Models\Account;
use Illuminate\Support\ServiceProvider;
use Modules\System\Extensions\TenantPolicyManager;
use Modules\System\Extensions\ActivityLogs\ActivityLogClient;

class SystemServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register(): void
    {
        $this->registerCommands();
        $this->registerSingletons();
        $this->registerHelpers();
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot(): void
    {
        $this->extendCache();

        Account::observe(AccountObserver::class);
        Setting::observe(SettingObserver::class);
        Permission::observe(PermissionObserver::class);
        Role::observe(RoleObserver::class);
        PlatformAdmin::observe(PlatformAdminObserver::class);
        TenantAdmin::observe(TenantAdminObserver::class);
        Tenant::observe(TenantObserver::class);
        WatchPoint::observe(WatchPointObserver::class);
        DeviceType::observe(DeviceTypeObserver::class);
        DeviceMold::observe(DeviceMoldObserver::class);
        DevicePropertyField::observe(DevicePropertyFieldObserver::class);
        DeviceTelemetryField::observe(DeviceTelemetryFieldObserver::class);
        Device::observe(DeviceObserver::class);
        DevicePropertyValue::observe(DevicePropertyValueObserver::class);
        TelemetryValue::observe(TelemetryValueObserver::class);
        Rule::observe(RuleObserver::class);
        RuleCriteria::observe(RuleCriteriaObserver::class);
        RuleAction::observe(RuleActionObserver::class);
        ActivityLog::observe(ActivityLogObserver::class);
        DeviceAction::observe(DeviceActionObserver::class);
        Notification::observe(NotificationObserver::class);
        Space::observe(SpaceObserver::class);
        TelemetryRawPerMold::observe(TelemetryRawPerMoldObserver::class);
        DeviceOnlineRecord::observe(DeviceOnlineRecordObserver::class);
        AlarmLog::observe(AlarmLogObserver::class);
        DeviceTypeCommonField::observe(DeviceTypeCommonFieldObserver::class);
        RuleTemplateAction::observe(RuleTemplateActionObserver::class);
        RuleTemplateCriteria::observe(RuleTemplateCriteriaObserver::class);
        RuleTemplate::observe(RuleTemplateObserver::class);
        StrategyTemplate::observe(StrategyTemplateObserver::class);
        Strategy::observe(StrategyObserver::class);
        TelemetryValueCommon::observe(TelemetryValueCommonObserver::class);
        CommonFieldDescriptions::observe(CommonFieldDescriptionsObserver::class);
        StrategyTemplateCategory::observe(StrategyTemplateCategoryObserver::class);
        TelemetryValueMapping::observe(TelemetryValueMappingObserver::class);
        Version::observe(VersionObserver::class);
        PinCodes::observe(PinCodesObserver::class);
        OperatorDeviceUsages::observe(OperatorDeviceUsagesObserver::class);
        ReportPlan::observe(ReportPlanObserver::class);
        Building::observe(BuildingObserver::class);
        Event::observe(EventObserver::class);
        ChannelType::observe(ChannelTypeObserver::class);
        DeviceCalibrationRecord::observe(DeviceCalibrationRecordObserver::class);
        Member::observe(MemberObserver::class);
        MemberDeviceUsage::observe(MemberDeviceUsageObserver::class);
        Team::observe(TeamObserver::class);
    }

    private function registerCommands()
    {
        $this->commands([
            SystemSetting::class,
            SystemPermission::class,
            SystemPermissionRefresh::class,
            SystemDumpLanguages::class,
            SystemCleanUploads::class,
            SystemBroadcastDeviceTelemetry::class,
            SystemNotificationTest::class,
            SystemWasm::class,
            SystemCommandTest::class,
            SystemUpdateDeviceStatus::class,
            SystemAmqpProducter::class,
            SystemSendVersionToController::class,
            SystemAmqpSubscriberWebUp::class,
            SystemRealtimeBroadcastConsumer::class,
            SystemCalibrationNotification::class,
            SystemRefreshTelemetryFieldsMapping::class,
            SystemCheckAndSendReport::class,
            SyncMemberDeviceUsages::class
        ]);
    }

    private function registerSingletons()
    {
        $this->app->singleton(TenantPolicyManagerInterface::class, TenantPolicyManager::class);
//        $this->app->singleton(RuleCacheManagerInterface::class, RuleCacheManagerDjango::class);
        $this->app->singleton(RuleCacheManagerInterface::class, RuleCacheManager::class);
        $this->app->singleton('activity.log', ActivityLogClient::class);
        
        // Register Member Data Repositories
        $this->app->singleton(\Modules\System\Repositories\MemberDataRepository::class);
        $this->app->singleton(\Modules\System\Repositories\OptimizedMemberDataRepository::class);
        $this->app->singleton(\Modules\System\Repositories\DeviceAssociationBridgeRepository::class);

        $this->registerDeviceMoldDriverSingleton();
        $this->registerDeviceMoldSingletons();
        $this->registerRuleActionActuatorManagerSingleton();
        $this->registerReactiveDeviceManagerSingleton();
    }

    private function registerHelpers()
    {
        require __DIR__ . "/helpers.php";
    }

    private function extendCache()
    {
        // Device Rule Cache
        Cache::extend('redis-rule', function ($app) {
            $prefix = 'iot-platform:';
            $connection = config('cache.stores.rule.connection', 'rule');
            return Cache::repository(new RuleCacheRedisStore($this->app['redis'], $prefix, $connection));
        });
        // Device Mold cache
        Cache::extend('redis-device-mold', function ($app) {
            $prefix = 'iot-platform:';
            $connection = config('cache.stores.device-mold.connection', 'device-mold');
            return Cache::repository(new DeviceMoldCacheRedisStore($this->app['redis'], $prefix, $connection));
        });
        // Device rules cache
        Cache::extend('redis-device-rules', function ($app) {
            $prefix = 'iot-platform:';
            $connection = config('cache.stores.device-rules.connection', 'device-rules');
            return Cache::repository(new DeviceRulesCacheRedisStore($this->app['redis'], $prefix, $connection));
        });
    }

    private function registerDeviceMoldSingletons()
    {
        $this->app->singleton('device-mold-strategy-manager', StrategyManager::class);
    }

    private function registerDeviceMoldDriverSingleton()
    {
        $this->app->singleton('device-mold-driver-manager', DeviceMoldDriverManager::class);
    }

    private function registerRuleActionActuatorManagerSingleton()
    {
        $this->app->singleton('rule-action-actuator-manager', RuleActionActuatorManager ::class);
    }

    private function registerReactiveDeviceManagerSingleton()
    {
        $this->app->singleton('reactive-device-manager', ReactiveDeviceManager::class);
    }
}
