<?php

Route::prefix('system')->group(function () {
//    Route::get('/preview-mail', function () {
//       return view('vendor.notifications.emailReportPlan')->render();
//    });
    Route::post('auth/login', ['as'   => 'System.Auth.login',
                               'uses' => '\\Modules\\System\\Controllers\\AuthController@login']);
    Route::post('pin_code_exit', ['as'   => 'System.PinCodes.Exit', 'uses' => '\\Modules\\System\\Controllers\\PinCodesController@isExit']);
    Route::post('register', ['as'   => 'System.Register', 'uses' => '\\Modules\\System\\Controllers\\AccountController@register']);
    Route::middleware('throttle:20,1')->group(function () {
        Route::post('email/code', ['as'   => 'System.Email.Code', 'uses' => '\\Modules\\System\\Controllers\\EmailCodeController@code']);
        Route::post('sms/code', ['as'   => 'System.Sms.Code', 'uses' => '\\Modules\\System\\Controllers\\SmsCodeController@code']);
    });
    Route::post('auth/forgot_password', ['as' => 'System.Auth.forgotPassword', 'uses' => '\\Modules\\System\\Controllers\\AuthController@forgotPassword']);
    Route::get('last_version', ['as' => 'System.Version.Last', 'uses' => '\\Modules\\System\\Controllers\\VersionController@lastVersion']);
    Route::post('auth/device-hub', ['as'   => 'System.Auth.deviceHub',
                                    'uses' => '\\Modules\\System\\Controllers\\AuthController@deviceHub']);
    Route::post('tenant/{id}/demo_admin', ['as' => 'System.Tenant.demoAdmin', 'uses' => '\\Modules\\System\\Controllers\\TenantController@demoAdmin']);
    Route::get('strategy/generate_rule', ['as'   => 'System.Strategy.generateRule',
        'uses' => '\\Modules\\System\\Controllers\\StrategyController@generateRule']);
    Route::get('strategy/update_rules', ['as'   => 'System.Strategy.updateRuleEngineRules',
        'uses' => '\\Modules\\System\\Controllers\\StrategyController@updateRuleEngineRules']);
    
    // Stream authentication endpoint for Oryx/SRS integration (no authentication required)
    Route::post('stream/auth', ['as' => 'System.Stream.auth', 'uses' => '\\Modules\\System\\Controllers\\TenantController@streamAuth']);
    
    Route::middleware('auth.sign')->group(function () {
        Route::post('account/device_token', ['as'   => 'System.Account.getDeviceToken',
            'uses' => '\\Modules\\System\\Controllers\\AccountController@getDeviceToken']);
        Route::get('device_mold/devices', ['as'   => 'System.DeviceMold.getDevicesByCode',
            'uses' => '\\Modules\\System\\Controllers\\DeviceMoldController@getDevicesByCode']);
        Route::post('device_online_record/update_by_device_identifier', ['as'   => 'System.DeviceOnlineRecord.updateDeviceOnlineRecord',
            'uses' => '\\Modules\\System\\Controllers\\DeviceOnlineRecordController@updateDeviceOnlineRecord']);
        Route::post('device/update_by_identifier', ['as'   => 'System.Device.updateDevicesStatus',
            'uses' => '\\Modules\\System\\Controllers\\DeviceController@updateDevicesStatus']);
        Route::get('telemetry_value_common/aircondition_on_off_records', ['as'   => 'System.TelemetryValueCommon.onOffRecords',
            'uses' => '\\Modules\\System\\Controllers\\TelemetryValueCommonController@onOffRecords']);
        Route::get('tenant/tenants_coords', ['as'   => 'System.TenantController.tenantsCoords',
            'uses' => '\\Modules\\System\\Controllers\\TenantController@tenantsCoords']);
        Route::get('rule_action/{id}/devices', ['as'   => 'System.RuleAction.devices',
            'uses' => '\\Modules\\System\\Controllers\\RuleActionController@devices']);
        Route::get('device_type/watch_points_under_pir_strategy', ['as'   => 'System.DeviceType.getWatchPointsWithPirUnderStrategy',
            'uses' => '\\Modules\\System\\Controllers\\DeviceTypeController@getWatchPointsWithPirUnderStrategy']);
        Route::get('device/{id}/related_rules', ['as'   => 'System.Device.relatedRules',
            'uses' => '\\Modules\\System\\Controllers\\DeviceController@relatedRules']);
        Route::get('strategy/need_judged_devices', ['as'   => 'System.Strategy.allNeedJudgeDevices',
            'uses' => '\\Modules\\System\\Controllers\\StrategyController@allNeedJudgeDevices']);
        Route::post('device/check', ['as'   => 'System.Device.check',
            'uses' => '\\Modules\\System\\Controllers\\DeviceController@check']);
        Route::post('device/upload_image_by_identifier', ['as'   => 'System.Device.uploadImageByIdentifier',
            'uses' => '\\Modules\\System\\Controllers\\DeviceController@uploadImageByIdentifier']);
    });
    Route::middleware('auth:api', 'auth.oneSession')->group(function () {
        Route::post('auth/logout', ['as'   => 'System.Auth.logout',
                                    'uses' => '\\Modules\\System\\Controllers\\AuthController@logout']);
        Route::post('auth/refresh', ['as'   => 'System.Auth.refresh',
                                     'uses' => '\\Modules\\System\\Controllers\\AuthController@refresh']);
        Route::get('auth/me', ['as'   => 'System.Auth.me',
                               'uses' => '\\Modules\\System\\Controllers\\AuthController@me']);

        Route::post('account/evacuated', ['as' => 'System.Account.Evacuated', 'uses' => '\\Modules\\System\\Controllers\\AccountController@evacuated']);
        Route::put('account/avatar', ['as'   => 'System.Account.avatar',
                                      'uses' => '\\Modules\\System\\Controllers\\AccountController@avatar']);
        Route::put('account/password', ['as'   => 'System.Account.password',
                                        'uses' => '\\Modules\\System\\Controllers\\AccountController@password']);
        Route::get('account', ['as'   => 'System.Account.index',
                               'uses' => '\\Modules\\System\\Controllers\\AccountController@index']);
        Route::get('account/{id}', ['as'   => 'System.Account.show',
                                    'uses' => '\\Modules\\System\\Controllers\\AccountController@show']);
        Route::post('account', ['as'   => 'System.Account.store',
                                'uses' => '\\Modules\\System\\Controllers\\AccountController@store']);
        Route::post('account/import', ['as'   => 'System.Account.import',
                                'uses' => '\\Modules\\System\\Controllers\\AccountController@import']);
        Route::put('account/{id}', ['as'   => 'System.Account.update',
                                    'uses' => '\\Modules\\System\\Controllers\\AccountController@update']);
        Route::delete('account/{id}', ['as'   => 'System.Account.destroy',
                                       'uses' => '\\Modules\\System\\Controllers\\AccountController@destroy']);

        Route::put('platform_admin/myself', ['as'   => 'System.PlatformAdmin.myself',
                                             'uses' => '\\Modules\\System\\Controllers\\PlatformAdminController@myself']);
        Route::get('platform_admin', ['as'   => 'System.PlatformAdmin.index',
                                      'uses' => '\\Modules\\System\\Controllers\\PlatformAdminController@index']);
        Route::get('platform_admin/{id}', ['as'   => 'System.PlatformAdmin.show',
                                           'uses' => '\\Modules\\System\\Controllers\\PlatformAdminController@show']);
        Route::post('platform_admin', ['as'   => 'System.PlatformAdmin.store',
                                       'uses' => '\\Modules\\System\\Controllers\\PlatformAdminController@store']);
        Route::put('platform_admin/{id}', ['as'   => 'System.PlatformAdmin.update',
                                           'uses' => '\\Modules\\System\\Controllers\\PlatformAdminController@update']);
        Route::delete('platform_admin/{id}', ['as'   => 'System.PlatformAdmin.destroy',
                                              'uses' => '\\Modules\\System\\Controllers\\PlatformAdminController@destroy']);
        Route::post('platform_admin/batch_delete', ['as' => 'System.PlatformAdmin.destroyList',
                                                      'uses' => '\\Modules\\System\\Controllers\\PlatformAdminController@destroyList']);

        Route::get('setting/default', ['as'   => 'System.Setting.default.index',
                                       'uses' => '\\Modules\\System\\Controllers\\SettingController@defaultIndex']);
        Route::get('setting/default/{id}', ['as'   => 'System.Setting.default.show',
                                            'uses' => '\\Modules\\System\\Controllers\\SettingController@defaultShow']);
        Route::post('setting/default', ['as'   => 'System.Setting.default.store',
                                        'uses' => '\\Modules\\System\\Controllers\\SettingController@defaultStore']);
        Route::put('setting/default/{id}', ['as'   => 'System.Setting.default.update',
                                            'uses' => '\\Modules\\System\\Controllers\\SettingController@defaultUpdate']);
        Route::put('setting/translate', ['as'   => 'System.Setting.translate',
                                         'uses' => '\\Modules\\System\\Controllers\\SettingController@translate']);

        Route::get('permission/platform', ['as'   => 'System.Permission.platform',
                                           'uses' => '\\Modules\\System\\Controllers\\PermissionController@platform']);
        Route::get('permission/tenant', ['as'   => 'System.Permission.tenant',
                                         'uses' => '\\Modules\\System\\Controllers\\PermissionController@tenant']);
        Route::put('permission/translate', ['as'   => 'System.Permission.translate',
                                            'uses' => '\\Modules\\System\\Controllers\\PermissionController@translate']);

        Route::get('role', ['as'   => 'System.Role.index',
                            'uses' => '\\Modules\\System\\Controllers\\RoleController@index']);
        Route::get('role/{id}', ['as'   => 'System.Role.show',
                                 'uses' => '\\Modules\\System\\Controllers\\RoleController@show']);
        Route::post('role', ['as'   => 'System.Role.store',
                             'uses' => '\\Modules\\System\\Controllers\\RoleController@store']);
        Route::put('role/{id}', ['as'   => 'System.Role.update',
                                 'uses' => '\\Modules\\System\\Controllers\\RoleController@update']);
        Route::delete('role/{id}', ['as'   => 'System.Role.destroy',
                                    'uses' => '\\Modules\\System\\Controllers\\RoleController@destroy']);

        Route::put('tenant_admin/myself', ['as'   => 'System.TenantAdmin.myself',
                                           'uses' => '\\Modules\\System\\Controllers\\TenantAdminController@myself']);
        Route::get('tenant_admin', ['as'   => 'System.TenantAdmin.index',
                                    'uses' => '\\Modules\\System\\Controllers\\TenantAdminController@index']);
        Route::get('tenant_admin/{id}', ['as'   => 'System.TenantAdmin.show',
                                         'uses' => '\\Modules\\System\\Controllers\\TenantAdminController@show']);
        Route::put('tenant_admin/{id}', ['as'   => 'System.TenantAdmin.update',
                                         'uses' => '\\Modules\\System\\Controllers\\TenantAdminController@update']);
        Route::delete('tenant_admin/{id}', ['as'   => 'System.TenantAdmin.destroy',
                                            'uses' => '\\Modules\\System\\Controllers\\TenantAdminController@destroy']);
        Route::put('tenant_admin/{id}/login_as', ['as'   => 'System.TenantAdmin.loginAs',
                                                  'uses' => '\\Modules\\System\\Controllers\\TenantAdminController@loginAs']);
        Route::post('tenant_admin/batch_delete', ['as'   => 'System.TenantAdmin.destroyList',
                                                  'uses' => '\\Modules\\System\\Controllers\\TenantAdminController@destroyList']);

        Route::get('tenant', ['as'   => 'System.Tenant.index',
                              'uses' => '\\Modules\\System\\Controllers\\TenantController@index']);
        Route::get('tenant/{id}', ['as'   => 'System.Tenant.show',
                                   'uses' => '\\Modules\\System\\Controllers\\TenantController@show']);
        Route::post('tenant', ['as'   => 'System.Tenant.store',
                               'uses' => '\\Modules\\System\\Controllers\\TenantController@store']);
        Route::put('tenant/{id}', ['as'   => 'System.Tenant.update',
                                   'uses' => '\\Modules\\System\\Controllers\\TenantController@update']);
        Route::delete('tenant/{id}', ['as'   => 'System.Tenant.destroy',
                                      'uses' => '\\Modules\\System\\Controllers\\TenantController@destroy']);
        Route::post('tenant/{id}/sub_tenant', ['as'   => 'System.Tenant.storeSubTenant',
                                               'uses' => '\\Modules\\System\\Controllers\\TenantController@storeSubTenant']);
        Route::post('tenant/{id}/role', ['as'   => 'System.Tenant.Role.store',
                                         'uses' => '\\Modules\\System\\Controllers\\TenantController@storeTenantRole']);
        Route::get('tenant/{id}/role', ['as'   => 'System.Tenant.Role.index',
                                        'uses' => '\\Modules\\System\\Controllers\\TenantController@indexTenantRole']);
        Route::post('tenant/{id}/tenant_admin', ['as'   => 'System.Tenant.TenantAdmin.store',
                                                 'uses' => '\\Modules\\System\\Controllers\\TenantController@storeTenantAdmin']);
        Route::get('tenant/{id}/tenant_admin', ['as'   => 'System.Tenant.TenantAdmin.index',
                                                'uses' => '\\Modules\\System\\Controllers\\TenantController@indexTenantAdmin']);
        Route::post('tenant/{id}/space', ['as'   => 'System.Tenant.Space.store',
                                          'uses' => '\\Modules\\System\\Controllers\\TenantController@storeSpace']);
        Route::get('tenant/{id}/space', ['as'   => 'System.Tenant.Space.index',
                                         'uses' => '\\Modules\\System\\Controllers\\TenantController@indexSpace']);
        Route::post('tenant/{id}/rule', ['as'   => 'System.Tenant.Rule.store',
                                         'uses' => '\\Modules\\System\\Controllers\\TenantController@storeRule']);
        Route::get('tenant/{id}/rule', ['as'   => 'System.Tenant.Rule.index',
                                        'uses' => '\\Modules\\System\\Controllers\\TenantController@indexRule']);
        Route::post('tenant/{id}/device', ['as'   => 'System.Tenant.Device.store',
                                           'uses' => '\\Modules\\System\\Controllers\\TenantController@storeDevice']);
        Route::get('tenant/{id}/device', ['as'   => 'System.Tenant.Device.index',
                                          'uses' => '\\Modules\\System\\Controllers\\TenantController@indexDevice']);
        Route::get('tenant/{id}/strategy_template_categories', ['as' => 'System.TenantController.strategyTemplateCategories',
            'uses' => '\\Modules\\System\\Controllers\\TenantController@strategyTemplateCategories']);
        Route::get('tenant/{id}/strategy', ['as' => 'System.TenantController.indexStrategy',
            'uses' => '\\Modules\\System\\Controllers\\TenantController@indexStrategy']);
        Route::post('tenant_send_evacuate', ['as' => 'System.TenantController.sendEvacuate',
            'uses' => '\\Modules\\System\\Controllers\\TenantController@sendEvacuate']);
        Route::get('tenant_resource_policy', ['as'   => 'System.Tenant.resourcePolicy',
                                              'uses' => '\\Modules\\System\\Controllers\\TenantController@resourcePolicy']);
        Route::post('tenant_operator_status', ['as'   => 'System.Tenant.operatorStatus',
            'uses' => '\\Modules\\System\\Controllers\\TenantController@operatorStatus']);

        Route::get('space/{id}', ['as'   => 'System.Space.show',
                                  'uses' => '\\Modules\\System\\Controllers\\SpaceController@show']);
        Route::put('space/{id}', ['as'   => 'System.Space.update',
                                  'uses' => '\\Modules\\System\\Controllers\\SpaceController@update']);
        Route::delete('space/{id}', ['as'   => 'System.Space.destroy',
                                     'uses' => '\\Modules\\System\\Controllers\\SpaceController@destroy']);
        Route::post('space/{id}/watch_point', ['as'   => 'System.Space.WatchPoint.store',
                                               'uses' => '\\Modules\\System\\Controllers\\SpaceController@storeWatchPoint']);
        Route::get('space/{id}/watch_point', ['as'   => 'System.Space.WatchPoint.index',
                                              'uses' => '\\Modules\\System\\Controllers\\SpaceController@indexWatchPoint']);
        Route::get('space/{id}/tenant_admin', ['as'   => 'System.Space.TenantAdmin.index',
                                               'uses' => '\\Modules\\System\\Controllers\\SpaceController@indexTenantAdmin']);
        Route::put('space/{spaceId}/assign_tenant_admin', ['as'   => 'System.Space.assignTenantAdmin',
                                                           'uses' => '\\Modules\\System\\Controllers\\SpaceController@assignTenantAdmin']);
        Route::delete('space/{spaceId}/tenant_admin/{tenantAdminId}', ['as'   => 'System.Space.removeTenantAdmin',
                                                                       'uses' => '\\Modules\\System\\Controllers\\SpaceController@removeTenantAdmin']);
        Route::get('space/{id}/device', ['as'   => 'System.Space.Device.index',
                                         'uses' => '\\Modules\\System\\Controllers\\SpaceController@indexDevice']);

        Route::get('watch_point', ['as'   => 'System.WatchPoint.index',
                                   'uses' => '\\Modules\\System\\Controllers\\WatchPointController@index']);
        Route::get('watch_point/{id}', ['as'   => 'System.WatchPoint.show',
                                        'uses' => '\\Modules\\System\\Controllers\\WatchPointController@show']);
        Route::put('watch_point/{id}', ['as'   => 'System.WatchPoint.update',
                                        'uses' => '\\Modules\\System\\Controllers\\WatchPointController@update']);
        Route::delete('watch_point/{id}', ['as'   => 'System.WatchPoint.destroy',
                                           'uses' => '\\Modules\\System\\Controllers\\WatchPointController@destroy']);
        Route::get('watch_point/{id}/device', ['as'   => 'System.WatchPoint.Device.index',
                                               'uses' => '\\Modules\\System\\Controllers\\WatchPointController@indexDevice']);
        Route::get('watch_point/{id}/energy_consumption', ['as'   => 'System.WatchPointController.energyConsumption',
            'uses' => '\\Modules\\System\\Controllers\\WatchPointController@energyConsumption']);
        Route::get('watch_point/{id}/status_record_statistics', ['as'   => 'System.WatchPointController.statusRecordStatistics',
            'uses' => '\\Modules\\System\\Controllers\\WatchPointController@statusRecordStatistics']);
        Route::get('device_type', ['as'   => 'System.DeviceType.index',
                                   'uses' => '\\Modules\\System\\Controllers\\DeviceTypeController@index']);
        Route::get('device_type/{id}', ['as'   => 'System.DeviceType.show',
                                        'uses' => '\\Modules\\System\\Controllers\\DeviceTypeController@show']);
        Route::post('device_type', ['as'   => 'System.DeviceType.store',
                                    'uses' => '\\Modules\\System\\Controllers\\DeviceTypeController@store']);
        Route::put('device_type/{id}', ['as'   => 'System.DeviceType.update',
                                        'uses' => '\\Modules\\System\\Controllers\\DeviceTypeController@update']);
        Route::delete('device_type/{id}', ['as'   => 'System.DeviceType.destroy',
                                           'uses' => '\\Modules\\System\\Controllers\\DeviceTypeController@destroy']);

        Route::get('device_mold', ['as'   => 'System.DeviceMold.index',
                                   'uses' => '\\Modules\\System\\Controllers\\DeviceMoldController@index']);
        Route::get('device_mold/{id}', ['as'   => 'System.DeviceMold.show',
                                        'uses' => '\\Modules\\System\\Controllers\\DeviceMoldController@show']);
        Route::post('device_mold', ['as'   => 'System.DeviceMold.store',
                                    'uses' => '\\Modules\\System\\Controllers\\DeviceMoldController@store']);
        Route::put('device_mold/{id}', ['as'   => 'System.DeviceMold.update',
                                        'uses' => '\\Modules\\System\\Controllers\\DeviceMoldController@update']);
        Route::delete('device_mold/{id}', ['as'   => 'System.DeviceMold.destroy',
                                           'uses' => '\\Modules\\System\\Controllers\\DeviceMoldController@destroy']);
        Route::post('device_mold/{id}/property_field', ['as'   => 'System.DeviceMold.DevicePropertyField.store',
                                                        'uses' => '\\Modules\\System\\Controllers\\DeviceMoldController@storeDevicePropertyField']);
        Route::get('device_mold/{id}/property_field', ['as'   => 'System.DeviceMold.DevicePropertyField.index',
                                                       'uses' => '\\Modules\\System\\Controllers\\DeviceMoldController@indexDevicePropertyField']);
        Route::post('device_mold/{id}/telemetry_field', ['as'   => 'System.DeviceMold.DeviceTelemetryField.store',
                                                         'uses' => '\\Modules\\System\\Controllers\\DeviceMoldController@storeDeviceTelemetryField']);
        Route::get('device_mold/{id}/telemetry_field', ['as'   => 'System.DeviceMold.DeviceTelemetryField.index',
                                                        'uses' => '\\Modules\\System\\Controllers\\DeviceMoldController@indexDeviceTelemetryField']);
        Route::post('device_mold/{id}/action', ['as'   => 'System.DeviceMold.DeviceAction.store',
                                                'uses' => '\\Modules\\System\\Controllers\\DeviceMoldController@storeDeviceAction']);
        Route::get('device_mold/{id}/action', ['as'   => 'System.DeviceMold.DeviceAction.index',
                                               'uses' => '\\Modules\\System\\Controllers\\DeviceMoldController@indexDeviceAction']);
        Route::put('device_mold/{id}/authentication_strategy/{property_name}', ['as'   => 'System.DeviceMold.authenticationStrategy',
                                                                                'uses' => '\\Modules\\System\\Controllers\\DeviceMoldController@authenticationStrategy']);
        Route::put('device_mold/{id}/transmission_strategy/{property_name}', ['as'   => 'System.DeviceMold.transmissionStrategy',
                                                                              'uses' => '\\Modules\\System\\Controllers\\DeviceMoldController@transmissionStrategy']);
        Route::put('device_mold/{id}/third_party_data_strategy/{property_name}', ['as' => 'System.DeviceMold.thirdPartyDataStrategy',
                                                                                  'uses' => '\\Modules\\System\\Controllers\\DeviceMoldController@thirdPartyDataStrategy']);
        Route::get('device_mold/{id}/latest-telemetry-raw', ['as' => 'System.DeviceMold.LatestTelemetryRaw',
                                                             'uses' => '\\Modules\\System\\Controllers\\DeviceMoldController@latestTelemetryRaw']);
        Route::get('device_mold_drivers', ['as'   => 'System.DeviceMold.drivers',
                                           'uses' => '\\Modules\\System\\Controllers\\DeviceMoldController@drivers']);
        Route::put('device_mold/{id}/test_parser', ['as' => 'System.DeviceMold.testParser',
                                                    'uses' => '\\Modules\\System\\Controllers\\DeviceMoldController@testParser']);


        Route::get('device_property_field/{id}', ['as'   => 'System.DevicePropertyField.show',
                                                  'uses' => '\\Modules\\System\\Controllers\\DevicePropertyFieldController@show']);
        Route::put('device_property_field/{id}', ['as'   => 'System.DevicePropertyField.update',
                                                  'uses' => '\\Modules\\System\\Controllers\\DevicePropertyFieldController@update']);
        Route::delete('device_property_field/{id}', ['as'   => 'System.DevicePropertyField.destroy',
                                                     'uses' => '\\Modules\\System\\Controllers\\DevicePropertyFieldController@destroy']);

        Route::get('device_telemetry_field/{id}', ['as'   => 'System.DeviceTelemetryField.show',
                                                   'uses' => '\\Modules\\System\\Controllers\\DeviceTelemetryFieldController@show']);
        Route::put('device_telemetry_field/{id}', ['as'   => 'System.DeviceTelemetryField.update',
                                                   'uses' => '\\Modules\\System\\Controllers\\DeviceTelemetryFieldController@update']);
        Route::delete('device_telemetry_field/{id}', ['as'   => 'System.DeviceTelemetryField.destroy',
                                                      'uses' => '\\Modules\\System\\Controllers\\DeviceTelemetryFieldController@destroy']);

        Route::get('device', ['as'   => 'System.Device.index',
                              'uses' => '\\Modules\\System\\Controllers\\DeviceController@index']);
        Route::get('device_overview', ['as'   => 'System.Device.indexForOverview',
        'uses' => '\\Modules\\System\\Controllers\\DeviceController@indexForOverview']);
        Route::get('device/{id}', ['as'   => 'System.Device.show',
                                   'uses' => '\\Modules\\System\\Controllers\\DeviceController@show']);
        Route::get('device/identifier/{identifier}', ['as'   => 'System.Device.showByIdentifier',
                                                       'uses' => '\\Modules\\System\\Controllers\\DeviceController@showByIdentifier']);
        Route::put('device/{id}', ['as'   => 'System.Device.update',
                                   'uses' => '\\Modules\\System\\Controllers\\DeviceController@update']);
        Route::delete('device/{id}', ['as'   => 'System.Device.destroy',
                                      'uses' => '\\Modules\\System\\Controllers\\DeviceController@destroy']);
        Route::post('device/destroy_list', ['as'   => 'System.Device.destroyList',
                                      'uses' => '\\Modules\\System\\Controllers\\DeviceController@destroyList']);
        Route::put('device/{id}/property_field/{fieldId}', ['as'   => 'System.Device.PropertyValue.update',
                                                            'uses' => '\\Modules\\System\\Controllers\\DeviceController@updatePropertyFieldValue']);
        Route::put('device/{deviceId}/action/{actionId}', ['as'   => 'System.Device.action',
                                                           'uses' => '\\Modules\\System\\Controllers\\DeviceController@action']);
        Route::get('device/{id}/telemetry_value', ['as'   => 'System.Device.telemetryValue',
                                                   'uses' => '\\Modules\\System\\Controllers\\DeviceController@telemetryValue']);
        Route::put('device/{id}/assign_tenant_admin', ['as'   => 'System.Device.assignTenantAdmin',
                                                       'uses' => '\\Modules\\System\\Controllers\\DeviceController@assignTenantAdmin']);
        Route::delete('device/{id}/tenant_admin/{tenantAdminId}', ['as'   => 'System.Device.removeTenantAdmin',
                                                                   'uses' => '\\Modules\\System\\Controllers\\DeviceController@removeTenantAdmin']);
        Route::post('device/send_command', ['as'   => 'System.Device.sendCommand',
            'uses' => '\\Modules\\System\\Controllers\\DeviceController@sendCommand']);
        Route::post('device/import', ['as' => 'System.Device.import', 'uses' => '\\Modules\\System\\Controllers\\DeviceController@import']);
        Route::get('device_calibration', ['as'   => 'System.Device.calibration','uses' => '\\Modules\\System\\Controllers\\DeviceController@calibration']);
        Route::get('device_property_value/{id}', ['as'   => 'System.DevicePropertyValue.show',
                                                  'uses' => '\\Modules\\System\\Controllers\\DevicePropertyValueController@show']);
        Route::put('device_property_value/{id}', ['as'   => 'System.DevicePropertyValue.update',
                                                  'uses' => '\\Modules\\System\\Controllers\\DevicePropertyValueController@update']);
        Route::delete('device_property_value/{id}', ['as'   => 'System.DevicePropertyValue.destroy',
                                                     'uses' => '\\Modules\\System\\Controllers\\DevicePropertyValueController@destroy']);

        Route::get('device_action/{id}', ['as'   => 'System.DeviceAction.show',
                                          'uses' => '\\Modules\\System\\Controllers\\DeviceActionController@show']);
        Route::put('device_action/{id}', ['as'   => 'System.DeviceAction.update',
                                          'uses' => '\\Modules\\System\\Controllers\\DeviceActionController@update']);
        Route::delete('device_action/{id}', ['as'   => 'System.DeviceAction.destroy',
                                             'uses' => '\\Modules\\System\\Controllers\\DeviceActionController@destroy']);
        Route::get('rule', ['as'   => 'System.Rule.index',
                            'uses' => '\\Modules\\System\\Controllers\\RuleController@index']);
        Route::post('rule', ['as'   => 'System.Rule.store',
            'uses' => '\\Modules\\System\\Controllers\\RuleController@store']);
        Route::get('rule/{id}', ['as'   => 'System.Rule.show',
                                 'uses' => '\\Modules\\System\\Controllers\\RuleController@show']);
        Route::put('rule/{id}', ['as'   => 'System.Rule.update',
                                 'uses' => '\\Modules\\System\\Controllers\\RuleController@update']);
        Route::delete('rule/{id}', ['as'   => 'System.Rule.destroy',
                                    'uses' => '\\Modules\\System\\Controllers\\RuleController@destroy']);
        Route::get('rule/{id}/rule_action', ['as'   => 'System.Rule.RuleAction.index',
                                             'uses' => '\\Modules\\System\\Controllers\\RuleController@indexRuleAction']);
        Route::post('rule/{id}/rule_action', ['as'   => 'System.Rule.RuleAction.store',
                                              'uses' => '\\Modules\\System\\Controllers\\RuleController@storeRuleAction']);
        Route::get('rule_action/{id}', ['as'   => 'System.RuleAction.show',
                                        'uses' => '\\Modules\\System\\Controllers\\RuleActionController@show']);
        Route::put('rule_action/{id}', ['as'   => 'System.RuleAction.update',
                                        'uses' => '\\Modules\\System\\Controllers\\RuleActionController@update']);
        Route::delete('rule_action/{id}', ['as'   => 'System.RuleAction.destroy',
                                           'uses' => '\\Modules\\System\\Controllers\\RuleActionController@destroy']);

        Route::get('activity_log', ['as'   => 'System.ActivityLog.index',
                                    'uses' => '\\Modules\\System\\Controllers\\ActivityLogController@index']);
        Route::get('activity_log/myself', ['as'   => 'System.ActivityLog.myself',
                                           'uses' => '\\Modules\\System\\Controllers\\ActivityLogController@myself']);
        Route::get('activity_log/tenant', ['as'   => 'System.ActivityLog.tenant',
                                           'uses' => '\\Modules\\System\\Controllers\\ActivityLogController@tenant']);

        Route::get('upload', ['as'   => 'System.Upload.index',
                              'uses' => '\\Modules\\System\\Controllers\\UploadController@index']);
        Route::post('upload', ['as'   => 'System.Upload.store',
                               'uses' => '\\Modules\\System\\Controllers\\UploadController@store']);
        Route::get('upload/{id}', ['as'   => 'System.Upload.show',
                                   'uses' => '\\Modules\\System\\Controllers\\UploadController@show']);
        Route::put('upload/{id}', ['as'   => 'System.Upload.update',
                                   'uses' => '\\Modules\\System\\Controllers\\UploadController@update']);
        Route::delete('upload/{id}', ['as'   => 'System.Upload.destroy',
                                      'uses' => '\\Modules\\System\\Controllers\\UploadController@destroy']);

        Route::get('notification', ['as'   => 'System.Notification.index',
                                    'uses' => '\\Modules\\System\\Controllers\\NotificationController@index']);
        Route::get('notification/{id}', ['as'   => 'System.Notification.show',
                                         'uses' => '\\Modules\\System\\Controllers\\NotificationController@show']);
        Route::put('notification/{id}/read', ['as'   => 'System.Notification.read',
                                              'uses' => '\\Modules\\System\\Controllers\\NotificationController@read']);
        Route::delete('notification/{id}', ['as'   => 'System.Notification.destroy',
                                            'uses' => '\\Modules\\System\\Controllers\\NotificationController@destroy']);
        Route::put('notification/read_all', ['as'   => 'System.Notification.readAll',
                                            'uses' => '\\Modules\\System\\Controllers\\NotificationController@readAll']);
        Route::get('export/accounts', ['as'   => 'System.Export.accounts',
                                       'uses' => '\\Modules\\System\\Controllers\\ExportController@accounts']);
        Route::get('export/accounts_template', ['as'   => 'System.Export.accountsTemplate',
            'uses' => '\\Modules\\System\\Controllers\\ExportController@accountsTemplate']);
        Route::get('export/plcs_template', ['as'   => 'System.Export.plcsTemplate',
            'uses' => '\\Modules\\System\\Controllers\\ExportController@plcsTemplate']);

        Route::get('export/devices', ['as'   => 'System.Export.devices', 'uses' => '\\Modules\\System\\Controllers\\ExportController@devices']);
        Route::get('export/platform_admin', ['as'   => 'System.Export.platformAdmin',
                                             'uses' => '\\Modules\\System\\Controllers\\ExportController@platformAdmin']);
        Route::get('export/alarm_logs', ['as'   => 'System.Export.alarmLogs',
            'uses' => '\\Modules\\System\\Controllers\\ExportController@alarmLogs']);
        Route::get('device_online_record', ['as' => 'System.DeviceOnlineRecord.index', 'uses' => '\\Modules\\System\\Controllers\\DeviceOnlineRecordController@index']);
        Route::get('device_online_record/{id}', ['as' => 'System.DeviceOnlineRecord.show', 'uses' => '\\Modules\\System\\Controllers\\DeviceOnlineRecordController@show']);
        Route::post('device_online_record', ['as' => 'System.DeviceOnlineRecord.store', 'uses' => '\\Modules\\System\\Controllers\\DeviceOnlineRecordController@store']);
        Route::put('device_online_record/{id}', ['as' => 'System.DeviceOnlineRecord.update', 'uses' => '\\Modules\\System\\Controllers\\DeviceOnlineRecordController@update']);
        Route::delete('device_online_record/{id}', ['as' => 'System.DeviceOnlineRecord.destroy', 'uses' => '\\Modules\\System\\Controllers\\DeviceOnlineRecordController@destroy']);
        Route::get('device/overview/overview', ['as' => 'System.Device.overview', 'uses' => '\\Modules\\System\\Controllers\\DeviceController@overview']);
        Route::get('Tenant/{id}/deviceStatistics', ['as' => 'System.Tenant.deviceStatistics', 'uses' => '\\Modules\\System\\Controllers\\TenantController@deviceStatistics']);
        Route::get('alarm_log', ['as' => 'System.AlarmLog.index', 'uses' => '\\Modules\\System\\Controllers\\AlarmLogController@index']);
        Route::get('alarm_log/{id}', ['as' => 'System.AlarmLog.show', 'uses' => '\\Modules\\System\\Controllers\\AlarmLogController@show']);
        Route::post('alarm_log', ['as' => 'System.AlarmLog.store', 'uses' => '\\Modules\\System\\Controllers\\AlarmLogController@store']);
        Route::put('alarm_log/{id}', ['as' => 'System.AlarmLog.update', 'uses' => '\\Modules\\System\\Controllers\\AlarmLogController@update']);
        Route::delete('alarm_log/{id}', ['as' => 'System.AlarmLog.destroy', 'uses' => '\\Modules\\System\\Controllers\\AlarmLogController@destroy']);
        Route::get('device_type_common_field', ['as' => 'System.DeviceTypeCommonField.index', 'uses' => '\\Modules\\System\\Controllers\\DeviceTypeCommonFieldController@index']);
        Route::get('device_type_common_field/{id}', ['as' => 'System.DeviceTypeCommonField.show', 'uses' => '\\Modules\\System\\Controllers\\DeviceTypeCommonFieldController@show']);
        Route::post('device_type_common_field', ['as' => 'System.DeviceTypeCommonField.store', 'uses' => '\\Modules\\System\\Controllers\\DeviceTypeCommonFieldController@store']);
        Route::put('device_type_common_field/{id}', ['as' => 'System.DeviceTypeCommonField.update', 'uses' => '\\Modules\\System\\Controllers\\DeviceTypeCommonFieldController@update']);
        Route::delete('device_type_common_field/{id}', ['as' => 'System.DeviceTypeCommonField.destroy', 'uses' => '\\Modules\\System\\Controllers\\DeviceTypeCommonFieldController@destroy']);
        Route::get('rule_template_action', ['as' => 'System.RuleTemplateAction.index', 'uses' => '\\Modules\\System\\Controllers\\RuleTemplateActionController@index']);
        Route::get('rule_template_action/{id}', ['as' => 'System.RuleTemplateAction.show', 'uses' => '\\Modules\\System\\Controllers\\RuleTemplateActionController@show']);
        Route::post('rule_template_action', ['as' => 'System.RuleTemplateAction.store', 'uses' => '\\Modules\\System\\Controllers\\RuleTemplateActionController@store']);
        Route::put('rule_template_action/{id}', ['as' => 'System.RuleTemplateAction.update', 'uses' => '\\Modules\\System\\Controllers\\RuleTemplateActionController@update']);
        Route::delete('rule_template_action/{id}', ['as' => 'System.RuleTemplateAction.destroy', 'uses' => '\\Modules\\System\\Controllers\\RuleTemplateActionController@destroy']);
        Route::get('rule_template_criteria', ['as' => 'System.RuleTemplateCriteria.index', 'uses' => '\\Modules\\System\\Controllers\\RuleTemplateCriteriaController@index']);
        Route::get('rule_template_criteria/{id}', ['as' => 'System.RuleTemplateCriteria.show', 'uses' => '\\Modules\\System\\Controllers\\RuleTemplateCriteriaController@show']);
        Route::post('rule_template_criteria', ['as' => 'System.RuleTemplateCriteria.store', 'uses' => '\\Modules\\System\\Controllers\\RuleTemplateCriteriaController@store']);
        Route::put('rule_template_criteria/{id}', ['as' => 'System.RuleTemplateCriteria.update', 'uses' => '\\Modules\\System\\Controllers\\RuleTemplateCriteriaController@update']);
        Route::delete('rule_template_criteria/{id}', ['as' => 'System.RuleTemplateCriteria.destroy', 'uses' => '\\Modules\\System\\Controllers\\RuleTemplateCriteriaController@destroy']);
        Route::get('rule_template', ['as' => 'System.RuleTemplate.index', 'uses' => '\\Modules\\System\\Controllers\\RuleTemplateController@index']);
        Route::get('rule_template/{id}', ['as' => 'System.RuleTemplate.show', 'uses' => '\\Modules\\System\\Controllers\\RuleTemplateController@show']);
        Route::post('rule_template', ['as' => 'System.RuleTemplate.store', 'uses' => '\\Modules\\System\\Controllers\\RuleTemplateController@store']);
        Route::put('rule_template/{id}', ['as' => 'System.RuleTemplate.update', 'uses' => '\\Modules\\System\\Controllers\\RuleTemplateController@update']);
        Route::delete('rule_template/{id}', ['as' => 'System.RuleTemplate.destroy', 'uses' => '\\Modules\\System\\Controllers\\RuleTemplateController@destroy']);
        Route::get('strategy_template', ['as' => 'System.StrategyTemplate.index', 'uses' => '\\Modules\\System\\Controllers\\StrategyTemplateController@index']);
        Route::get('strategy_template/{id}', ['as' => 'System.StrategyTemplate.show', 'uses' => '\\Modules\\System\\Controllers\\StrategyTemplateController@show']);
        Route::post('strategy_template/{id}/copy', ['as' => 'System.StrategyTemplate.copy', 'uses' => '\\Modules\\System\\Controllers\\StrategyTemplateController@copy']);
        Route::post('strategy_template', ['as' => 'System.StrategyTemplate.store', 'uses' => '\\Modules\\System\\Controllers\\StrategyTemplateController@store']);
        Route::put('strategy_template/{id}', ['as' => 'System.StrategyTemplate.update', 'uses' => '\\Modules\\System\\Controllers\\StrategyTemplateController@update']);
        Route::delete('strategy_template/{id}', ['as' => 'System.StrategyTemplate.destroy', 'uses' => '\\Modules\\System\\Controllers\\StrategyTemplateController@destroy']);
        Route::get('strategy', ['as' => 'System.Strategy.index', 'uses' => '\\Modules\\System\\Controllers\\StrategyController@index']);
        Route::get('strategy/{id}', ['as' => 'System.Strategy.show', 'uses' => '\\Modules\\System\\Controllers\\StrategyController@show']);
        Route::post('strategy/{id}/copy', ['as' => 'System.Strategy.copy', 'uses' => '\\Modules\\System\\Controllers\\StrategyController@copy']);
        Route::post('strategy', ['as' => 'System.Strategy.store', 'uses' => '\\Modules\\System\\Controllers\\StrategyController@store']);
        Route::put('strategy/{id}', ['as' => 'System.Strategy.update', 'uses' => '\\Modules\\System\\Controllers\\StrategyController@update']);
        Route::delete('strategy/{id}', ['as' => 'System.Strategy.destroy', 'uses' => '\\Modules\\System\\Controllers\\StrategyController@destroy']);
        Route::get('telemetry_value_common', ['as' => 'System.TelemetryValueCommon.index', 'uses' => '\\Modules\\System\\Controllers\\TelemetryValueCommonController@index']);
        Route::get('telemetry_value_common/{id}', ['as' => 'System.TelemetryValueCommon.show', 'uses' => '\\Modules\\System\\Controllers\\TelemetryValueCommonController@show']);
        Route::post('telemetry_value_common', ['as' => 'System.TelemetryValueCommon.store', 'uses' => '\\Modules\\System\\Controllers\\TelemetryValueCommonController@store']);
        Route::put('telemetry_value_common/{id}', ['as' => 'System.TelemetryValueCommon.update', 'uses' => '\\Modules\\System\\Controllers\\TelemetryValueCommonController@update']);
        Route::delete('telemetry_value_common/{id}', ['as' => 'System.TelemetryValueCommon.destroy', 'uses' => '\\Modules\\System\\Controllers\\TelemetryValueCommonController@destroy']);
        Route::get('common_field_descriptions', ['as' => 'System.CommonFieldDescriptions.index', 'uses' => '\\Modules\\System\\Controllers\\CommonFieldDescriptionsController@index']);
        Route::get('common_field_descriptions/{id}', ['as' => 'System.CommonFieldDescriptions.show', 'uses' => '\\Modules\\System\\Controllers\\CommonFieldDescriptionsController@show']);
        Route::post('common_field_descriptions', ['as' => 'System.CommonFieldDescriptions.store', 'uses' => '\\Modules\\System\\Controllers\\CommonFieldDescriptionsController@store']);
        Route::put('common_field_descriptions/{id}', ['as' => 'System.CommonFieldDescriptions.update', 'uses' => '\\Modules\\System\\Controllers\\CommonFieldDescriptionsController@update']);
        Route::delete('common_field_descriptions/{id}', ['as' => 'System.CommonFieldDescriptions.destroy', 'uses' => '\\Modules\\System\\Controllers\\CommonFieldDescriptionsController@destroy']);
        Route::get('strategy_template_category', ['as' => 'System.StrategyTemplateCategory.index', 'uses' => '\\Modules\\System\\Controllers\\StrategyTemplateCategoryController@index']);
        Route::get('strategy_template_category/{id}', ['as' => 'System.StrategyTemplateCategory.show', 'uses' => '\\Modules\\System\\Controllers\\StrategyTemplateCategoryController@show']);
        Route::post('strategy_template_category', ['as' => 'System.StrategyTemplateCategory.store', 'uses' => '\\Modules\\System\\Controllers\\StrategyTemplateCategoryController@store']);
        Route::put('strategy_template_category/{id}', ['as' => 'System.StrategyTemplateCategory.update', 'uses' => '\\Modules\\System\\Controllers\\StrategyTemplateCategoryController@update']);
        Route::delete('strategy_template_category/{id}', ['as' => 'System.StrategyTemplateCategory.destroy', 'uses' => '\\Modules\\System\\Controllers\\StrategyTemplateCategoryController@destroy']);
        Route::get('telemetry_value_mapping', ['as' => 'System.TelemetryValueMapping.index', 'uses' => '\\Modules\\System\\Controllers\\TelemetryValueMappingController@index']);
        Route::get('telemetry_value_mapping/{id}', ['as' => 'System.TelemetryValueMapping.show', 'uses' => '\\Modules\\System\\Controllers\\TelemetryValueMappingController@show']);
        Route::post('telemetry_value_mapping', ['as' => 'System.TelemetryValueMapping.store', 'uses' => '\\Modules\\System\\Controllers\\TelemetryValueMappingController@store']);
        Route::put('telemetry_value_mapping/{id}', ['as' => 'System.TelemetryValueMapping.update', 'uses' => '\\Modules\\System\\Controllers\\TelemetryValueMappingController@update']);
        Route::delete('telemetry_value_mapping/{id}', ['as' => 'System.TelemetryValueMapping.destroy', 'uses' => '\\Modules\\System\\Controllers\\TelemetryValueMappingController@destroy']);
        Route::get('version', ['as' => 'System.Version.index', 'uses' => '\\Modules\\System\\Controllers\\VersionController@index']);
        Route::get('version/{id}', ['as' => 'System.Version.show', 'uses' => '\\Modules\\System\\Controllers\\VersionController@show']);
        Route::post('version', ['as' => 'System.Version.store', 'uses' => '\\Modules\\System\\Controllers\\VersionController@store']);
        Route::put('version/{id}', ['as' => 'System.Version.update', 'uses' => '\\Modules\\System\\Controllers\\VersionController@update']);
        Route::delete('version/{id}', ['as' => 'System.Version.destroy', 'uses' => '\\Modules\\System\\Controllers\\VersionController@destroy']);
        Route::get('pin_codes', ['as' => 'System.PinCodes.index', 'uses' => '\\Modules\\System\\Controllers\\PinCodesController@index']);
        Route::get('pin_codes/{id}', ['as' => 'System.PinCodes.show', 'uses' => '\\Modules\\System\\Controllers\\PinCodesController@show']);
        Route::post('pin_codes', ['as' => 'System.PinCodes.store', 'uses' => '\\Modules\\System\\Controllers\\PinCodesController@store']);
        Route::put('pin_codes/{id}', ['as' => 'System.PinCodes.update', 'uses' => '\\Modules\\System\\Controllers\\PinCodesController@update']);
        Route::delete('pin_codes/{id}', ['as' => 'System.PinCodes.destroy', 'uses' => '\\Modules\\System\\Controllers\\PinCodesController@destroy']);
        Route::get('operator_device_usages', ['as' => 'System.OperatorDeviceUsages.index', 'uses' => '\\Modules\\System\\Controllers\\OperatorDeviceUsagesController@index']);
        Route::get('operator_device_usages/{id}', ['as' => 'System.OperatorDeviceUsages.show', 'uses' => '\\Modules\\System\\Controllers\\OperatorDeviceUsagesController@show']);
        Route::post('operator_device_usages', ['as' => 'System.OperatorDeviceUsages.store', 'uses' => '\\Modules\\System\\Controllers\\OperatorDeviceUsagesController@store']);
        Route::put('operator_device_usages/{id}', ['as' => 'System.OperatorDeviceUsages.update', 'uses' => '\\Modules\\System\\Controllers\\OperatorDeviceUsagesController@update']);
        Route::delete('operator_device_usages/{id}', ['as' => 'System.OperatorDeviceUsages.destroy', 'uses' => '\\Modules\\System\\Controllers\\OperatorDeviceUsagesController@destroy']);
        Route::get('report_plan', ['as' => 'System.ReportPlan.index', 'uses' => '\\Modules\\System\\Controllers\\ReportPlanController@index']);
        Route::get('report_plan/{id}', ['as' => 'System.ReportPlan.show', 'uses' => '\\Modules\\System\\Controllers\\ReportPlanController@show']);
        Route::post('report_plan', ['as' => 'System.ReportPlan.store', 'uses' => '\\Modules\\System\\Controllers\\ReportPlanController@store']);
        Route::post('report_plan/batch_delete', ['as' => 'System.ReportPlan.batchDestroy', 'uses' => '\\Modules\\System\\Controllers\\ReportPlanController@batchDestroy']);
        Route::put('report_plan/{id}', ['as' => 'System.ReportPlan.update', 'uses' => '\\Modules\\System\\Controllers\\ReportPlanController@update']);
        Route::delete('report_plan/{id}', ['as' => 'System.ReportPlan.destroy', 'uses' => '\\Modules\\System\\Controllers\\ReportPlanController@destroy']);
        Route::get('report_plan_models', ['as'   => 'System.ReportPlan.reportPlanModels', 'uses' => '\\Modules\\System\\Controllers\\ReportPlanController@reportPlanModels']);
        Route::post('report_plan/{id}/copy', ['as' => 'System.ReportPlan.copy', 'uses' => '\\Modules\\System\\Controllers\\ReportPlanController@copy']);
        Route::get('building', ['as' => 'System.Building.index', 'uses' => '\\Modules\\System\\Controllers\\BuildingController@index']);
        Route::get('building/{id}', ['as' => 'System.Building.show', 'uses' => '\\Modules\\System\\Controllers\\BuildingController@show']);
        Route::post('building', ['as' => 'System.Building.store', 'uses' => '\\Modules\\System\\Controllers\\BuildingController@store']);
        Route::put('building/{id}', ['as' => 'System.Building.update', 'uses' => '\\Modules\\System\\Controllers\\BuildingController@update']);
        Route::put('building/{id}/devices_position', ['as' => 'System.Building.devices_position', 'uses' => '\\Modules\\System\\Controllers\\BuildingController@devicesPosition']);
        Route::delete('building/{id}', ['as' => 'System.Building.destroy', 'uses' => '\\Modules\\System\\Controllers\\BuildingController@destroy']);
        Route::post('building/batch_delete', ['as' => 'System.Building.destroyList', 'uses' => '\\Modules\\System\\Controllers\\BuildingController@destroyList']);
        Route::get('event', ['as' => 'System.Event.index', 'uses' => '\\Modules\\System\\Controllers\\EventController@index']);
        Route::get('event/{id}', ['as' => 'System.Event.show', 'uses' => '\\Modules\\System\\Controllers\\EventController@show']);
        Route::post('event', ['as' => 'System.Event.store', 'uses' => '\\Modules\\System\\Controllers\\EventController@store']);
        Route::put('event/{id}', ['as' => 'System.Event.update', 'uses' => '\\Modules\\System\\Controllers\\EventController@update']);
        Route::delete('event/{id}', ['as' => 'System.Event.destroy', 'uses' => '\\Modules\\System\\Controllers\\EventController@destroy']);
        Route::get('channel_type', ['as' => 'System.ChannelType.index', 'uses' => '\\Modules\\System\\Controllers\\ChannelTypeController@index']);
        Route::get('channel_type/{id}', ['as' => 'System.ChannelType.show', 'uses' => '\\Modules\\System\\Controllers\\ChannelTypeController@show']);
        Route::post('channel_type', ['as' => 'System.ChannelType.store', 'uses' => '\\Modules\\System\\Controllers\\ChannelTypeController@store']);
        Route::put('channel_type/{id}', ['as' => 'System.ChannelType.update', 'uses' => '\\Modules\\System\\Controllers\\ChannelTypeController@update']);
        Route::delete('channel_type/{id}', ['as' => 'System.ChannelType.destroy', 'uses' => '\\Modules\\System\\Controllers\\ChannelTypeController@destroy']);
        Route::get('device_calibration_record', ['as' => 'System.DeviceCalibrationRecord.index', 'uses' => '\\Modules\\System\\Controllers\\DeviceCalibrationRecordController@index']);
        Route::get('device_calibration_record/{id}', ['as' => 'System.DeviceCalibrationRecord.show', 'uses' => '\\Modules\\System\\Controllers\\DeviceCalibrationRecordController@show']);
        Route::post('device_calibration_record', ['as' => 'System.DeviceCalibrationRecord.store', 'uses' => '\\Modules\\System\\Controllers\\DeviceCalibrationRecordController@store']);
        Route::put('device_calibration_record/{id}', ['as' => 'System.DeviceCalibrationRecord.update', 'uses' => '\\Modules\\System\\Controllers\\DeviceCalibrationRecordController@update']);
        Route::delete('device_calibration_record/{id}', ['as' => 'System.DeviceCalibrationRecord.destroy', 'uses' => '\\Modules\\System\\Controllers\\DeviceCalibrationRecordController@destroy']);
        Route::get('member', ['as' => 'System.Member.index', 'uses' => '\\Modules\\System\\Controllers\\MemberController@index']);
        Route::get('member/{id}', ['as' => 'System.Member.show', 'uses' => '\\Modules\\System\\Controllers\\MemberController@show']);
        Route::post('member', ['as' => 'System.Member.store', 'uses' => '\\Modules\\System\\Controllers\\MemberController@store']);
        Route::put('member/{id}', ['as' => 'System.Member.update', 'uses' => '\\Modules\\System\\Controllers\\MemberController@update']);
        Route::delete('member/{id}', ['as' => 'System.Member.destroy', 'uses' => '\\Modules\\System\\Controllers\\MemberController@destroy']);
        Route::get('member_device_usage', ['as' => 'System.MemberDeviceUsage.index', 'uses' => '\\Modules\\System\\Controllers\\MemberDeviceUsageController@index']);
        Route::get('member_device_usage/{id}', ['as' => 'System.MemberDeviceUsage.show', 'uses' => '\\Modules\\System\\Controllers\\MemberDeviceUsageController@show']);
        Route::post('member_device_usage', ['as' => 'System.MemberDeviceUsage.store', 'uses' => '\\Modules\\System\\Controllers\\MemberDeviceUsageController@store']);
        Route::put('member_device_usage/{id}', ['as' => 'System.MemberDeviceUsage.update', 'uses' => '\\Modules\\System\\Controllers\\MemberDeviceUsageController@update']);
        Route::delete('member_device_usage/{id}', ['as' => 'System.MemberDeviceUsage.destroy', 'uses' => '\\Modules\\System\\Controllers\\MemberDeviceUsageController@destroy']);
        Route::get('team', ['as' => 'System.Team.index', 'uses' => '\\Modules\\System\\Controllers\\TeamController@index']);
        Route::get('team/{id}', ['as' => 'System.Team.show', 'uses' => '\\Modules\\System\\Controllers\\TeamController@show']);
        Route::post('team', ['as' => 'System.Team.store', 'uses' => '\\Modules\\System\\Controllers\\TeamController@store']);
        Route::put('team/{id}', ['as' => 'System.Team.update', 'uses' => '\\Modules\\System\\Controllers\\TeamController@update']);
        Route::delete('team/{id}', ['as' => 'System.Team.destroy', 'uses' => '\\Modules\\System\\Controllers\\TeamController@destroy']);
        Route::post('team/batch_delete', ['as' => 'System.Team.batchDestroy', 'uses' => '\\Modules\\System\\Controllers\\TeamController@batchDestroy']);
        Route::post('member/batch_delete', ['as' => 'System.Member.batchDestroy', 'uses' => '\\Modules\\System\\Controllers\\MemberController@batchDestroy']);
        Route::post('member/batch_assign_team', ['as' => 'System.Member.batchAssignTeam', 'uses' => '\\Modules\\System\\Controllers\\MemberController@batchAssignTeam']);
        
        // Tenant-based Member Data Routes
        Route::prefix('tenant/{tenantId}')->group(function () {
            // Member Data Routes
            Route::prefix('member-data')->group(function () {
                Route::get('telemetry', ['as' => 'System.TenantMemberData.getTelemetry', 'uses' => '\\Modules\\System\\Controllers\\MemberDataController@getMemberTelemetryData']);
                Route::get('all-telemetry', ['as' => 'System.TenantMemberData.getAllTelemetry', 'uses' => '\\Modules\\System\\Controllers\\MemberDataController@getAllMembersTelemetryData']);
                Route::get('active-devices', ['as' => 'System.TenantMemberData.getActiveDevices', 'uses' => '\\Modules\\System\\Controllers\\MemberDataController@getMemberActiveDevices']);
                Route::post('process-stream', ['as' => 'System.TenantMemberData.processStream', 'uses' => '\\Modules\\System\\Controllers\\MemberDataController@processDataStream']);
            });

            // Optimized Member Data Routes
            Route::prefix('optimized-member-data')->group(function () {
                Route::get('telemetry', ['as' => 'System.TenantOptimizedMemberData.getTelemetry', 'uses' => '\\Modules\\System\\Controllers\\OptimizedMemberDataController@getTelemetryData']);
                Route::get('dashboard-summary', ['as' => 'System.TenantOptimizedMemberData.getDashboardSummary', 'uses' => '\\Modules\\System\\Controllers\\OptimizedMemberDataController@getDashboardSummary']);
                Route::get('active-devices', ['as' => 'System.TenantOptimizedMemberData.getActiveDevices', 'uses' => '\\Modules\\System\\Controllers\\OptimizedMemberDataController@getActiveDevices']);
                Route::get('batch-telemetry', ['as' => 'System.TenantOptimizedMemberData.getBatchTelemetry', 'uses' => '\\Modules\\System\\Controllers\\OptimizedMemberDataController@getBatchTelemetryData']);
                Route::get('search', ['as' => 'System.TenantOptimizedMemberData.searchMembers', 'uses' => '\\Modules\\System\\Controllers\\OptimizedMemberDataController@searchMembers']);
                Route::post('clear-cache', ['as' => 'System.TenantOptimizedMemberData.clearCache', 'uses' => '\\Modules\\System\\Controllers\\OptimizedMemberDataController@clearCache']);
            });

            // Member Sync Routes
            Route::prefix('member-sync')->group(function () {
                Route::post('trigger', ['as' => 'System.TenantMemberSync.triggerSync', 'uses' => '\\Modules\\System\\Controllers\\MemberSyncController@triggerSync']);
                Route::get('status', ['as' => 'System.TenantMemberSync.getSyncStatus', 'uses' => '\\Modules\\System\\Controllers\\MemberSyncController@getSyncStatus']);
                Route::post('end-association', ['as' => 'System.TenantMemberSync.endAssociation', 'uses' => '\\Modules\\System\\Controllers\\MemberSyncController@endAssociation']);
            });
        });
    });
});
