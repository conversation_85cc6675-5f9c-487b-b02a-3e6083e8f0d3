<?php


namespace Modules\System\QueryBuilders;


use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Scaffold\QueryBuilder\QueryBuilder;
use Illuminate\Support\Facades\Redis;
use Modules\System\Models\DeviceMold;
use Modules\System\Models\Device;

class DeviceQueryBuilder extends QueryBuilder
{
    /**
     * @var Builder | LengthAwarePaginator
     */
    private $query = null;
    /**
     * @var Builder | LengthAwarePaginator
     */
    public function __construct($modelClassOrQueryBuilder)
    {
        parent::__construct($modelClassOrQueryBuilder);
    }
    /**
     * @param $modelClassOrQueryBuilder Model|Builder|string
     * @return QueryBuilder
     */
    public static function for($modelClassOrQueryBuilder)
    {
        $builder = new self($modelClassOrQueryBuilder);
        return $builder;
    }

    public function getQuery()
    {
        $this->query = parent::getQuery();
        $this->applyCustomFilters();
        return $this->query;
    }

    /**
     * 需要结合 customFilterFilters 自定义过滤
     */
    public function applyCustomFilters()
    {
        $formattedFilters = $this->filtersFormat();
        if (isset($formattedFilters['is_online'])) {
            $isGateway = app('request')->get('is_gateway') === true || app('request')->get('is_gateway') === 'true';
            $isOnline = $formattedFilters['is_online']['value'] === true || $formattedFilters['is_online']['value'] === 'true';
            if ($isGateway) {
                if ($isOnline) {
                    $this->query->whereHas('subOnlineDevices');
                } else {
                    $this->query->whereDoesntHave('subOnlineDevices');
                }
            } else {
                $redis = Redis::Connection('online-devices');
                $keys = $redis->keys('*');
                $keys = array_map(function ($key) {
                    return str_replace(config('database.redis.options.prefix'), '', $key);
                }, $keys);
                if ($isOnline) {
                    $this->query->whereIn('identifier', $keys);
                } else {
                    $this->query->whereNotIn('identifier', $keys);
                }
            }
        }
        if (isset($formattedFilters['inhibit'])) {
            if ($formattedFilters['inhibit']['value'] == 1) {
                $this->alarmQuery("*inhibit");
            } else {
                $this->alarmQuery("*inhibit", false);
            }
        }
        if (isset($formattedFilters['anomaly']) && $formattedFilters['anomaly']['value']) {
            $this->alarmQuery("*" . $formattedFilters['anomaly']['value']);
        }
        if (isset($formattedFilters['channel_type']) && $formattedFilters['channel_type']['value']) {
            $this->query->whereHas('channelType', function ($query) use ($formattedFilters) {
                $query->where('value', 'ilike', $formattedFilters['channel_type']['value']);
            });
        }
        if (isset($formattedFilters['id']) && $formattedFilters['id']['value']) {
            $this->applyFieldFilter('id', $formattedFilters['id']);
        }
        if (isset($formattedFilters['name']) && $formattedFilters['name']['value']) {
            $this->applyFieldFilter('name', $formattedFilters['name']);
        }
        if (isset($formattedFilters['identifier']) && $formattedFilters['identifier']['value']) {
            $this->applyFieldFilter('identifier', $formattedFilters['identifier']);
        }
        if (isset($formattedFilters['tag_no']) && $formattedFilters['tag_no']['value']) {
            $this->applyFieldFilter('tag_no', $formattedFilters['tag_no']);
        }
        if (isset($formattedFilters['device_mold_id']) && $formattedFilters['device_mold_id']['value']) {
            $this->applyFieldFilter('device_mold_id', $formattedFilters['device_mold_id']);
        }
        if (isset($formattedFilters['space_id']) && $formattedFilters['space_id']['value']) {
            $this->applyFieldFilter('space_id', $formattedFilters['space_id']);
        }
        if (isset($formattedFilters['tenant_id']) && $formattedFilters['tenant_id']['value']) {
            $this->applyFieldFilter('tenant_id', $formattedFilters['tenant_id']);
        }
        if (isset($formattedFilters['status']) && $formattedFilters['status']['value']) {
            $this->applyFieldFilter('status', $formattedFilters['status']);
        }
        if (isset($formattedFilters['is_gateway']) && $formattedFilters['is_gateway']['value']) {
            $this->applyFieldFilter('is_gateway', $formattedFilters['is_gateway']);
        }
        if (isset($formattedFilters['gateway_device_id']) && $formattedFilters['gateway_device_id']['value']) {
            $this->applyFieldFilter('gateway_device_id', $formattedFilters['gateway_device_id']);
        }
        if (isset($formattedFilters['deviceMold.model_code']) && $formattedFilters['deviceMold.model_code']['value']) {
            $this->query->whereHas('deviceMold', function ($query) use ($formattedFilters) {
                $filter = $formattedFilters['deviceMold.model_code'];
                $operator = $filter['operator'];
                $value = $filter['value'];
                
                if ($operator === 'in') {
                    // Handle 'in' operator with comma-separated values
                    $values = array_map('trim', explode(',', $value));
                    $query->whereIn('model_code', $values);
                } elseif ($operator === 'nin') {
                    // Handle 'not in' operator
                    $values = array_map('trim', explode(',', $value));
                    $query->whereNotIn('model_code', $values);
                } else {
                    // Handle other operators (eq, like, etc.)
                    $dbOperator = $this->mapOperatorToDatabase($operator);
                    $query->where('model_code', $dbOperator, $value);
                }
            });
        }
        if (isset($formattedFilters['gateway.identifier']) && $formattedFilters['gateway.identifier']['value']) {
            $this->query->whereHas('gateway', function ($gatewayQuery) use ($formattedFilters) {
                $searchTerm = $formattedFilters['gateway.identifier']['value']; // 假设搜索词存储在这个键中
                $gatewayQuery->whereRaw("identifier ILIKE ? ESCAPE '\\'", [$searchTerm]);
            });
        }
        if (isset($formattedFilters['gateway.name']) && $formattedFilters['gateway.name']['value']) {
            $gatewayIds = Device::where('name', 'ilike', $formattedFilters['gateway.name']['value'])->where('is_gateway', true)->pluck('id');
            $this->query->whereIn('devices.gateway_device_id', $gatewayIds);
        }
        if (isset($formattedFilters['gateway.deviceMold.name']) && $formattedFilters['gateway.deviceMold.name']['value']) {
            $gatewayIds = Device::whereHas('deviceMold', function($query) use ($formattedFilters) {
                $query->where('name', 'ilike', $formattedFilters['gateway.deviceMold.name']['value'])->where('is_gateway', true);
            })->pluck('id');
            $this->query->whereIn('devices.gateway_device_id', $gatewayIds);
        }
        if (isset($formattedFilters['gas_channel']) && $formattedFilters['gas_channel']['value']) {
            $this->query->where('gas_channel', 'ilike', $formattedFilters['gas_channel']['value']);
        }
        if (isset($formattedFilters['measurement_range']) && $formattedFilters['measurement_range']['value']) {
            $this->query->where('measurement_range', 'ilike', $formattedFilters['measurement_range']['value']);
        }
    }

    /**
     * @param $key
     */
    private function alarmQuery($key, $isWhereIn = true)
    {
        $alarmRedis = Redis::Connection('alarm-devices');
        $alarmIdentifiers = $alarmRedis->keys($key);
        $alarmIdentifiers = $alarmIdentifiers ? $alarmIdentifiers : [];
        $prefix = config('database.redis.options.prefix');
        if ($isWhereIn) {
            $this->query->whereIn('identifier', $this->getFilterIdentifiers($prefix, $alarmIdentifiers));
        } else {
            $this->query->whereNotIn('identifier', $this->getFilterIdentifiers($prefix, $alarmIdentifiers));
        }
    }

    /*
     * @param $alarmIdentifiers
     * @return array
     */
    private function getFilterIdentifiers($prefix, $alarmIdentifiers)
    {
        $pattern = '/^'. $prefix .'([a-zA-Z0-9:]+)_[^_]+_[^_]+$/';
        $resultArray = [];

        foreach ($alarmIdentifiers as $item) {
            if (preg_match($pattern, $item, $matches)) {
                $resultArray[] = $matches[1];
            }
        }
        return $resultArray;
    }

    /**
     * @return array
     */
    public function customFilterFilters($filters)
    {
        $filters = array_filter($filters, function($filter) {
            return strpos($filter, 'is_online') === false
            && strpos($filter, 'anomaly') === false
            && strpos($filter, 'inhibit') === false
            && strpos($filter, 'channel_type') === false
            && strpos($filter, 'name') === false
            && strpos($filter, 'identifier') === false
            && strpos($filter, 'tag_no') === false
            && strpos($filter, 'deviceMold.model_code') === false
            && strpos($filter, 'gateway.identifier') === false
            && strpos($filter, 'gas_channel') === false
            && strpos($filter, 'measurement_range') === false
            && strpos($filter, 'gateway.name') === false
            && strpos($filter, 'gateway.deviceMold.name') === false;
        });
        return $filters;
    }

    /**
     * input('filter') value is like: filter=is_online_device:eq:true,lastOperatorDeviceUsage.usage_time:gte:2023-08-10%2000:00:00,lastOperatorDeviceUsage.usage_time:lte:2023-08-11%2023:59:59
     * @return array [
     *                  "is_online_device" => [
     *                      "field" => "is_online_device"
     *                      "operator" => "eq"
     *                      "value" => "true"
     *                  ],
     *                  "other field" => ...
     *              ]
     */
    private function filtersFormat()
    {
        $filterParameters = app('request')->input('filter');
        $formattedFilters = [];

        if ($filterParameters) {
            // Support both array format (filter[]=..&filter[]=..) and delimited format
            if (is_array($filterParameters)) {
                // Handle Laravel array parameters: filter[]=field1:op:val1&filter[]=field2:op:val2
                $filters = $filterParameters;
            } else {
                // Support multiple filters separated by semicolons, URL-encoded semicolons, or pipes
                $separators = [';', '%3B', '%3b', '|'];
                $filters = [$filterParameters]; // default: single filter
                
                foreach ($separators as $separator) {
                    if (strpos($filterParameters, $separator) !== false) {
                        $filters = explode($separator, $filterParameters);
                        break;
                    }
                }
            }
            
            foreach ($filters as $filter) {
                $filter = trim($filter);
                if (empty($filter)) continue;
                
                // 只分割前两个冒号
                $parts = explode(':', $filter, 3);
                if (count($parts) === 3) {
                    $formattedFilters[$parts[0]] = [
                        'field' => $parts[0],
                        'operator' => $parts[1],
                        'value' => $parts[2], // 包含剩下的值部分，包括可能的逗号
                    ];
                }
            }
        }
        return $formattedFilters;
    }

    /**
     * Apply filter to a specific field with operator support
     * @param string $fieldName
     * @param array $filter
     */
    private function applyFieldFilter($fieldName, $filter)
    {
        $operator = $filter['operator'];
        $value = $filter['value'];
        
        // Handle boolean fields
        if (in_array($fieldName, ['is_gateway', 'is_public', 'is_online'])) {
            $value = $this->convertToBoolean($value);
        }
        
        if ($operator === 'in') {
            // Handle 'in' operator with comma-separated values
            $values = array_map('trim', explode(',', $value));
            // Convert boolean values if needed
            if (in_array($fieldName, ['is_gateway', 'is_public', 'is_online'])) {
                $values = array_map([$this, 'convertToBoolean'], $values);
            }
            $this->query->whereIn($fieldName, $values);
        } elseif ($operator === 'nin') {
            // Handle 'not in' operator
            $values = array_map('trim', explode(',', $value));
            // Convert boolean values if needed
            if (in_array($fieldName, ['is_gateway', 'is_public', 'is_online'])) {
                $values = array_map([$this, 'convertToBoolean'], $values);
            }
            $this->query->whereNotIn($fieldName, $values);
        } else {
            // Handle other operators (eq, like, etc.)
            $dbOperator = $this->mapOperatorToDatabase($operator);
            $this->query->where($fieldName, $dbOperator, $value);
        }
    }

    /**
     * Convert string boolean values to actual boolean
     * @param mixed $value
     * @return bool
     */
    private function convertToBoolean($value)
    {
        if (is_bool($value)) {
            return $value;
        }
        
        $lowercaseValue = strtolower(trim($value));
        return in_array($lowercaseValue, ['true', '1', 'yes', 'on'], true);
    }

    /**
     * Map filter operators to database operators
     * @param string $operator
     * @return string
     */
    private function mapOperatorToDatabase($operator)
    {
        $map = [
            'eq' => '=',
            'ne' => '!=',
            'gt' => '>',
            'gte' => '>=', 
            'lt' => '<',
            'lte' => '<=',
            'like' => 'ilike',
        ];
        
        return $map[$operator] ?? 'ilike'; // default to ilike for backward compatibility
    }

    /**
     * 自定义排序
     * @param $column
     * @param $direction
     * @param $theQuery
     */
    public function customOrderBy($column, $direction, $theQuery)
    {
        if ($column === 'devices.anomaly') {
            if ($direction === 'asc') {
                $time = now()->subSeconds(40)->setTimezone('UTC');
                $theQuery->orderByRaw("CASE
                WHEN last_save_time IS NULL OR last_save_time < '$time' THEN 6
                WHEN anomaly = 'A1' THEN 1
                WHEN anomaly = 'A2' THEN 2
                WHEN anomaly = 'fault' THEN 3
                WHEN anomaly = 'warning' THEN 4
                ELSE 5
              END");
            } else {
                $theQuery->orderByRaw("CASE
                WHEN anomaly = 'A1' THEN 5
                WHEN anomaly = 'A2' THEN 4
                WHEN anomaly = 'fault' THEN 3
                WHEN anomaly = 'warning' THEN 2
                ELSE 1
              END");
            }
        } else if ($column === 'devices.inhibit') {
            if ($direction === 'asc') {
                $time = now()->subSeconds(40)->setTimezone('UTC');
                $theQuery->orderByRaw("CASE
                WHEN last_save_time IS NULL OR last_save_time < '$time' THEN 4
                WHEN inhibit = 1 THEN 1
                WHEN inhibit = 0 THEN 2
                ELSE 3
              END");
            }
        } else {
            $theQuery->orderBy($column, $direction);
        }
    }

    public function customJoinInOrderBy($relationName, $mainTable, $table, $foreign)
    {
        if ($mainTable == $table && $relationName === 'gateway') {
            $foreign = str_replace($table, $relationName, $foreign);
            $table = $table . ' as ' . $relationName;
        }
        return [$table, $foreign];
    }
}
