<?php

namespace Modules\System\Requests\Device;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Modules\System\Contracts\TenantPolicyManagerInterface;
use Modules\System\Models\Device;
use Modules\System\Models\DeviceAction;
use Modules\System\Models\DeviceMold;
use Modules\System\Models\Tenant;
use Modules\System\Models\WatchPoint;
use Modules\System\Policies\DeviceMoldPolicy;

class DeviceCreateRequest extends FormRequest
{
    use GatewayDeviceIdValidatorTrait;

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $deviceTenantId = intval($this->route('id'));
        $rules = Device::$rules;

        // The WatchPoint should have exactly same tenant_id.
        $rules['watch_point_ids'] = [
        ];

        // The DeviceMold
        if (config('scaffold.device_mold_owner') === DeviceMoldPolicy::OWNER_TOP_TENANT) {
            // Managed by tenant. The DeviceMold should belongs to the top tenant.
            $topTenantId = Tenant::topTenant($deviceTenantId)->pluck('id');
            $rules['device_mold_id'] = [$this->deviceMoldIdExistsMethod($topTenantId)];
        } else {
            // Managed by Platform. The DeviceMold should have tenant_id === 0
            $rules['device_mold_id'] = [$this->deviceMoldIdExistsMethod(0)];
        }
        // Gateway Device ID
        $rules['gateway_device_id'][] = $this->gatewayDeviceIdExistsMethod($deviceTenantId);

        /**
         * The identifier validation
         * If this device does not have `gateway_device_id`, then it's `identifier` must be unique of this system.
         */
        $gatewayDeviceId = $this->get('gateway_device_id');
        if (!$gatewayDeviceId) {
            $rules['identifier'][] = Rule::unique(Device::class, 'identifier')->where(function ($query) use ($deviceTenantId) {
                $query->whereNull('deleted_at');
                // $query->where('tenant_id', $deviceTenantId);
            });
        } else {
            // This device has a `gateway_device_id`, then it's `identifier` should be unique under this gateway device.
            $rules['identifier'][] = Rule::unique(Device::class, 'identifier')
                ->where(function ($query) use ($gatewayDeviceId) {
                    $query->whereNull('deleted_at');
                    $query->where('gateway_device_id', $gatewayDeviceId);
                });
        }

        if ($this->get('is_gateway')) {
            $rules['identifier'][] = 'regex:/^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$/';
        }

        return $rules;
    }

    private function deviceMoldIdExistsMethod($deviceTenantId = 0)
    {
        return Rule::exists(DeviceMold::class, 'id')->where(function ($query) use ($deviceTenantId) {
            $query->where('tenant_id', $deviceTenantId);
        });
    }

    public function attributes()
    {
        $default = __('System/Device.fields');
        if ($this->get('is_gateway')) {
            $default['identifier'] = __('System/Device.fields.mac_address');
        }
        return $default;
    }
}
