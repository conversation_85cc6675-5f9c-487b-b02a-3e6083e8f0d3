<?php

namespace Modules\System\Requests\Team;

use Illuminate\Foundation\Http\FormRequest;
use Modules\System\Models\Team;
class TeamUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $rules = Team::$rules;
        
        // Add validation for member_ids if provided
        $rules['member_ids'] = ['nullable', 'array'];
        $rules['member_ids.*'] = ['integer', 'exists:members,id'];
        
        return $rules;
    }
    public function attributes()
    {
        return __('System/Team.fields');
    }
}