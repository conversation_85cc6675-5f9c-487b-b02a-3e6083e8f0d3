<?php

namespace Modules\System\Repositories;

use Exception;
use Carbon\Carbon;
use Scaffold\BaseRepository;
use Modules\System\Models\Rule;
use Modules\System\Models\Device;
use Modules\System\Models\Tenant;
use Illuminate\Support\Facades\DB;
use Modules\System\Models\Account;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Redis;
use Modules\System\Models\DeviceMold;
use Modules\System\Models\DeviceType;
use Illuminate\Database\Query\Builder;
use Modules\System\Models\TenantAdmin;
use Illuminate\Database\Eloquent\Model;
use Modules\System\Models\RuleCriteria;
use Modules\System\Policies\DevicePolicy;
use Illuminate\Database\Eloquent\Collection;
use Modules\System\Models\DevicePropertyField;
use Modules\System\Models\DeviceTelemetryField;
use Modules\System\Models\TelemetryValueCommon;
use Modules\System\Rpc\DataTypes\ValueTypeEnum;
use Modules\System\Models\DeviceTypeCommonField;
use Illuminate\Contracts\Container\BindingResolutionException;
use Modules\System\Extensions\DeviceMoldStrategies\StrategyManager;
use Modules\System\Extensions\RuleCacheManager\RuleCacheManagerInterface;
use Modules\System\Services\DeviceDataRouterCacheService;
use Modules\System\Extensions\DeviceMoldCacheManager\DeviceMoldCacheManager;
use Modules\System\Extensions\VirtualFieldCalculator\VirtualFieldCalculator;
use Modules\System\Models\DeviceAction;
use Illuminate\Support\Facades\Artisan;

class DeviceRepository extends BaseRepository
{
    protected $fieldSearchable = [
        'identifier',
        'status',
        'name',
    ];

    public function model()
    {
        return Device::class;
    }

    /**
     * @param int $tenantId
     * @param bool $includeSubTenants true: include cascade tenants; false: Default, belongs to $tenantId only
     * @return Collection
     */
    public function getTenantDevices(int $tenantId, $includeSubTenants = false)
    {
        $devices = $this->scopeQuery(function ($query) use ($tenantId, $includeSubTenants) {
            return $query->tenantId($tenantId, $includeSubTenants);
        })->get();
        return $devices;
    }

    /**
     * @param array $attributes [
     *                          -   "identifier": "string",
     *                          -   "position": "string",
     *                          -   "status": 0,
     *                          -   "watch_point_id": 0,
     *                          -   "device_mold_id": 0,
     *                          -   "tenant_id": 0,
     *                          -   "name": "string"
     *                          ]
     * @return Model|null
     * @throws BindingResolutionException
     */
    public function create(array $attributes)
    {
        $device = null;
        DB::transaction(function () use ($attributes, &$device) {
            $tenant = Tenant::with(['rootWatchPoint'])->find($attributes['tenant_id']);
            if (!isset($attributes['watch_point_id']) || $attributes['watch_point_id'] == 0) {
                if (count($tenant->rootWatchPoint->childrenPoints) > 0) {
                    $spaceRootWatchPoint = $tenant->rootWatchPoint->childrenPoints[0];
                    $attributes['watch_point_id'] = $spaceRootWatchPoint->id;
                    $attributes['space_id'] = $spaceRootWatchPoint->space_id;
                }
            }
            /**
             * @var Device|null $device
             */
            $watchPointId = $attributes['watch_point_id'];
            unset($attributes['watch_point_id']);
            // $buildingIds = $attributes['building_ids'];
            unset($attributes['building_ids']);
            $device = parent::create($attributes);
            $device->watchPoints()->attach($watchPointId);
            // $device->controllerBuildings()->attach($buildingIds);

            if ($device->watch_point_id) {
                // update specified criteria for this device
                $this->createSpecifiedRuleCriteriaForDevice($device);
                $ruleCacheManager = app()->make(RuleCacheManagerInterface::class);
                $ruleCacheManager->refreshDeviceRulesCache([$device->id]);
            }

            // Remove gateway cache
            if ($device->gateway) {
                Device::RemoveDeviceCache($device->gateway);
            }
            
            // Cache the new device in device-data-router
            try {
                $cacheService = app(DeviceDataRouterCacheService::class);
                $cacheService->cacheDeviceInfo($device);
            } catch (Exception $e) {
                // Log error but don't fail the creation
                \Log::error('Failed to cache device info in device-data-router', [
                    'device_id' => $device->id,
                    'error' => $e->getMessage()
                ]);
            }
        });
        return $device;
    }

    /**
     * For a given device. Check all potential definition criteria from it's watch point.
     * If a definition criterion has matched device_mold_id,
     * then create a specified rule criterion for this device and the definition criterion.
     *
     * @param Device $device
     */
    private function createSpecifiedRuleCriteriaForDevice(Device $device)
    {
        $potentialDefinitionCriteria = $device->watchPoint->potentialDefinitionCriteria()->where('device_mold_id', $device->device_mold_id)->get();
        $specifiedCriteriaPayload = [];
        foreach ($potentialDefinitionCriteria as $potentialDefinitionCriterion) {
            $specifiedCriteriaPayload[] = [
                'rule_id' => $potentialDefinitionCriterion->rule_id,
                'parent_id' => $potentialDefinitionCriterion->id,
                'watch_point_id' => $device->watch_point_id,
                'device_id' => $device->id,
                'field_id' => $potentialDefinitionCriterion->field_id,
                'field_type' => $potentialDefinitionCriterion->field_type,
                'operator' => $potentialDefinitionCriterion->operator,
                'value' => $potentialDefinitionCriterion->value,
                'device_mold_id' => $potentialDefinitionCriterion->device_mold_id,
            ];
        }
        RuleCriteria::insert($specifiedCriteriaPayload);
    }

    /**
     * @param array $attributes [
     *                          -   "identifier": "string",
     *                          -   "position": "string",
     *                          -   "status": 0,
     *                          -   "watch_point_id": 0,
     *                          -   "device_mold_id": 0,
     *                          -   "tenant_id": 0,
     *                          -   "name": "string"
     *                          ]
     * @param $id
     * @return mixed
     */
    public function update(array $attributes, $deviceId)
    {
        $device = null;
        DB::transaction(function () use ($attributes, $deviceId, &$device) {
            /**
             * @var $oldDevice Device
             * @var $device    Device
             */
            $oldDevice = $this->find($deviceId);
            if (isset($attributes['longitude']) && !$attributes['longitude']) {
                unset($attributes['longitude']);
            }
            if (isset($attributes['latitude']) && !$attributes['latitude']) {
                unset($attributes['latitude']);
            }
            $device = parent::update($attributes, $deviceId);

            if (isset($attributes['watch_point_ids'])) {
                $device->watchPoints()->sync($attributes['watch_point_ids']);
                unset($attributes['watch_point_ids']);
            }

            if (isset($attributes['building_ids'])) {
                $device->controllerBuildings()->sync($attributes['building_ids']);
                unset($attributes['building_ids']);
            }

            if ($device->wasChanged('watch_point_id')) {
                $this->updateDeviceSpecifiedCriteria($device);
                $ruleCacheManager = app()->make(RuleCacheManagerInterface::class);
                $ruleCacheManager->refreshDeviceRulesCache([$device->id]);
            }

            // Remove this device
            Device::RemoveDeviceCache($device);
            // Remove child devices if this device is a gateway
            $device->subDevices->each(function ($subDevice) {
                Device::RemoveDeviceCache($subDevice);
            });
            // Remove gateway device cache if it has
            if ($device->gateway) {
                Device::RemoveDeviceCache($device->gateway);
            }
        });
        return $device;
    }

    /**
     * Update the given device specified criteria records based on it's watch_point_id
     * @param Device $device
     */
    private function updateDeviceSpecifiedCriteria(Device $device)
    {
        // Clear old specified criteria
        $device->ruleSpecifiedCriteria()->delete();

        // Create new specified criteria
        $this->createSpecifiedRuleCriteriaForDevice($device);
    }

    /**
     * Update/Create a device property value record by a given device and a property field.
     * @param Device $device
     * @param DevicePropertyField $propertyField
     * @param $propertyValue
     * @return bool|int
     */
    public function updatePropertyFieldValue(Device $device, DevicePropertyField $propertyField, $propertyValue)
    {
        return $propertyField->setValueToDevice($device->id, $propertyValue);
    }

    /**
     * @param $deviceId
     * @return int|null
     */
    public function delete($deviceId)
    {
        $device = null;
        DB::transaction(function () use ($deviceId, &$device) {
            /**
             * @var Device $device
             */
            $device = $this->find($deviceId);
            if ($device->is_gateway && $device->subDevices()->count() > 0) {
                $device = new \Exception(__('System/Device.errors.sub_devices_exist'));
                return $device;
            }

            $device->delete();

            $strategyManager = StrategyManager::GetManager();
            $strategyManager->inactivateStrategyToDevice($device);

            // Delete device property value items
            $device->devicePropertyValues()->delete();

            /**
             * Collect useful variables for later updating/deleting process.
             * Once criteria were deleted, these variables may not get retrieved easily.
             * @var $affectedCriteria Collection
             */
            $affectedCriteria = $device->ruleCriteria;
            $rules = Rule::query()->whereHas('ruleCriteria', function ($q) use ($device) {
                $q->where('device_id', '=', $device->id);
            })->get();

            // Delete rule criteria
            $device->ruleCriteria()->delete();

            if ($rules->count()) {
                /**
                 * @var RuleCacheManagerInterface $ruleCacheManager
                 * @var RuleRepository $ruleRepository
                 */
                $ruleCacheManager = app()->make(RuleCacheManagerInterface::class);
                $ruleRepository = app()->make(RuleRepository::class);
                $ruleCacheManager->deleteDeviceRulesCache([$device->id]);
                foreach ($rules as $rule) {
                    /**
                     * @var Rule $rule
                     */
                    $rule->criteria_tree = $ruleRepository->removeCriteriaIdFromTree($rule->criteria_tree, $affectedCriteria->pluck('id')->toArray());
                    $rule->save();
                    $ruleCacheManager->refreshDeviceRuleAttributesCache($rule, true);
                }
            }

            // Remove this device
            Device::RemoveDeviceCache($device);
            // Remove child devices if this device is a gateway
            $device->subDevices->each(function ($subDevice) {
                Device::RemoveDeviceCache($subDevice);
            });
            // Remove gateway device cache if it has
            if ($device->gateway) {
                Device::RemoveDeviceCache($device->gateway);
            }
        });
        return $device;
    }

    /**
     * 批量删除 device
     */
    public function batchDelete($deviceIds)
    {
        foreach ($deviceIds as $deviceId) {
            $device = $this->delete($deviceId);
            if ($device instanceof \Exception) {
                return $device;
            }
        }
    }

    /**
     * Get telemetry data with time_bucket
     * @param $device
     * @param $startStr
     * @param $endStr
     * @param $intervalSeconds
     * @param DeviceTelemetryField $telemetryField
     * @return \Illuminate\Database\Eloquent\Builder[]|Collection|Builder[]|\Illuminate\Support\Collection
     */
    public function retrieveByDevice($device, $startStr, $endStr, $intervalSeconds, $telemetryField)
    {
        // Retrieve this field name: like integer_value, float_value.
        $fieldName = $telemetryField->deviceTypeCommonField
            ? $telemetryField->deviceTypeCommonField->value_type : $telemetryField->value_type;
        $fieldType = $fieldName . '_value';
        $fieldCode = $telemetryField->deviceTypeCommonField
            ? $telemetryField->deviceTypeCommonField->code : $telemetryField->code;
        // Query with Timescale DB features.
        $query = TelemetryValueCommon::query()
            ->selectRaw("time_bucket('{$intervalSeconds} seconds', measured_at) AS timestamp");

        if (in_array($fieldName, [ValueTypeEnum::CoordinateName, ValueTypeEnum::BooleanName,
            ValueTypeEnum::StringName])) {
            // Skip this coordinate field.
            // TODO Should have a better solution to display all kind of value types.
            $query = $query->selectRaw("{$fieldType} AS value");
            $query = $query->where('device_identifier', $device->identifier);
            $query->where('field_code', $fieldCode)
                ->whereRaw("measured_at >= TIMESTAMPTZ '{$startStr}' AND measured_at < TIMESTAMPTZ '{$endStr}'")
                ->orderBy('timestamp');
        } else {
            $query = $query->selectRaw("MAX({$fieldType})::numeric AS value");
            $query = $query->where('device_identifier', $device->identifier);
            $query->where('field_code', $fieldCode)
                ->whereRaw("measured_at >= TIMESTAMPTZ '{$startStr}' AND measured_at < TIMESTAMPTZ '{$endStr}'")
                ->groupBy('timestamp')
                ->orderBy('timestamp');
        }
        $result = $query->get()->filter(function ($item){
            return $item->value !== null;
        })->values();
        return $result->map(function ($item) {
            return [
                'timestamp' => strtotime($item->timestamp),
                'value'     => $item->value,
            ];
        })->unique(function ($item) {
            return $item['timestamp'];
        })->values();
    }

    /**
     * @param int $deviceId
     * @param $telemetryFields
     * @param $startDate
     * @param $endDate
     * @param $counts
     * @return array
     */
    public function telemetryValuesByCodes(int $deviceId, $telemetryFields, $startDate, $endDate, $counts) {
        $data = [];
        $device = Device::query()->where('id', $deviceId)->first();
        // Interval seconds.
        $start = Carbon::createFromFormat('Y-m-d H:i:sO', $startDate);
        $end = Carbon::createFromFormat('Y-m-d H:i:sO', $endDate);
        $startStr = $start->format('Y-m-d H:i:sO');
        $endStr = $end->format('Y-m-d H:i:sO');
        $dateRange = $start->diffInDays($end) + 1;
        if ($counts) {
            // $intervalSeconds = ??
        } else {
            $intervalSeconds = $this->defaultIntervalSeconds($dateRange);
        }
        foreach ($telemetryFields as $telemetryField) {
            /**
             * @var DeviceTelemetryField $telemetryField
             */
            $fieldData = $this->retrieveByDevice($device, $startStr, $endStr, $intervalSeconds, $telemetryField);
            $data[$telemetryField->code] = $fieldData;
        }
        return $data;
    }

    /**
     * @param $dateRange
     * @return float|int
     */
    public function defaultIntervalSeconds($dateRange)
    {
        if ($dateRange <= 1) { // 如果时间范围是一天内
            $intervalSeconds = 10; // 10 秒一个 遥测数据
        } elseif ($dateRange > 1 && $dateRange <= 3) { // 一天到3天内
            $intervalSeconds = 60; // 1 分钟一个 遥测数据
        } elseif ($dateRange > 3 && $dateRange <= 7) {
            $intervalSeconds = 60 * 5; // 5 分钟一个
        } elseif ($dateRange > 7 && $dateRange <= 14) {
            $intervalSeconds = 60 * 30; // 30 分钟一个
        } elseif ($dateRange > 14 && $dateRange <= 31) {
            $intervalSeconds = 60 * 60; // 60 分钟一个
        } elseif ($dateRange > 31 && $dateRange <= 60) {
            $intervalSeconds = 60 * 60 * 2; // 60 * 2 分钟一个
        } elseif ($dateRange > 60 && $dateRange <= 365) {
            $intervalSeconds = 60 * 60 * 6; // 60 * 6 分钟一个
        } else {
            $intervalSeconds = 60 * 60 * 24; // 1 日一个
        }
        return $intervalSeconds;
    }

    /**
     * @param $device
     * @param $telemetryFields
     * @param $startDate
     * @param $endDate
     * @return array
     */
    public function telemetryValues($device, $telemetryFields, $startDate, $endDate)
    {
        $start = Carbon::createFromFormat('Y-m-d H:i:sO', $startDate);
        $end = Carbon::createFromFormat('Y-m-d H:i:sO', $endDate);
        $startStr = $start->format('Y-m-d H:i:sO');
        $endStr = $end->format('Y-m-d H:i:sO');
        $intervalSeconds = 3600;
        $query = TelemetryValueCommon::query()
            ->selectRaw("time_bucket('{$intervalSeconds} seconds', measured_at) AS timestamp,
            string_value,integer_value,float_value, bool_value,value_type,field_code,coordinate_value");
        $query = $query->where('device_identifier', $device->identifier);
        $query->whereRaw("measured_at >= TIMESTAMPTZ '{$startStr}' AND measured_at < TIMESTAMPTZ '{$endStr}'")
            ->orderBy('timestamp');
        $fieldCodes = [];
        foreach ($telemetryFields as $telemetryField) {
            $fieldCode = $telemetryField->deviceTypeCommonField
                ? $telemetryField->deviceTypeCommonField->code : $telemetryField->code;
            array_push($fieldCodes, $fieldCode);
        }

        $result = $query->get();
        $data = [];
        $result->each(function ($item) use ($telemetryFields, &$data){
            foreach ($telemetryFields as $telemetryField) {
                $fieldCode = $telemetryField->deviceTypeCommonField
                    ? $telemetryField->deviceTypeCommonField->code : $telemetryField->code;
                $fieldType = $telemetryField->deviceTypeCommonField
                    ? $telemetryField->deviceTypeCommonField->value_type : $telemetryField->value_type;
                $fieldValueName = $fieldType . '_value';
                if ($fieldCode === $item->field_code) {
                    $out = [
                        'timestamp' => strtotime($item->timestamp),
                        'value'     => $item[$fieldValueName],
                    ];
                    $data[$fieldCode][] = $out;
                }
            }
        });
        return $data;
    }

    /**
     * Get sorted virtual fields of deviceId.
     * Return fields array will be sorted by formula variables dependency.
     * The least dependency virtual field will be in the front.
     * @param $deviceId
     * @return mixed
     * @throws Exception
     */
    public static function GetSortedVirtualFieldsOfDevice($deviceId)
    {
        // Get device mold ID. Because a device shall not change it's mold, so we could cache it forever.
        $deviceMoldId = cache()->rememberForever('device-mold-of-device-' . $deviceId, function () use ($deviceId) {
            $device = Device::query()->find($deviceId);
            if ($device) {
                return $device->device_mold_id;
            }
            return null;
        });

        // Get sorted virtual fields of the device mold ID.
        // These fields should be sorted by dependency. The field only composed of real fields should be in the front.
        $cacheKey = DeviceMold::GetVirtualFieldsCacheKey($deviceMoldId);
        $sortedVirtualFields = cache()->rememberForever($cacheKey, function () use ($deviceId) {
            $fields = DeviceTelemetryField::query()->whereHas('deviceMold',
                function ($deviceMoldQuery) use ($deviceId) {
                    $deviceMoldQuery->whereHas('devices',
                        function ($devicesQuery) use ($deviceId) {
                            $devicesQuery->where('id', '=', $deviceId);
                        });
                })
                ->get();
            $virtualFields = $fields->filter(function ($item) {
                return $item->is_virtual;
            });
            $basicFieldCodes = $fields->filter(function ($item) {
                return !$item->is_virtual;
            })->pluck('code')->toArray();
            $virtualFields = VirtualFieldCalculator::SortVirtualFieldsCollection($virtualFields, $basicFieldCodes);
            return new Collection($virtualFields);
        });
        return $sortedVirtualFields;
    }

    /**
     *
     * @param Device $device
     * @param integer[] $tenantAdminIdArray
     * @param bool $canUse
     */
    public function assignTenantAdmin(Device $device, $tenantAdminIdArray, $canUse = true)
    {
        /**
         * Filter tenant admin ID array. Only tenant admins belong to the tenant of the given space $item shall pass.
         */
        $ids = TenantAdmin::query()
            ->where('tenant_id', '=', $device->tenant_id)
            ->whereIn('id', $tenantAdminIdArray)->pluck('id');
        $attachParams = [];
        foreach ($ids as $tenantAdminId) {
            $attachParams[$tenantAdminId] = ['can_use' => $canUse];
        }
        $device->tenantAdmins()->syncWithoutDetaching($attachParams);
    }

    /**
     * @param Device $device
     * @param $tenantAdminId
     */
    public function removeTenantAdmin(Device $device, $tenantAdminId)
    {
        $device->tenantAdmins()->detach($tenantAdminId);
    }

    /**
     * @param Account $account
     * @param string $tenantId
     * @param boolean $includeSubTenants
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function queryByPermission(Account $account, string $tenantId = '', bool $includeSubTenants = false)
    {
        if ($tenantId === '') {
            /**
             * for platform account
             */
            $viewAnyTerminalDevice = DevicePolicy::$paViewAnyTerminalDevice;
            $viewAnyGatewayDevice = DevicePolicy::$paViewAnyGatewayDevice;
            $query = Device::query();
        } else {
            /**
             * for tenant account
             */
            $viewAnyTerminalDevice = DevicePolicy::$taViewAnyTerminalDevice;
            $viewAnyGatewayDevice = DevicePolicy::$taViewAnyGatewayDevice;
            $query = Device::query()->tenantId($tenantId, $includeSubTenants);
        }

        if ($account->hasPermissionTo($viewAnyTerminalDevice) && !$account->hasPermissionTo($viewAnyGatewayDevice)) {
            $query = $query->where('is_gateway', false);
        } else if (!$account->hasPermissionTo($viewAnyTerminalDevice) && $account->hasPermissionTo($viewAnyGatewayDevice)) {
            $query = $query->where('is_gateway', true);
        }
        return $query;
    }

    /**
     * @param array $deviceIds
     *
     * @return array
     * @throws BindingResolutionException
     */
    public function deviceStatistics(array $deviceIds)
    {
        $results = Device::query()
            ->whereIn('id', $deviceIds)
            ->select(DB::raw('is_online, count(id) as count'))
            ->groupBy('is_online')
            ->get();
        $device_offline_count = 0;
        $device_online_count = 0;
        foreach ($results as $result) {
            if ($result->is_online == 0) {
                $device_offline_count = $result->count;
            } else {
                $device_online_count = $result->count;
            }
        }
//        $device_close_count = Device::query()
//            ->whereIn('id', array_diff($deviceIds, $total_offline_device_ids))
//            ->where('status', 2)
//            ->count('id');
        $deviceMoldWithCount = DeviceMold::query()->select(['id', 'name'])->withCount(['devices' => function ($query) use ($deviceIds) {
            $query->whereIn('id', $deviceIds);
        }])->where('is_gateway', false)->get()->toArray();
        $now = \Illuminate\Support\Carbon::now();
        $end_time = $now->toDateTimeString();
        $start_time = $now->subDays(7)->startOfDay()->toDateTimeString();

        return [
            'device_offline_count' => $device_offline_count,
            'device_online_count' => $device_online_count,
            'device_alarm' => [
                "device_alarm_level_1" => [],
                "device_alarm_level_2" => [],
                "device_alarm_level_3" => [],
            ],
            "device_mold_count" => $deviceMoldWithCount
        ];
    }

    /**
     * @param array $tenant_ids
     * @return array
     */
    public function overview(array $tenant_ids)
    {
        $allDeviceCount = 0;
        $offlineDeviceCount = 0;
        $onlineDeviceCount = 0;
        //$deviceTelemetryComparisons = DeviceTelemetryComparison::query()->get();
        $tenantWithDevices = Tenant::query()->withCount(['devices', 'offlineDevices', 'onlineDevices'])->whereIn('id', $tenant_ids)->get()->toArray();
        $deviceMoldIdAndIds = Device::query()->whereIn('tenant_id', $tenant_ids)->pluck("device_mold_id", 'id')->toArray();
        $deviceMoldIds = [];
        $deviceIds = [];
        $deviceAlarmData = [];
        if ($deviceMoldIdAndIds) {
            $deviceMoldIds = array_unique(array_values($deviceMoldIdAndIds));
            $deviceIds = array_keys($deviceMoldIdAndIds);
            //$deviceAlarmData = DeviceAlarm::with('device')->whereIn('device_id', $deviceIds)->limit(20)->orderBy('created_at','DESC')->get()->toArray();
        }
        $deviceMoldWithDevicesCount = DeviceMold::query()->select(['id', 'name'])->withCount(['devices' => function (\Illuminate\Database\Eloquent\Builder $query) use ($deviceIds) {
            if ($deviceIds) {
                $query->whereIn('id', $deviceIds);
            }
        }])->whereIn('id', $deviceMoldIds)->where('is_gateway', false)->get()->toArray();
        if ($tenantWithDevices) {
            $allDeviceCount = array_sum(array_column($tenantWithDevices, 'devices_count'));
            $offlineDeviceCount = array_sum(array_column($tenantWithDevices, 'offline_devices_count'));
            $onlineDeviceCount = array_sum(array_column($tenantWithDevices, 'online_devices_count'));
        }
        return [
            "tenantWithDevices" => $tenantWithDevices,
            "deviceMoldWithDevicesCount" => $deviceMoldWithDevicesCount,
            "allDeviceCount" => $allDeviceCount,
            "offlineDeviceCount" => $offlineDeviceCount,
            "onlineDeviceCount" => $onlineDeviceCount,
            "deviceTelemetryComparisons" => [],
            "deviceAlarmData" => [],
        ];
    }

    /**
     * @param $identifier
     * @return \Illuminate\Database\Eloquent\Builder|Model|object|null
     */
    public function getDeviceWithIdentifier($identifier)
    {
        return Device::query()->where("identifier", $identifier)->first();
    }

    /**
     * 设备是否在线
     * @param $device
     * @return bool
     */
    public function isDeviceAlive($device)
    {
        if ($device->deviceMold && $device->deviceMold->model_code) {
            if ($device->deviceMold->deviceType['code'] === 'air_condition_1') {
                $device = Device::query()->select(['is_online'])
                    ->where(["identifier" => $device->identifier])
                    ->first();
                return $device->is_online;
            }
        }
        return false;
    }

    /**
     * 设备状态是否和指令一致
     * @param $device
     * @param $command
     * @return bool
     */
    public function isDeviceStatusSameAsCommand($device, $command)
    {
        $fields = array_keys($command);
        //      从数据库获取最新值
//        // 这里需要修复，需要改成每个字段获取最新一条，而不是limit(count())。
//        $telemetryValuesCommon = TelemetryValueCommon::query()
//            ->where(["device_identifier" => $device->identifier])
//            ->whereIn('field_code', $fields)
//            ->orderBy('measured_at', 'desc')
//            ->limit(count($fields))
//            ->get();

        $same = [];
        $telemetryValueCommonRepository = app(TelemetryValueRepository::class);
        foreach ($fields as $k => $f) {
            $same[$k] = false;
            // 从缓存中读取最新值
            $key = $telemetryValueCommonRepository::GetCacheKeyByIdentifierAndFieldCode($device->identifier, $f);
            $value = cache()->get($key);
            if ($value !== null) {
                $value = unserialize($value);
            }
            if ($value && $value['value_type'] && $value[$value['value_type'] . '_value'] == $command[$f]) {
                $same[$k] = true;
            }
//            $telemetryValuesCommon->each(function ($telemetryValue) use ($k, $f, $command, &$same) {
//                if ($telemetryValue['field_code'] === $f) {
//                    if ($telemetryValue[$telemetryValue['value_type'] . '_value'] == $command[$f]) {
//                        $same[$k] = true;
//                    }
//                }
//            });
        }
        if (!in_array(false, $same)) {
            return true;
        }
        return false;
    }

    /**
     * 指令是否已经发送
     */
    public function isCommandAlreadySend($device)
    {
        // TODO
        return false;
    }

    /**
     * 设备是否在人为控制中
     */
    public function isDeviceInHumanControl($device)
    {
        // TODO
        return false;
    }

    /**
     * 是否需要发送指令给设备
     * @param $device
     * @param $command
     * @return bool
     */
    public function isNeedSendCommand($device, $command)
    {
        if (!$device && !$device->deviceMold) {
            return false;
        }

        if (!$this->isDeviceAlive($device)) {
            return false;
        }

        if ($this->isDeviceStatusSameAsCommand($device, $command)) {
            return false;
        }

        if ($this->isCommandAlreadySend($device)) {
            return false;
        }

        if ($this->isDeviceInHumanControl($device)) {
            return false;
        }
        return true;
    }

    public function batchUpdateByIdentifier($devices)
    {
        $identifiers = [];
        foreach ($devices as $device) {
            Device::query()->where('identifier', $device['identifier'])->update($device);
            $identifiers[] = $device['identifier'];
        }
        
        // Update device caches in device-data-router
        try {
            $cacheService = app(DeviceDataRouterCacheService::class);
            $cacheService->batchCacheDevicesByIdentifiers($identifiers);
        } catch (Exception $e) {
            // Log error but don't fail the update
            \Log::error('Failed to batch update device cache in device-data-router', [
                'identifiers' => $identifiers,
                'error' => $e->getMessage()
            ]);
        }
    }

    public function batchUpdateDeviceIdentifierCache($identifiers)
    {
        if ($identifiers) {
            $now = time();
            $suffix = config('cache.suffix.inbound');
            foreach ($identifiers as $identifier) {
                Cache::put($identifier . $suffix, $now, 30);
            }
        }
    }

    /**
     * @param array $attributes [
            "device_mold_name" => "",
            "device_mold_code" => "",
            "device_identifier" => "",
            "device_firmware_version" => "",
            "operator" => 16,
            "fields"= > [
                [
                "code":"account_id", //默认就有，可见性为false, 默认不展示
                "unit": "",
                "value_type":"int",
                ],
                [
                "code":"coordinate", //默认就有
                "unit": "",
                "value_type":"string",
                ],
                [
                "code" => "o2",
                "unit" =>  "",
                "value_type" => "string",
                ]
            ]
     * ]
     * 在未知是否有设备类型的情况下检查device是否存在
     * 如果不存在，则创建device, 并将依赖的设备型号，类型，测点，租户关联条件等全部准备好
     * 注：设备类型（device type）在系统初始化就存在，所有设备型号使用默认设备类型
     * @param $tenantId integer
     * @param $isPlc bool
     */
    public function createWithUnknownMold($attributes, $tenantId, $isPlc = false)
    {
        if (!isset($attributes['device_mold_code']) || !isset($attributes['device_mold_name']) || !isset($attributes['device_identifier'])) {
            return null;
        }
        $device = Device::query()->where('identifier', $attributes['device_identifier'])->first();
        $deviceMold = DeviceMold::query()->where('model_code', $attributes['device_mold_code'])->first();
        $deviceType = DeviceType::query()->where('code', DeviceType::$DEFAULT_TYPE_CODE)->first();
        $deviceMoldCacheManager = app()->make(DeviceMoldCacheManager::class);
        $isNeedRefresh = false;
        if (is_null($device)) {
            if (is_null($deviceMold)) {
                $deviceMold = new DeviceMold([
                    'model_code' => $attributes['device_mold_code'],
                    'name' => $attributes['device_mold_name'],
                    'manufacturer' => 'fgds',
                    'device_type_id' => $deviceType->id,
                    'tenant_id' => 0, // 设备类型不属于任何企业（组织/租户）
                    'platform' => 'fgds',
                    'is_gateway' => $isPlc,
                    'need_gateway' => false
                ]);
                $deviceMold->save();
                $isNeedRefresh = true;
            }
            $device = new Device([
                'identifier' => $attributes['device_identifier'],
                'position' => "",
                'status' => 0,
                'watch_point_id' => 0,
                'device_mold_id' => $deviceMold->id,
                'tenant_id' => $tenantId,
                'gateway_device_id' => isset($attributes['gateway_device_id']) ? $attributes['gateway_device_id'] : null,
                'name' => $attributes['device_name'],
                'channel_type' => $attributes['channel_type'],
                'is_gateway' => $isPlc,
                'tag_no' => isset($attributes['tag_no']) ? $attributes['tag_no'] : '',
                'longitude' => 116.545436,
                'latitude' => 40.099459,
            ]);
            $device->save();
        } else {
            if (is_null($deviceMold)) {
                $deviceMold = new DeviceMold([
                    'model_code' => $attributes['device_mold_code'],
                    'name' => $attributes['device_mold_name'],
                    'manufacturer' => 'fgds',
                    'device_type_id' => $deviceType->id,
                    'tenant_id' => 0, // 设备类型不属于任何企业（组织/租户）
                    'platform' => 'fgds',
                    'is_gateway' => $isPlc,
                    'need_gateway' => false
                ]);
                $deviceMold->save();
                $isNeedRefresh = true;
            }
            $device->name = $attributes['device_name'];
            $device->device_mold_id = $deviceMold->id;
            $device->channel_type = $attributes['channel_type'];
            $device->tag_no = isset($attributes['tag_no']) ? $attributes['tag_no'] : '';
            $device->gateway_device_id = isset($attributes['gateway_device_id']) ? $attributes['gateway_device_id'] : null;
            $device->save();
        }

        if (!isset($attributes['fields'])) {
            return $device;
        }
        $gasChannel = '';
        $measurementRange = '';
        foreach ($attributes['fields'] as $field) {
            if ($gasChannel) {
                $gasChannel = $gasChannel . ',' . $field['code'];
            } else {
                $gasChannel = $field['code'];
            }
            // fgds 仪表设备只有一个气体通道, 也就是 $attributes['fields'] 只有一个元素
            $measurementRange = $field['lowLimit'] . '-' . $field['highLimit'] . ' ' . $field['unit'];
            DB::transaction(function () use ($deviceMold, $deviceType, $field, &$isNeedRefresh) {
                $telemetryFieldQuery = DeviceTelemetryField::query()->where('device_mold_id', $deviceMold->id)->lockForUpdate();
                $typeCommonFieldQuery = DeviceTypeCommonField::query()->where('device_type_id', $deviceType->id)->lockForUpdate();
                $telemetryField = $telemetryFieldQuery->where('code', $field['code'])->first();
                if (is_null($telemetryField)) {
                    $typeCommonField = $typeCommonFieldQuery->where('code', $field['code'])->first();
                    if (is_null($typeCommonField)) {
                        $typeCommonField = new DeviceTypeCommonField([
                            'name' => $field['code'],
                            'code' => $field['code'],
                            'device_type_id' => $deviceType->id,
                            'value_type' => $field['value_type'],
                            'unit_symbol' => $field['unit'],
                        ]);
                        $typeCommonField->save();
                    }

                    $deviceTelemetryField = new DeviceTelemetryField([
                        'name' => $field['code'],
                        'code' => $field['code'],
                        'device_mold_id' => $deviceMold->id,
                        'is_visible' => $field['code'] !== 'account_id', // account_id 是辅助字段，不需要展示在web页面中
                        'value_type' => $field['value_type'],
                        'unit_symbol' => $field['unit'],
                        'additional' => '{"icon":"","background":""}',
                        'device_type_common_field_id' => $typeCommonField->id,
                    ]);
                    $isNeedRefresh = true;
                    $deviceTelemetryField->save();
                }
            });
            if ($isNeedRefresh) {
                $deviceMoldCacheManager->refreshDeviceMoldsCache([$deviceMold->model_code]);
            }
        }
        $device->gas_channel = $gasChannel;
        $device->measurement_range = $measurementRange;
        $device->save();
        return $device;
    }

    /**
     * @param array $payload
     * [
     *     "mac" => "", // string, 控制器MAC地址
     *     "type" => "", // string, 控制器设备类型
     *     "version" => "", // string, 软件版本号
     *     "os" => "", // string, 所在操作系统
     *     "devices" => [
     *         [
     *             "id" => 123, // int, 设备（仪表）id，与控制器 mac 地址组合为唯一设备标识符
     *             "tag" => "", // string, 设备（仪表）类型
     *             "fields" => [
     *                 [
     *                     "code" => "ch4", // string, 气体名称
     *                     "unit" => "", // string, 气体单位
     *                     "valueType" => "integer" // string, integer|string|float 该气体发送数据的类型
     *                     "highLimit": "", // string, 气体测量值最高限
     *                     "lowLimit": "" // string, 气体测量值最低限
     *                 ]
     *             ]
     *         ]
     *     ]
     * ]
    */
    public function splitPlcAndDevices($payload)
    {
        // // try create plc if it doesn't exist
        // $companyCode = isset($payload['companyCode']) ? $payload['companyCode'] : '';
        // $tenant = Tenant::query()->where('company_code', $companyCode)->first();
        // if ($tenant == null) {
        //     return;
        // }
        // $plcPayload = [
        //     'device_mold_name' => isset($payload['type']) ? $payload['type'] : '',
        //     'device_mold_code' => isset($payload['type']) ? $payload['type'] : '',
        //     'device_identifier' => isset($payload['mac']) ? $payload['mac'] : '',
        // ];
        // $plc = $this->createWithUnknownMold($plcPayload, $tenant->id, true);
        // if ($plc == null) {
        //     return;
        // }

        // plc （controller）控制器会在web管理后端创建好
        $plc = Device::query()->where('identifier', $payload['mac'])->first();
        if ($plc == null) {
            return;
        }

        // try create devices if them dosen't exist
        $devices = [];
        if (!isset($payload['devices'])) {
            return true;
        }
        $devices = array_map(function($device) use(&$plc){
            $newDevice = [
                'device_mold_name' => $device['tag'],
                'device_mold_code' => $device['tag'],
                'device_identifier' => $plc->identifier . $device['id'],
                'gateway_device_id' => $plc->id,
                'device_name' => isset($device['device_name']) ? $device['device_name'] : $plc->identifier . $device['id'],
                'channel_type' => isset($device['channel_type']) ? $device['channel_type'] : '',
                'tag_no' => isset($device['tag_no']) ? $device['tag_no'] : '',
                'fields' => array_map(function($fields){
                    $field = [
                        'code' => $fields['code'],
                        'unit' => $fields['unit'],
                        'value_type' => $fields['valueType'],
                        'highLimit' => $fields['highLimit'],
                        'lowLimit' => $fields['lowLimit'],
                    ];
                    return $field;
                }, $device['fields'])
            ];
            return $newDevice;
        }, $payload['devices']);
        foreach ($devices as $device) {
            $this->createWithUnknownMold($device, $plc->tenant_id);
        }
        return true;
    }

    /**
     * 获取实时在线的设备(id)与操作员(account_id)
     * @return array
     */
    public function onlineDevicesWithAccountId()
    {
        $redis = Redis::Connection('online-devices');
        $keys = $redis->keys('*');
        $prefix = config('database.redis.options.prefix');;
        $keys = array_map(function($key) use($prefix){
            if (strpos($key, $prefix) === 0) {
                $key = substr($key, strlen($prefix));
            }
            return $key;
        }, $keys);
        if (count($keys) < 1) {
            return [];
        }
        // 批量获取所有 key 对应的 value
        $accountIds = $redis->mget($keys);
        $pairs = [];
        foreach ($keys as $index => $key) {
            $pairs[$keys[$index]] = $accountIds[$index];
        }
        return $pairs;
    }

    /**
     * 获取已经接近下次校准时间5天或已经逾期下次校准时间的设备列表
     */
    public function getCalibrationDevices($tenantId)
    {
        $query = Device::query()
        ->select('name', 'tag_no', 'next_calibration_time')
        ->selectRaw('EXTRACT(DAY FROM AGE(next_calibration_time, CURRENT_DATE)) AS days_difference')
        ->where('tenant_id', $tenantId)
        ->where('next_calibration_time', '<=', now()->addDays(5));
        return $query;
    }

        /**
     * @param $device
     * @param $actionId
     * @param $parameters
     */
    public function action($device, $actionId, $parameters)
    {
        $action = DeviceAction::query()->with('deviceMold')->where('id', $actionId)->first();
        if (!$parameters || count($parameters) < 1) {
            $parameters = $action->parameters;
        }
        if ($parameters) {
            $commands = array_map(function($parameter) {
                $value = $this->convertValue($parameter['default_value'], $parameter['value_type']);
                return ['field' => $parameter['name'], 'value' => $value];
            }, $parameters);
            $sendData = [
                [
                    'identifier' => $device->identifier,
                    'platform' => $device->deviceMold->platform,
                    'device_mold_code' => $device->deviceMold->model_code,
                    'command' => $commands,
                ]
            ];
            $exitCode = Artisan::call('system:amqp-send', [
                'data' => $sendData,
                'type' => 'send_command'
            ]);
            $this->batchUpdateDeviceIdentifierCache(array_column($sendData,'identifier'));
            return $exitCode;
        }
    }

    /**
     * @param $value
     * @param $type
     * @return bool|float|int|string
     */
    function convertValue($value, $type) {
        switch ($type) {
            case 'integer':
                return (int)$value;
            case 'float':
                return (float)$value;
            case 'string':
                return (string)$value;
            case 'bool':
                if ($value === "false") {
                    return false;
                }
                return (bool)$value;
            default:
                return $value;
        }
    }
}
