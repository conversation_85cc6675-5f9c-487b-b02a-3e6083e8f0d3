<?php

namespace Modules\System\Repositories;

use Scaffold\BaseRepository;
use Modules\System\Models\Team;
class TeamRepository extends BaseRepository
{
    protected $fieldSearchable = ['name', 'person_in_charge', 'phone_number', 'email', 'status', 'description', 'tenant_id'];
    public function model()
    {
        return Team::class;
    }

    public function deleteWhereIn($ids)
    {
        $deletedCount = $this->model->whereIn('id', $ids)->delete();
        return $deletedCount;
    }
    
    public function findWhereInIds($ids)
    {
        return $this->model->whereIn('id', $ids)->get();
    }
}