<?php

namespace Modules\System\Repositories;

use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class MemberDataRepository
{
    /**
     * Process member's own telemetry data (status signals, location, etc.)
     */
    private function processMemberTelemetry($memberData, $timestamp)
    {
        $memberId = $memberData['MemberID'];
        
        // Store location data
        if (isset($memberData['Longitude'], $memberData['Latitude'])) {
            $this->storeMemberTelemetry($memberId, 'Longitude', $memberData['Longitude'], 'float', null, $timestamp);
            $this->storeMemberTelemetry($memberId, 'Latitude', $memberData['Latitude'], 'float', null, $timestamp);
        }
        
        // Store status signals
        if (isset($memberData['StatusSignals'])) {
            foreach ($memberData['StatusSignals'] as $signal => $value) {
                $this->storeMemberTelemetry($memberId, $signal, $value, 'integer', null, $timestamp);
            }
        }
        
        // Store other member fields
        $otherFields = ['UserName', 'UserDuty', 'UserTel', 'UserType', 'IsLeader', 'UserNetwork'];
        foreach ($otherFields as $field) {
            if (isset($memberData[$field])) {
                $valueType = is_numeric($memberData[$field]) ? 'integer' : 'string';
                $this->storeMemberTelemetry($memberId, $field, $memberData[$field], $valueType, null, $timestamp);
            }
        }
    }
    
    /**
     * Store member telemetry data
     */
    private function storeMemberTelemetry($memberId, $fieldCode, $value, $valueType, $unit, $timestamp)
    {
        $data = [
            'device_identifier' => $memberId,
            'field_code' => $fieldCode,
            'value_type' => $valueType,
            'unit' => $unit,
            'measured_at' => $timestamp,
            'created_at' => now(),
            'updated_at' => now()
        ];
        
        // Set the appropriate value field based on type
        switch ($valueType) {
            case 'float':
                $data['float_value'] = (float) $value;
                break;
            case 'integer':
                $data['integer_value'] = (int) $value;
                break;
            case 'boolean':
                $data['boolean_value'] = (bool) $value;
                break;
            default:
                $data['string_value'] = (string) $value;
        }
        
        DB::table('telemetry_member')->insert($data);
    }
    
    /**
     * Update member-device association
     */
    private function updateMemberDeviceAssociation($memberId, $deviceIdentifier, $deviceType, $gatewayIdentifier, $timestamp, $metadata = [])
    {
        // Check if there's an active association
        $existingAssociation = DB::table('member_device_associations')
            ->where('member_id', $memberId)
            ->where('device_identifier', $deviceIdentifier)
            ->where('is_active', true)
            ->first();
        
        if (!$existingAssociation) {
            // Create new association
            DB::table('member_device_associations')->insert([
                'member_id' => $memberId,
                'device_identifier' => $deviceIdentifier,
                'device_type' => $deviceType,
                'gateway_device_identifier' => $gatewayIdentifier,
                'association_start' => $timestamp,
                'is_active' => true,
                'device_metadata' => json_encode($metadata),
                'created_at' => now(),
                'updated_at' => now()
            ]);
        } else {
            // Update existing association if gateway changed
            if ($existingAssociation->gateway_device_identifier !== $gatewayIdentifier) {
                DB::table('member_device_associations')
                    ->where('id', $existingAssociation->id)
                    ->update([
                        'gateway_device_identifier' => $gatewayIdentifier,
                        'device_metadata' => json_encode($metadata),
                        'updated_at' => now()
                    ]);
            }
        }
    }
    
    /**
     * Get all telemetry data for a member within time range
     * This is the main query method for member-centric data retrieval
     */
    public function getMemberTelemetryData($memberId, $startTime, $endTime, $tenantId = null, $includeSubTenants = false, $latestOnly = false)
    {
        // Verify member belongs to allowed tenant
        if ($tenantId !== null) {
            $memberExists = DB::table('members')
                ->where('card_id', $memberId)
                ->when($includeSubTenants, function ($query) use ($tenantId) {
                    // For sub-tenants, we'd need a more complex query with tenant hierarchy
                    return $query->whereIn('tenant_id', $this->getTenantAndSubTenantIds($tenantId));
                }, function ($query) use ($tenantId) {
                    return $query->where('tenant_id', $tenantId);
                })
                ->exists();
                
            if (!$memberExists) {
                throw new \Exception("Member not found or access denied for tenant {$tenantId}");
            }
        }
        
        $result = [
            'member_data' => [],
            'device_data' => []
        ];
        
        // 1. Get member's own telemetry data
        $result['member_data'] = $this->getMemberOwnTelemetry($memberId, $startTime, $endTime, $latestOnly);
        
        // 2. Get all devices associated with member during time period (or currently active if latest only)
        $deviceAssociations = $latestOnly 
            ? $this->getMemberActiveDevices($memberId, $tenantId, $includeSubTenants) 
            : $this->getMemberDeviceAssociations($memberId, $startTime, $endTime);
            
        if ($latestOnly) {
            // For latest only mode, convert active devices format to association format
            foreach ($deviceAssociations as $deviceType => $devices) {
                foreach ($devices as $device) {
                    $deviceTelemetry = $this->getDeviceTelemetryByType(
                        $deviceType, 
                        $device->device_identifier, 
                        null, 
                        null, 
                        $latestOnly
                    );
                    
                    if (!isset($result['device_data'][$deviceType])) {
                        $result['device_data'][$deviceType] = [];
                    }
                    
                    $result['device_data'][$deviceType][] = [
                        'device_identifier' => $device->device_identifier,
                        'association_period' => [
                            'start' => $this->convertToChineseTimezone($device->association_start),
                            'end' => $this->convertToChineseTimezone($device->association_end)
                        ],
                        'gateway' => $device->gateway_device_identifier,
                        'metadata' => json_decode($device->device_metadata, true),
                        'telemetry' => $deviceTelemetry
                    ];
                }
            }
        } else {
            // 3. Get telemetry data for each associated device
            foreach ($deviceAssociations as $association) {
                $deviceType = $association->device_type;
                $deviceIdentifier = $association->device_identifier;
                
                // Calculate effective time range for this device
                $effectiveStart = max($startTime, $association->association_start);
                $effectiveEnd = min($endTime, $association->association_end ?? $endTime);
                
                $deviceTelemetry = $this->getDeviceTelemetryByType(
                    $deviceType, 
                    $deviceIdentifier, 
                    $effectiveStart, 
                    $effectiveEnd, 
                    $latestOnly
                );
                
                if (!isset($result['device_data'][$deviceType])) {
                    $result['device_data'][$deviceType] = [];
                }
                
                $result['device_data'][$deviceType][] = [
                    'device_identifier' => $deviceIdentifier,
                    'association_period' => [
                        'start' => $this->convertToChineseTimezone($association->association_start),
                        'end' => $this->convertToChineseTimezone($association->association_end)
                    ],
                    'gateway' => $association->gateway_device_identifier,
                    'metadata' => json_decode($association->device_metadata, true),
                    'telemetry' => $deviceTelemetry
                ];
            }
        }
        
        return $result;
    }
    
    /**
     * Get telemetry data for all members in a tenant within specified time range
     * 
     * @param Carbon $startTime
     * @param Carbon $endTime
     * @param int|null $tenantId
     * @param bool $includeSubTenants
     * @param array|null $memberIds
     * @param bool $latestOnly
     * @return array
     */
    public function getAllMembersTelemetryData($startTime, $endTime, $tenantId = null, $includeSubTenants = false, $memberIds = null, $latestOnly = false)
    {
        // Get all members in the tenant, optionally filtered by specific member_ids
        $membersQuery = DB::table('members');
        
        if ($tenantId !== null) {
            $membersQuery->when($includeSubTenants, function ($query) use ($tenantId) {
                return $query->whereIn('tenant_id', $this->getTenantAndSubTenantIds($tenantId));
            }, function ($query) use ($tenantId) {
                return $query->where('tenant_id', $tenantId);
            });
        }
        
        // Add member_ids filter if specified
        if ($memberIds !== null && !empty($memberIds)) {
            $membersQuery->whereIn('id', $memberIds);
        }
        
        $members = $membersQuery->select('id', 'card_id', 'name', 'tenant_id', 'team_id')->get();
        
        $result = [];
        
        foreach ($members as $member) {
            $memberData = [
                'member_info' => [
                    'id' => $member->id,
                    'member_id' => $member->card_id,
                    'name' => $member->name,
                    'tenant_id' => $member->tenant_id,
                    'team_id' => $member->team_id
                ],
                'member_data' => [],
                'device_data' => []
            ];
            
            // Get member's own telemetry data
            $memberData['member_data'] = $this->getMemberOwnTelemetry($member->card_id, $startTime, $endTime, $latestOnly);
            
            // Get all devices associated with member during time period (or currently active if latest only)
            $deviceAssociations = $latestOnly 
                ? $this->getMemberActiveDevices($member->card_id, $tenantId, $includeSubTenants) 
                : $this->getMemberDeviceAssociations($member->card_id, $startTime, $endTime);
            
            if ($latestOnly) {
                // For latest only mode, convert active devices format to association format
                foreach ($deviceAssociations as $deviceType => $devices) {
                    foreach ($devices as $device) {
                        $deviceTelemetry = $this->getDeviceTelemetryByType(
                            $deviceType, 
                            $device->device_identifier, 
                            null, 
                            null, 
                            $latestOnly
                        );

                        if (!isset($memberData['device_data'][$deviceType])) {
                            $memberData['device_data'][$deviceType] = [];
                        }
                        
                        $memberData['device_data'][$deviceType][] = [
                            'device_identifier' => $device->device_identifier,
                            'association_period' => [
                                'start' => $this->convertToChineseTimezone($device->association_start),
                                'end' => $this->convertToChineseTimezone($device->association_end)
                            ],
                            'gateway' => $device->gateway_device_identifier,
                            'metadata' => json_decode($device->device_metadata, true),
                            'telemetry' => $deviceTelemetry
                        ];
                    }
                }
            } else {
                // Get telemetry data for each associated device
                foreach ($deviceAssociations as $association) {
                    $deviceType = $association->device_type;
                    $deviceIdentifier = $association->device_identifier;
                    
                    // Calculate effective time range for this device
                    $effectiveStart = max($startTime, $association->association_start);
                    $effectiveEnd = min($endTime, $association->association_end ?? $endTime);
                    
                    $deviceTelemetry = $this->getDeviceTelemetryByType(
                        $deviceType, 
                        $deviceIdentifier, 
                        $effectiveStart, 
                        $effectiveEnd, 
                        $latestOnly
                    );
                    
                    if (!isset($memberData['device_data'][$deviceType])) {
                        $memberData['device_data'][$deviceType] = [];
                    }
                    
                    $memberData['device_data'][$deviceType][] = [
                        'device_identifier' => $deviceIdentifier,
                        'association_period' => [
                            'start' => $this->convertToChineseTimezone($association->association_start),
                            'end' => $this->convertToChineseTimezone($association->association_end)
                        ],
                        'gateway' => $association->gateway_device_identifier,
                        'metadata' => json_decode($association->device_metadata, true),
                        'telemetry' => $deviceTelemetry
                    ];
                }
            }
            
            // Only include members that have data during the time period
            if (!empty($memberData['member_data']) || !empty($memberData['device_data'])) {
                $result[] = $memberData;
            }
        }
        
        $response = [
            'members_count' => count($result),
            'total_members_in_tenant' => count($members),
            'members' => $result
        ];
        
        // Only include time_range if not in latest_only mode
        if (!$latestOnly) {
            $response['time_range'] = [
                'start' => $startTime->toISOString(),
                'end' => $endTime->toISOString()
            ];
        }
        
        return $response;
    }
    
    /**
     * Get member's own telemetry data
     */
    private function getMemberOwnTelemetry($memberId, $startTime, $endTime, $latestOnly = false)
    {
        $query = DB::table('telemetry_member')
            ->where('device_identifier', $memberId);
            
        if ($latestOnly) {
            // Get the latest record for each field_code
            return $query->select('*')
                ->whereIn('id', function ($subQuery) use ($memberId) {
                    $subQuery->select(DB::raw('MAX(id)'))
                        ->from('telemetry_member')
                        ->where('device_identifier', $memberId)
                        ->groupBy('field_code');
                })
                ->orderBy('measured_at', 'desc')
                ->get()
                ->groupBy('field_code')
                ->map(function ($records) {
                    return $records->map(function ($record) {
                        return [
                            'value' => $record->{$record->value_type . '_value'},
                            'unit' => $record->unit,
                            'measured_at' => $this->convertToChineseTimezone($record->measured_at),
                            'alarm' => $record->alarm,
                            'fault' => $record->fault
                        ];
                    });
                });
        } else {
            // Get all records within time range
            return $query->whereBetween('measured_at', [$startTime, $endTime])
                ->orderBy('measured_at')
                ->get()
                ->groupBy('field_code')
                ->map(function ($records) {
                    return $records->map(function ($record) {
                        return [
                            'value' => $record->{$record->value_type . '_value'},
                            'unit' => $record->unit,
                            'measured_at' => $this->convertToChineseTimezone($record->measured_at),
                            'alarm' => $record->alarm,
                            'fault' => $record->fault
                        ];
                    });
                });
        }
    }
    
    /**
     * Get member device associations within time range
     */
    private function getMemberDeviceAssociations($memberId, $startTime, $endTime)
    {
        return DB::table('member_device_associations')
            ->where('member_id', $memberId)
            ->where(function ($query) use ($startTime, $endTime) {
                $query->where(function ($q) use ($startTime, $endTime) {
                    // Association starts before our end time
                    $q->where('association_start', '<=', $endTime)
                      // And either never ends or ends after our start time
                      ->where(function ($subQ) use ($startTime) {
                          $subQ->whereNull('association_end')
                               ->orWhere('association_end', '>=', $startTime);
                      });
                });
            })
            ->orderBy('association_start')
            ->get();
    }
    
    /**
     * Get device telemetry by device type
     */
    private function getDeviceTelemetryByType($deviceType, $deviceIdentifier, $startTime, $endTime, $latestOnly = false)
    {
        $tableName = "telemetry_{$deviceType}";
        
        $query = DB::table($tableName)
            ->where('device_identifier', $deviceIdentifier);
            
        if ($latestOnly) {
            // Get the latest record for each field_code
            return $query->select('*')
                ->whereIn('id', function ($subQuery) use ($tableName, $deviceIdentifier) {
                    $subQuery->select(DB::raw('MAX(id)'))
                        ->from($tableName)
                        ->where('device_identifier', $deviceIdentifier)
                        ->groupBy('field_code');
                })
                ->orderBy('measured_at', 'desc')
                ->get()
                ->groupBy('field_code')
                ->map(function ($records) {
                    return $records->map(function ($record) {
                        return [
                            'value' => $record->{$record->value_type . '_value'},
                            'unit' => $record->unit,
                            'measured_at' => $this->convertToChineseTimezone($record->measured_at),
                            'alarm' => $record->alarm,
                            'fault' => $record->fault
                        ];
                    });
                });
        } else {
            // Get all records within time range
            return $query->whereBetween('measured_at', [$startTime, $endTime])
                ->orderBy('measured_at')
                ->get()
                ->groupBy('field_code')
                ->map(function ($records) {
                    return $records->map(function ($record) {
                        return [
                            'value' => $record->{$record->value_type . '_value'},
                            'unit' => $record->unit,
                            'measured_at' => $this->convertToChineseTimezone($record->measured_at),
                            'alarm' => $record->alarm,
                            'fault' => $record->fault
                        ];
                    });
                });
        }
    }
    
    /**
     * End device association (when device is no longer associated with member)
     */
    public function endDeviceAssociation($memberId, $deviceIdentifier, $endTime)
    {
        DB::table('member_device_associations')
            ->where('member_id', $memberId)
            ->where('device_identifier', $deviceIdentifier)
            ->where('is_active', true)
            ->update([
                'association_end' => $endTime,
                'is_active' => false,
                'updated_at' => now()
            ]);
    }
    
    /**
     * Get current active devices for a member
     */
    public function getMemberActiveDevices($memberId, $tenantId = null, $includeSubTenants = false)
    {
        // Verify member belongs to allowed tenant
        if ($tenantId !== null) {
            $memberExists = DB::table('members')
                ->where('card_id', $memberId)
                ->when($includeSubTenants, function ($query) use ($tenantId) {
                    return $query->whereIn('tenant_id', $this->getTenantAndSubTenantIds($tenantId));
                }, function ($query) use ($tenantId) {
                    return $query->where('tenant_id', $tenantId);
                })
                ->exists();
                
            if (!$memberExists) {
                throw new \Exception("Member not found or access denied for tenant {$tenantId}");
            }
        }
        
        return DB::table('member_device_associations')
            ->where('member_id', $memberId)
            ->where('is_active', true)
            ->get()
            ->groupBy('device_type');
    }
    
    /**
     * Process incoming data stream with tenant validation
     */
    public function processDataStream($parsedData, $tenantId = null)
    {
        $timestamp = Carbon::parse($parsedData['TimeSeries']['Timestamp']);
        $gatewayIdentifier = $parsedData['TimeSeries']['Gateway']['DeviceIdentifier'];
        
        // Process member data
        if (isset($parsedData['TimeSeries']['Members'])) {
            foreach ($parsedData['TimeSeries']['Members'] as $member) {
                // Validate member belongs to tenant if specified
                if ($tenantId !== null) {
                    $memberExists = DB::table('members')
                        ->where('card_id', $member['MemberID'])
                        ->where('tenant_id', $tenantId)
                        ->exists();
                        
                    if (!$memberExists) {
                        throw new \Exception("Member {$member['MemberID']} not found in tenant {$tenantId}");
                    }
                }
                
                $this->processMemberTelemetry($member, $timestamp);
            }
        }
        
        // Process peripherals associations
        if (isset($parsedData['Peripherals'])) {
            foreach ($parsedData['Peripherals'] as $peripheral) {
                // Validate member belongs to tenant if specified
                if ($tenantId !== null) {
                    $memberExists = DB::table('members')
                        ->where('card_id', $peripheral['AssociatedMemberID'])
                        ->where('tenant_id', $tenantId)
                        ->exists();
                        
                    if (!$memberExists) {
                        throw new \Exception("Member {$peripheral['AssociatedMemberID']} not found in tenant {$tenantId}");
                    }
                }
                
                $this->updateMemberDeviceAssociation(
                    $peripheral['AssociatedMemberID'],
                    $peripheral['DeviceIdentifier'],
                    $peripheral['DeviceType'],
                    $gatewayIdentifier,
                    $timestamp,
                    $peripheral['Metadata'] ?? []
                );
            }
        }
    }
    
    /**
     * Get tenant and sub-tenant IDs (helper method)
     */
    private function getTenantAndSubTenantIds($tenantId)
    {
        // This is a simplified version - you might need to implement
        // a more sophisticated tenant hierarchy query
        $tenantIds = [$tenantId];
        
        // Get sub-tenants recursively
        $subTenants = DB::table('tenants')
            ->where('parent_tenant_id', $tenantId)
            ->pluck('id')
            ->toArray();
            
        return array_merge($tenantIds, $subTenants);
    }
    
    /**
     * Convert UTC timestamp to China timezone with format "Y-m-d H:i:s"
     * 
     * @param string $utcTimestamp
     * @return string
     */
    private function convertToChineseTimezone($utcTimestamp)
    {
        if (!$utcTimestamp) {
            return null;
        }
        
        // Explicitly parse as UTC then convert to China timezone
        return Carbon::parse($utcTimestamp, 'UTC')
            ->setTimezone('Asia/Shanghai')
            ->format('Y-m-d H:i:s');
    }
}