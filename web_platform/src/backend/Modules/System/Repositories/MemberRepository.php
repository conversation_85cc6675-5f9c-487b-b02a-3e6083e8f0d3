<?php

namespace Modules\System\Repositories;

use Scaffold\BaseRepository;
use Modules\System\Models\Member;
class MemberRepository extends BaseRepository
{
    protected $fieldSearchable = ['card_id', 'name', 'phone', 'email', 'department', 'position', 'status', 'tenant_id', 'team_id'];
    public function model()
    {
        return Member::class;
    }
    public function deleteWhereIn($ids)
    {
        $deletedCount = $this->model->whereIn('id', $ids)->delete();
        return $deletedCount;
    }
    public function findWhereInIds($ids)
    {
        return $this->model->whereIn('id', $ids)->get();
    }
}