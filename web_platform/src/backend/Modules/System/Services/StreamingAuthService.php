<?php

namespace Modules\System\Services;

use Carbon\Carbon;
use Illuminate\Support\Facades\Hash;

/**
 * Streaming Authentication Service for Oryx integration
 * Handles token generation and validation for streaming media access
 */
class StreamingAuthService
{
    /**
     * Generate streaming token for device
     *
     * @param string $streamName Stream identifier (device identifier)
     * @param int $expireMinutes Token expiry time in minutes (default 60 minutes)
     * @return array Contains token and expire timestamp
     */
    public function generateStreamingToken(string $streamName, int $expireMinutes = 60): array
    {
        $expireTimestamp = Carbon::now()->addMinutes($expireMinutes)->timestamp;
        $secret = $this->getStreamingSecret();
        
        // Create HMAC token: HMAC(secret, streamName + expire)
        $payload = $streamName . $expireTimestamp;
        $token = hash_hmac('sha256', $payload, $secret);
        
        return [
            'token' => $token,
            'expire' => $expireTimestamp,
            'expire_at' => Carbon::createFromTimestamp($expireTimestamp)->toISOString()
        ];
    }
    
    /**
     * Generate streaming URL with authentication
     *
     * @param string $deviceIdentifier Device identifier
     * @param int|null $expireMinutes Token expiry time in minutes (null for default)
     * @return string Complete streaming URL with authentication
     */
    public function generateStreamingUrl(string $deviceIdentifier, int $expireMinutes = null): string
    {
        if ($expireMinutes === null) {
            $expireMinutes = config('streaming.token_expiry_minutes', 60);
        }
        
        $baseUrl = $this->getStreamingBaseUrl();
        $tokenData = $this->generateStreamingToken($deviceIdentifier, $expireMinutes);
        
        return $baseUrl . "/live/{$deviceIdentifier}.flv?app=live&stream={$deviceIdentifier}&param=token={$tokenData['token']}&expire={$tokenData['expire']}";
    }
    
    /**
     * Validate streaming token
     *
     * @param string $streamName Stream identifier
     * @param string $token Token to validate
     * @param int $expire Expiry timestamp
     * @return bool True if token is valid
     */
    public function validateStreamingToken(string $streamName, string $token, int $expire): bool
    {
        \Illuminate\Support\Facades\Log::info('Validating streaming token', [
            'stream_name' => $streamName,
            'token' => $token,
            'expire' => $expire,
            'expire_time' => date('Y-m-d H:i:s', $expire),
            'current_time' => time(),
            'current_readable' => date('Y-m-d H:i:s', time()),
            'is_expired' => $expire < time()
        ]);
        
        // Check if token has expired
        if ($expire < time()) {
            \Illuminate\Support\Facades\Log::warning('Token has expired', [
                'expire' => $expire,
                'current_time' => time()
            ]);
            return false;
        }
        
        $secret = $this->getStreamingSecret();
        $payload = $streamName . $expire;
        $expectedToken = hash_hmac('sha256', $payload, $secret);
        
        \Illuminate\Support\Facades\Log::info('Token comparison', [
            'stream_name' => $streamName,
            'expire' => $expire,
            'payload' => $payload,
            'secret' => substr($secret, 0, 5) . '***', // Show only first 5 chars for security
            'expected_token' => $expectedToken,
            'received_token' => $token,
            'tokens_match' => hash_equals($expectedToken, $token)
        ]);
        
        // Use hash_equals to prevent timing attacks
        return hash_equals($expectedToken, $token);
    }
    
    /**
     * Validate streaming authentication from request parameters
     *
     * @param string $app Application name (e.g., 'live')
     * @param string $stream Stream name (device identifier)
     * @param string $param URL parameters string containing token and expire
     * @return bool True if authentication is valid
     */
    public function validateStreamingAuth(string $app, string $stream, string $param): bool
    {
        // Remove any leading ? or & if present
        $cleanParam = ltrim($param, '?&');
        
        // First level parse - this gets us the outer parameters
        parse_str($cleanParam, $outerParams);
        
        \Illuminate\Support\Facades\Log::info('First level parsing', [
            'original_param' => $param,
            'clean_param' => $cleanParam,
            'outer_params' => $outerParams
        ]);
        
        // Initialize token and expire
        $token = null;
        $expire = null;
        
        // Check if token and expire are in the top level
        if (isset($outerParams['token'])) {
            $token = $outerParams['token'];
        }
        if (isset($outerParams['expire'])) {
            $expire = $outerParams['expire'];
        }
        
        // If token is in the 'param' field (SRS nested structure)
        if (!$token && isset($outerParams['param'])) {
            // SRS sends token nested in param field - need to parse again
            $nestedParamString = $outerParams['param'];
            parse_str($nestedParamString, $nestedParams);
            
            \Illuminate\Support\Facades\Log::info('Second level parsing (nested)', [
                'nested_param_string' => $nestedParamString,
                'nested_params' => $nestedParams
            ]);
            
            if (isset($nestedParams['token'])) {
                $token = $nestedParams['token'];
            }
        }
        
        \Illuminate\Support\Facades\Log::info('Final token extraction', [
            'extracted_token' => $token,
            'extracted_expire' => $expire,
            'token_source' => isset($outerParams['token']) ? 'top_level' : (isset($outerParams['param']) ? 'nested_param' : 'not_found'),
            'expire_source' => isset($outerParams['expire']) ? 'top_level' : 'not_found'
        ]);
        
        if (!$token || !$expire) {
            \Illuminate\Support\Facades\Log::warning('Missing token or expire in parameters', [
                'token_present' => !empty($token),
                'expire_present' => !empty($expire),
                'outer_params' => $outerParams
            ]);
            return false;
        }
        
        return $this->validateStreamingToken($stream, $token, (int)$expire);
    }
    
    /**
     * Get streaming secret from configuration
     *
     * @return string Streaming secret key
     */
    protected function getStreamingSecret(): string
    {
        return config('streaming.secret', 'default-streaming-secret-key');
    }
    
    /**
     * Get streaming base URL from configuration
     *
     * @return string Base streaming URL without trailing slash
     */
    protected function getStreamingBaseUrl(): string
    {
        return rtrim(config('streaming.base_url', 'http://k8s.draegersafety.com.cn:31146'), '/');
    }
}
