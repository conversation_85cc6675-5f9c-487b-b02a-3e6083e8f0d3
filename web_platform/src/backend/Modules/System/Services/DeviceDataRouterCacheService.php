<?php

namespace Modules\System\Services;

use Illuminate\Redis\RedisManager;
use Illuminate\Support\Facades\Log;
use Modules\System\Models\Device;
use Exception;

/**
 * Service for managing device cache in device-data-router's Redis
 * This service maintains compatibility with the Go service's cache format
 */
class DeviceDataRouterCacheService
{
    private RedisManager $redis;
    private string $connection;
    
    // Cache key format - must match device-data-router/internal/cache/redis.go
    private const DEVICE_IDENTIFIER_KEY = 'device_identifier:%s';
    private const DEVICE_LATEST_KEY = 'device_latest:%s';
    
    // Default TTL - matches device_config_ttl in device-data-router config
    private const DEFAULT_TTL = 240 * 3600; // 240 hours in seconds

    public function __construct(RedisManager $redis)
    {
        $this->redis = $redis;
        // Use the same Redis connection as device-data-router (database 7)
        $this->connection = 'device-data-router';
    }

    /**
     * Cache device information in the same format as device-data-router
     */
    public function cacheDeviceInfo(Device $device): bool
    {
        try {
            // Load related data
            $device->load(['deviceMold.deviceType']);
            
            // Build cache data structure matching DeviceCache in Go service
            $cacheData = [
                'device_id' => $device->id,
                'identifier' => $device->identifier,
                'name' => $device->name,
                'device_mold_code' => $device->deviceMold->model_code ?? '',
                'device_type_code' => $device->deviceMold->deviceType->code ?? '',
                'is_gateway' => (bool)$device->is_gateway,
                'gateway_device_id' => $device->gateway_device_id,
                'tenant_id' => $device->tenant_id,
                'updated_at' => now()->toISOString(), // ISO 8601 format
            ];

            $cacheKey = sprintf(self::DEVICE_IDENTIFIER_KEY, $device->identifier);
            $cacheJson = json_encode($cacheData);

            // Set cache with TTL
            $result = $this->redis->connection($this->connection)
                ->setex($cacheKey, self::DEFAULT_TTL, $cacheJson);

            Log::info('Device cache updated', [
                'device_identifier' => $device->identifier,
                'cache_key' => $cacheKey,
                'tenant_id' => $device->tenant_id,
            ]);

            // Convert Redis response to boolean
            return $result === 'OK' || $result === true || $result === 1;
        } catch (Exception $e) {
            Log::error('Failed to cache device info', [
                'device_id' => $device->id,
                'device_identifier' => $device->identifier,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Invalidate device cache
     */
    public function invalidateDeviceCache(string $deviceIdentifier): bool
    {
        try {
            $redis = $this->redis->connection($this->connection);
            
            // Remove device info cache
            $deviceCacheKey = sprintf(self::DEVICE_IDENTIFIER_KEY, $deviceIdentifier);
            $deviceDelResult = $redis->del($deviceCacheKey);

            Log::info('Device cache invalidated', [
                'device_identifier' => $deviceIdentifier,
                'device_cache_key' => $deviceCacheKey,
                'keys_deleted' => $deviceDelResult,
            ]);

            // Return true - even if keys didn't exist, invalidation is successful
            return true;
        } catch (Exception $e) {
            Log::error('Failed to invalidate device cache', [
                'device_identifier' => $deviceIdentifier,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Batch cache multiple devices
     */
    public function batchCacheDevices(array $deviceIds): int
    {
        $successCount = 0;
        
        $devices = Device::with(['deviceMold.deviceType'])
            ->whereIn('id', $deviceIds)
            ->get();

        foreach ($devices as $device) {
            if ($this->cacheDeviceInfo($device)) {
                $successCount++;
            }
        }

        Log::info('Batch device cache completed', [
            'total_devices' => count($devices),
            'success_count' => $successCount,
        ]);

        return $successCount;
    }

    /**
     * Batch cache devices by identifiers
     */
    public function batchCacheDevicesByIdentifiers(array $identifiers): int
    {
        $successCount = 0;
        
        $devices = Device::with(['deviceMold.deviceType'])
            ->whereIn('identifier', $identifiers)
            ->get();

        foreach ($devices as $device) {
            if ($this->cacheDeviceInfo($device)) {
                $successCount++;
            }
        }

        Log::info('Batch device cache by identifiers completed', [
            'total_identifiers' => count($identifiers),
            'found_devices' => count($devices),
            'success_count' => $successCount,
        ]);

        return $successCount;
    }

    /**
     * Batch invalidate device caches by identifiers
     */
    public function batchInvalidateDevicesByIdentifiers(array $identifiers): int
    {
        $successCount = 0;

        foreach ($identifiers as $identifier) {
            if ($this->invalidateDeviceCache($identifier)) {
                $successCount++;
            }
        }

        Log::info('Batch device cache invalidation completed', [
            'total_identifiers' => count($identifiers),
            'success_count' => $successCount,
        ]);

        return $successCount;
    }

    /**
     * Get cached device info (for debugging/verification)
     */
    public function getCachedDeviceInfo(string $deviceIdentifier): ?array
    {
        try {
            $cacheKey = sprintf(self::DEVICE_IDENTIFIER_KEY, $deviceIdentifier);
            $cacheData = $this->redis->connection($this->connection)->get($cacheKey);
            
            if ($cacheData) {
                return json_decode($cacheData, true);
            }
            
            return null;
        } catch (Exception $e) {
            Log::error('Failed to get cached device info', [
                'device_identifier' => $deviceIdentifier,
                'error' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Check if device cache exists
     */
    public function deviceCacheExists(string $deviceIdentifier): bool
    {
        try {
            $cacheKey = sprintf(self::DEVICE_IDENTIFIER_KEY, $deviceIdentifier);
            $result = $this->redis->connection($this->connection)->exists($cacheKey);
            // Redis exists() returns the number of keys that exist (integer), convert to boolean
            return $result > 0;
        } catch (Exception $e) {
            Log::error('Failed to check device cache existence', [
                'device_identifier' => $deviceIdentifier,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Get latest device data from device-data-router cache
     * This reads the real-time telemetry cache populated by device-data-router
     */
    public function getDeviceLatestData(string $deviceIdentifier): ?array
    {
        try {
            $cacheKey = sprintf(self::DEVICE_LATEST_KEY, $deviceIdentifier);
            $cacheData = $this->redis->connection($this->connection)->get($cacheKey);
            
            if ($cacheData) {
                $decoded = json_decode($cacheData, true);
                if ($decoded !== null) {
                    return $this->formatDeviceLatestData($decoded);
                }
            }
            
            return null;
        } catch (Exception $e) {
            Log::error('Failed to get device latest data', [
                'device_identifier' => $deviceIdentifier,
                'error' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Batch get latest device data for multiple devices
     */
    public function batchGetDeviceLatestData(array $deviceIdentifiers): array
    {
        $results = [];
        
        try {
            $redis = $this->redis->connection($this->connection);
            $cacheKeys = array_map(fn($identifier) => sprintf(self::DEVICE_LATEST_KEY, $identifier), $deviceIdentifiers);
            
            // Use mget for batch retrieval
            $cacheValues = $redis->mget($cacheKeys);
            
            foreach ($deviceIdentifiers as $index => $identifier) {
                $cacheData = $cacheValues[$index] ?? null;
                if ($cacheData) {
                    $decoded = json_decode($cacheData, true);
                    if ($decoded !== null) {
                        $results[$identifier] = $this->formatDeviceLatestData($decoded);
                    }
                }
            }
        } catch (Exception $e) {
            Log::error('Failed to batch get device latest data', [
                'identifiers_count' => count($deviceIdentifiers),
                'error' => $e->getMessage(),
            ]);
        }
        return $results;
    }

    /**
     * Format device latest data for PHP consumption
     */
    private function formatDeviceLatestData(array $rawData): array
    {
        $formatted = [
            'device_identifier' => $rawData['device_identifier'] ?? null,
            'device_type' => $rawData['device_type'] ?? null,
            'device_name' => $rawData['device_name'] ?? null,
            'is_member' => $rawData['is_member'] ?? false,
            'is_gateway' => $rawData['is_gateway'] ?? false,
            'tenant_id' => $rawData['tenant_id'] ?? null,
            'last_updated' => $rawData['last_updated'] ?? null,
            'online_status' => $rawData['online_status'] ?? 'unknown',
            'fields' => [],
            'gateway_info' => null,
            'member_info' => null,
        ];

        // Format telemetry fields
        if (isset($rawData['fields']) && is_array($rawData['fields'])) {
            foreach ($rawData['fields'] as $fieldName => $fieldData) {
                $formatted['fields'][$fieldName] = [
                    'value' => $fieldData['value'] ?? null,
                    'unit' => $fieldData['unit'] ?? null,
                    'timestamp' => $fieldData['timestamp'] ?? null,
                    'classification' => $fieldData['classification'] ?? 'unknown',
                ];
            }
        }

        // Format gateway info
        if (isset($rawData['gateway_info'])) {
            $formatted['gateway_info'] = [
                'gateway_device_id' => $rawData['gateway_info']['gateway_device_id'] ?? null,
                'gateway_identifier' => $rawData['gateway_info']['gateway_identifier'] ?? null,
                'child_devices' => $rawData['gateway_info']['child_devices'] ?? [],
            ];
        }

        // Format member info
        if (isset($rawData['member_info'])) {
            $formatted['member_info'] = [
                'member_id' => $rawData['member_info']['member_id'] ?? null,
                'member_name' => $rawData['member_info']['member_name'] ?? null,
                'associated_devices' => $rawData['member_info']['associated_devices'] ?? [],
            ];
        }

        return $formatted;
    }

    /**
     * Check if device latest data cache exists
     */
    public function deviceLatestDataExists(string $deviceIdentifier): bool
    {
        try {
            $cacheKey = sprintf(self::DEVICE_LATEST_KEY, $deviceIdentifier);
            $result = $this->redis->connection($this->connection)->exists($cacheKey);
            return $result > 0;
        } catch (Exception $e) {
            Log::error('Failed to check device latest data existence', [
                'device_identifier' => $deviceIdentifier,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }
}
