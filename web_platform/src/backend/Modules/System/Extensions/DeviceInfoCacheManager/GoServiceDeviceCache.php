<?php

namespace Modules\System\Extensions\DeviceInfoCacheManager;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Redis;
use Exception;

/**
 * Reads device information from Go service (device-data-router) Redis cache
 * This avoids duplicating cache logic and ensures data consistency
 */
class GoServiceDeviceCache
{
    /**
     * Cache store name for device-data-router
     */
    private $cacheStore = 'device-data-router';

    private $cachePrefix = 'lcs_';
    /**
     * Cache key format used by Go service
     * Format: device_identifier:{deviceIdentifier}
     * Note: The prefix 'lcs_' is handled by Laravel Redis connection configuration
     */
    private function getDeviceCacheKey(string $deviceIdentifier): string
    {
        return "device_identifier:{$deviceIdentifier}";
    }

    /**
     * Get device information from Go service cache
     *
     * @param string $deviceIdentifier
     * @return array|null Device information array or null if not found
     */
    public function getDeviceInfo(string $deviceIdentifier): ?array
    {
        try {
            $cacheKey = $this->getDeviceCacheKey($deviceIdentifier);
            
            // Use Laravel Redis connection with configured store
            $cached = Redis::connection($this->cacheStore)->get($cacheKey);
            
            if ($cached === null) {
                logger()->debug('Device not found in Go service cache', [
                    'device_identifier' => $deviceIdentifier,
                    'cache_key' => $cacheKey
                ]);
                return null;
            }

            // Parse JSON data from Go service
            $deviceData = json_decode($cached, true);
            
            if ($deviceData === null) {
                logger()->warning('Invalid JSON in Go service device cache', [
                    'device_identifier' => $deviceIdentifier,
                    'cache_key' => $cacheKey,
                    'raw_data' => substr($cached, 0, 200) // Log first 200 chars for debugging
                ]);
                return null;
            }

            // Convert Go service format to our expected format
            return $this->convertGoDeviceFormat($deviceData);
            
        } catch (Exception $e) {
            logger()->error('Failed to get device info from Go service cache', [
                'device_identifier' => $deviceIdentifier,
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            
            return null;
        }
    }

    /**
     * Convert Go service DeviceCache format to PHP expected format
     *
     * @param array $goDeviceData
     * @return array
     */
    private function convertGoDeviceFormat(array $goDeviceData): array
    {
        return [
            'device_id' => $goDeviceData['device_id'] ?? null,
            'identifier' => $goDeviceData['identifier'] ?? null,
            'tenant_id' => $goDeviceData['tenant_id'] ?? null,
            'name' => $goDeviceData['name'] ?? null,
            'device_mold_code' => $goDeviceData['device_mold_code'] ?? null,
            'device_type_code' => $goDeviceData['device_type_code'] ?? null,
            'is_gateway' => $goDeviceData['is_gateway'] ?? false,
            'gateway_device_id' => $goDeviceData['gateway_device_id'] ?? null,
            'updated_at' => $goDeviceData['updated_at'] ?? null,
            'cached_by' => 'go-service',
            'source' => 'device-data-router'
        ];
    }

    /**
     * Get tenant ID for a device by identifier
     *
     * @param string $deviceIdentifier
     * @return int|null
     */
    public function getDeviceTenantId(string $deviceIdentifier): ?int
    {
        $deviceInfo = $this->getDeviceInfo($deviceIdentifier);
        return $deviceInfo ? $deviceInfo['tenant_id'] : null;
    }

    /**
     * Check if Go service cache is available
     *
     * @return bool
     */
    public function isAvailable(): bool
    {
        try {
            // Test cache connection by attempting to get a non-existent key
            Cache::store($this->cacheStore)->get('health_check_' . time());
            return true;
        } catch (Exception $e) {
            logger()->warning('Go service device cache not available', [
                'error' => $e->getMessage(),
                'cache_store' => $this->cacheStore
            ]);
            return false;
        }
    }

    /**
     * Test connection to Redis and validate configuration
     *
     * @return array
     */
    public function testConnection(): array
    {
        try {
            $testKey = 'connection_test_' . time();
            $testValue = ['test' => true, 'timestamp' => time()];
            
            // Try to set and get a test value
            Cache::store($this->cacheStore)->put($testKey, json_encode($testValue), 60); // 1 minute
            $retrieved = Cache::store($this->cacheStore)->get($testKey);
            
            // Clean up test key
            Cache::store($this->cacheStore)->forget($testKey);
            
            $connectionWorking = ($retrieved !== null);
            
            return [
                'connection_working' => $connectionWorking,
                'cache_store' => $this->cacheStore,
                'test_successful' => $connectionWorking,
                'redis_config' => [
                    'host' => config("database.redis.{$this->cacheStore}.host"),
                    'port' => config("database.redis.{$this->cacheStore}.port"),
                    'database' => config("database.redis.{$this->cacheStore}.database"),
                    'prefix_override' => config("database.redis.{$this->cacheStore}.options.prefix", 'not_set'),
                ],
                'env_vars' => [
                    'REDIS_HOST' => env('REDIS_HOST', 'not_set'),
                    'REDIS_PORT' => env('REDIS_PORT', 'not_set'),
                    'REDIS_PASSWORD' => env('REDIS_PASSWORD') ? '***SET***' : 'not_set',
                    'REDIS_DEVICE_DATA_ROUTER_DB' => env('REDIS_DEVICE_DATA_ROUTER_DB', 'not_set_defaults_to_7'),
                ],
                'expected_go_config' => [
                    'host' => 'k8s.draegersafety.com.cn',
                    'port' => 32406,
                    'database' => 7,
                    'password' => '***SHOULD_MATCH***'
                ]
            ];
        } catch (Exception $e) {
            return [
                'connection_working' => false,
                'cache_store' => $this->cacheStore,
                'error' => $e->getMessage(),
                'redis_config' => [
                    'host' => config("database.redis.{$this->cacheStore}.host"),
                    'port' => config("database.redis.{$this->cacheStore}.port"),
                    'database' => config("database.redis.{$this->cacheStore}.database"),
                ]
            ];
        }
    }


    /**
     * Get cache statistics and health status
     *
     * @return array
     */
    public function getStats(): array
    {
        try {
            $isAvailable = $this->isAvailable();
            return [
                'cache_store' => $this->cacheStore,
                'available' => $isAvailable,
                'source' => 'device-data-router (Go service)',
                'description' => 'Reading device cache maintained by Go service'
            ];
        } catch (Exception $e) {
            return [
                'cache_store' => $this->cacheStore,
                'available' => false,
                'error' => $e->getMessage(),
                'source' => 'device-data-router (Go service)'
            ];
        }
    }
}
