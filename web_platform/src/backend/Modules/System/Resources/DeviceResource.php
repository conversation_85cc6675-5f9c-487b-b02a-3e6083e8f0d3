<?php

namespace Modules\System\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Modules\System\Services\StreamingAuthService;

/**
 * Class DeviceResource
 * @package Modules\System\Resources
 *
 * @OA\Schema(
 *     schema="System.DeviceResource",
 *     title="System.DeviceResource",
 *     description="Response resource of model Device.",
 *     required={"id"},
 *     @OA\Property(property="id", type="integer", readOnly="true"),
 *     @OA\Property(property="latest_data", type="object", description="Real-time device data from cache (only when include_latest_data=true)",
 *         @OA\Property(property="device_identifier", type="string", description="Device identifier"),
 *         @OA\Property(property="device_type", type="string", description="Device type code"),
 *         @OA\Property(property="device_name", type="string", description="Device name"),
 *         @OA\Property(property="is_member", type="boolean", description="Whether this is a member device"),
 *         @OA\Property(property="is_gateway", type="boolean", description="Whether this is a gateway device"),
 *         @OA\Property(property="tenant_id", type="integer", description="Tenant ID"),
 *         @OA\Property(property="last_updated", type="string", format="date-time", description="Last update timestamp"),
 *         @OA\Property(property="online_status", type="string", enum={"online", "offline", "unknown"}, description="Device online status"),
 *         @OA\Property(property="fields", type="object", description="Telemetry fields",
 *             @OA\AdditionalProperties(type="object", description="Field data",
 *                 @OA\Property(property="value", description="Field value (can be string, number, boolean)"),
 *                 @OA\Property(property="unit", type="string", nullable=true, description="Field unit"),
 *                 @OA\Property(property="timestamp", type="string", format="date-time", description="Field timestamp"),
 *                 @OA\Property(property="classification", type="string", description="Field classification (e.g., identity, telemetry)")
 *             )
 *         ),
 *         @OA\Property(property="gateway_info", type="object", nullable=true, description="Gateway information (for child devices)",
 *             @OA\Property(property="gateway_device_id", type="integer", nullable=true, description="Gateway device ID"),
 *             @OA\Property(property="gateway_identifier", type="string", nullable=true, description="Gateway device identifier"),
 *             @OA\Property(property="child_devices", type="array", description="Child devices under this gateway",
 *                 @OA\Items(type="string", description="Child device identifier")
 *             )
 *         ),
 *         @OA\Property(property="member_info", type="object", nullable=true, description="Member information (for member devices)",
 *             @OA\Property(property="member_id", type="integer", nullable=true, description="Member ID"),
 *             @OA\Property(property="member_name", type="string", nullable=true, description="Member name"),
 *             @OA\Property(property="associated_devices", type="array", description="Devices associated with this member",
 *                 @OA\Items(type="string", description="Associated device identifier")
 *             )
 *         )
 *     ),
 *     @OA\Property(property="streaming_url", type="string", nullable=true, description="Streaming URL for devices with model_code='tic' (only when device supports streaming)",
 *         example="http://k8s.draegersafety.com.cn:31146/live/device123.flv?token=abcd1234&expire=1735968000"
 *     ),
 *     @OA\Property(property="associated_members", type="array", description="Currently associated members and their teams (only when include_associated_members=true)",
 *         @OA\Items(type="object", description="Associated member information",
 *             @OA\Property(property="member_id", type="string", description="Member ID from data stream"),
 *             @OA\Property(property="member_db_id", type="integer", description="Member database ID"),
 *             @OA\Property(property="name", type="string", description="Member name"),
 *             @OA\Property(property="phone", type="string", nullable=true, description="Member phone number"),
 *             @OA\Property(property="email", type="string", nullable=true, description="Member email"),
 *             @OA\Property(property="department", type="string", nullable=true, description="Member department"),
 *             @OA\Property(property="position", type="string", nullable=true, description="Member position"),
 *             @OA\Property(property="device_type", type="string", description="Device type associated with member"),
 *             @OA\Property(property="association_start", type="string", format="date-time", description="When association started"),
 *             @OA\Property(property="team", type="object", nullable=true, description="Member's team information",
 *                 @OA\Property(property="id", type="integer", description="Team ID"),
 *                 @OA\Property(property="name", type="string", description="Team name"),
 *                 @OA\Property(property="person_in_charge", type="string", nullable=true, description="Person in charge"),
 *                 @OA\Property(property="phone_number", type="string", nullable=true, description="Team contact phone"),
 *                 @OA\Property(property="email", type="string", nullable=true, description="Team contact email")
 *             )
 *         )
 *     ),
 *     allOf={@OA\Schema(ref="#/components/schemas/System.Device")},
 * )
 */
class DeviceResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray($request)
    {
        $item = parent::toArray($request);
        
        // Add streaming URL for devices with model_code 'tic'
        if ($this->resource && 
            $this->resource->deviceMold && 
            $this->resource->deviceMold->model_code === 'tic' && 
            $this->resource->identifier) {
            
            $streamingAuthService = app(StreamingAuthService::class);
            $item['streaming_url'] = $streamingAuthService->generateStreamingUrl($this->resource->identifier);
        }
        
        return $item;
    }
}
