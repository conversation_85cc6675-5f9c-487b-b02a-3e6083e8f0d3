<?php

namespace Modules\System\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Modules\System\Events\MemberRealtimeDataUpdated;
use Modules\System\Events\TenantRealtimeDataUpdated;

/**
 * Handle realtime member data broadcasting
 */
class MemberRealtimeDataJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Task maximum retry attempts
     *
     * @var int
     */
    public int $tries = 3;

    /**
     * Task timeout
     *
     * @var int
     */
    public int $timeout = 30;

    /**
     * The realtime data payload
     *
     * @var array
     */
    public $data;

    /**
     * Create a new job instance.
     *
     * @param array $data
     */
    public function __construct(array $data)
    {
        $this->data = $data;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            $memberID = $this->data['member_id'];
            $tenantID = $this->data['tenant_id'];
            $messageType = $this->data['type'];
            $gateway = $this->data['gateway'] ?? 'unknown';
            $timestamp = $this->data['timestamp'] ?? now()->toISOString();

            logger()->debug('Processing realtime broadcast', [
                'member_id' => $memberID,
                'tenant_id' => $tenantID,
                'type' => $messageType,
                'gateway' => $gateway,
                'timestamp' => $timestamp
            ]);

            // Validate required data structure - now flattened, no 'data' wrapper
            if (!isset($this->data['member_data']) || !isset($this->data['device_data'])) {
                logger()->warning('Invalid realtime data structure', [
                    'member_id' => $memberID,
                    'data_keys' => array_keys($this->data)
                ]);
                return;
            }

            // Prepare broadcast data with metadata
            $broadcastData = [
                'member_id' => $memberID,
                'tenant_id' => $tenantID,
                'gateway' => $gateway,
                'timestamp' => $timestamp,
                'type' => $messageType,
                'data' => [
                    'member_data' => $this->data['member_data'],
                    'device_data' => $this->data['device_data']
                ],
                'stats' => $this->calculateDataStats($this->data)
            ];

            // Broadcast to member-specific channel (synchronous)
            event(new MemberRealtimeDataUpdated($memberID, $broadcastData));

            // Broadcast to tenant-specific channel (for management interfaces, synchronous)
            event(new TenantRealtimeDataUpdated($tenantID, $broadcastData));

            logger()->info('Realtime data broadcasted successfully', [
                'member_id' => $memberID,
                'tenant_id' => $tenantID,
                'gateway' => $gateway,
                'member_fields_count' => count($this->data['member_data'] ?? []),
                'device_types_count' => count($this->data['device_data'] ?? []),
                'total_devices_count' => $this->countTotalDevices($this->data['device_data'] ?? [])
            ]);

        } catch (\Exception $e) {
            logger()->error('Failed to process realtime broadcast', [
                'member_id' => $this->data['member_id'] ?? 'unknown',
                'tenant_id' => $this->data['tenant_id'] ?? 'unknown',
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            
            // Re-throw to trigger retry mechanism
            throw $e;
        }
    }

    /**
     * Calculate statistics about the data being broadcast
     *
     * @param array $data
     * @return array
     */
    private function calculateDataStats(array $data): array
    {
        $stats = [
            'member_fields_count' => 0,
            'device_types_count' => 0,
            'total_devices_count' => 0,
            'total_telemetry_points' => 0,
            'device_breakdown' => []
        ];

        // Count member data fields
        if (isset($data['member_data']) && is_array($data['member_data'])) {
            $stats['member_fields_count'] = count($data['member_data']);
        }

        // Count device data
        if (isset($data['device_data']) && is_array($data['device_data'])) {
            $stats['device_types_count'] = count($data['device_data']);
            
            foreach ($data['device_data'] as $deviceType => $devices) {
                if (is_array($devices)) {
                    $deviceCount = count($devices);
                    $stats['total_devices_count'] += $deviceCount;
                    $stats['device_breakdown'][$deviceType] = [
                        'count' => $deviceCount,
                        'telemetry_points' => 0
                    ];

                    // Count telemetry points for this device type
                    foreach ($devices as $device) {
                        if (isset($device['telemetry']) && is_array($device['telemetry'])) {
                            foreach ($device['telemetry'] as $field => $points) {
                                if (is_array($points)) {
                                    $pointCount = count($points);
                                    $stats['device_breakdown'][$deviceType]['telemetry_points'] += $pointCount;
                                    $stats['total_telemetry_points'] += $pointCount;
                                }
                            }
                        }
                    }
                }
            }
        }

        return $stats;
    }

    /**
     * Count total number of devices across all device types
     *
     * @param array $deviceData
     * @return int
     */
    private function countTotalDevices(array $deviceData): int
    {
        $total = 0;
        
        foreach ($deviceData as $devices) {
            if (is_array($devices)) {
                $total += count($devices);
            }
        }
        
        return $total;
    }

    /**
     * Handle a job failure.
     *
     * @param \Throwable $exception
     * @return void
     */
    public function failed(\Throwable $exception)
    {
        logger()->error('MemberRealtimeDataJob failed permanently', [
            'member_id' => $this->data['member_id'] ?? 'unknown',
            'tenant_id' => $this->data['tenant_id'] ?? 'unknown',
            'exception' => $exception->getMessage(),
            'file' => $exception->getFile(),
            'line' => $exception->getLine(),
            'attempts' => $this->attempts()
        ]);
    }
}