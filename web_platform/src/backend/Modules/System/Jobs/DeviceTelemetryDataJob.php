<?php

namespace Modules\System\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Modules\System\Events\DeviceTelemetryDataUpdated;
use Modules\System\Events\TenantDeviceTelemetryDataUpdated;
use Modules\System\Extensions\DeviceInfoCacheManager\GoServiceDeviceCache;

/**
 * Handle device telemetry data processing
 */
class DeviceTelemetryDataJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Task maximum retry attempts
     *
     * @var int
     */
    public int $tries = 3;

    /**
     * Task timeout
     *
     * @var int
     */
    public int $timeout = 30;

    /**
     * The device telemetry data payload
     *
     * @var array
     */
    public $data;

    /**
     * Create a new job instance.
     *
     * @param array $data
     */
    public function __construct(array $data)
    {
        $this->data = $data;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            $deviceIdentifier = $this->data['device_identifier'];
            $deviceId = $this->data['metadata']['device_id'] ?? $deviceIdentifier;
            $deviceMoldCode = $this->data['metadata']['device_mold_code'] ?? 'unknown';
            $memberName = $this->data['metadata']['member_name'] ?? null;
            $source = $this->data['metadata']['source'] ?? 'device_telemetry';
            $gateway = $this->data['gateway'] ?? 'unknown';

            // Process device telemetry data

            // Validate required telemetry data
            if (!isset($this->data['telemetry']) || !is_array($this->data['telemetry'])) {
                logger()->warning('Invalid telemetry data structure', [
                    'device_identifier' => $deviceIdentifier,
                    'data_keys' => array_keys($this->data)
                ]);
                return;
            }

            // Get device info (including tenant_id) from Go service cache
            $goDeviceCache = new GoServiceDeviceCache();
            $deviceInfo = $goDeviceCache->getDeviceInfo($deviceIdentifier);
            
            if (!$deviceInfo) {
                logger()->warning('Device not found in Go service cache for telemetry data', [
                    'device_identifier' => $deviceIdentifier,
                    'source' => 'device-data-router'
                ]);
                // Still process the data but without tenant broadcasting
            }

            $tenantId = $deviceInfo['tenant_id'] ?? null;
            $deviceName = $deviceInfo['name'] ?? null;

            // Prepare broadcast data
            $broadcastData = [
                'device_identifier' => $deviceIdentifier,
                'device_id' => $deviceId,
                'device_name' => $deviceName,
                'device_mold_code' => $deviceMoldCode,
                'member_name' => $memberName,
                'source' => $source,
                'gateway' => $gateway,
                'association_period' => $this->data['association_period'] ?? null,
                'metadata' => $this->data['metadata'],
                'telemetry' => $this->data['telemetry'],
                'stats' => $this->calculateTelemetryStats($this->data['telemetry']),
                'timestamp' => now()->toISOString(),
                'tenant_id' => $tenantId
            ];

            // Broadcast device telemetry data to device-specific channel （暂时不用）
            // event(new DeviceTelemetryDataUpdated($deviceIdentifier, $broadcastData));

            // Broadcast to tenant-specific channel if tenant_id is available
            if ($tenantId) {
                event(new TenantDeviceTelemetryDataUpdated($tenantId, $broadcastData));
                
                logger()->info('Device telemetry data broadcasted to tenant and device channels', [
                    'device_identifier' => $deviceIdentifier,
                    'device_id' => $deviceId,
                    'device_name' => $deviceName,
                    'tenant_id' => $tenantId,
                    'device_mold_code' => $deviceMoldCode,
                    'telemetry_types_count' => count($this->data['telemetry']),
                    'total_measurements_count' => $this->countTotalMeasurements($this->data['telemetry']),
                    'cache_source' => 'device-data-router'
                ]);
            } else {
                logger()->info('Device telemetry data broadcasted to device channel only', [
                    'device_identifier' => $deviceIdentifier,
                    'device_id' => $deviceId,
                    'device_mold_code' => $deviceMoldCode,
                    'telemetry_types_count' => count($this->data['telemetry']),
                    'total_measurements_count' => $this->countTotalMeasurements($this->data['telemetry']),
                    'reason' => 'tenant_id not available',
                    'cache_source' => 'device-data-router'
                ]);
            }

        } catch (\Exception $e) {
            logger()->error('Failed to process device telemetry data', [
                'device_identifier' => $this->data['device_identifier'] ?? 'unknown',
                'device_id' => $this->data['metadata']['device_id'] ?? 'unknown',
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            
            // Re-throw to trigger retry mechanism
            throw $e;
        }
    }

    /**
     * Calculate statistics about the telemetry data
     *
     * @param array $telemetry
     * @return array
     */
    private function calculateTelemetryStats(array $telemetry): array
    {
        $stats = [
            'telemetry_types_count' => count($telemetry),
            'total_measurements_count' => 0,
            'measurement_types' => [],
            'latest_measurement_time' => null,
            'battery_level' => null
        ];

        foreach ($telemetry as $type => $measurements) {
            if (is_array($measurements)) {
                $stats['total_measurements_count'] += count($measurements);
                $stats['measurement_types'][] = $type;
                
                // Find the latest measurement time
                foreach ($measurements as $measurement) {
                    if (isset($measurement['measured_at'])) {
                        $measuredAt = $measurement['measured_at'];
                        if ($stats['latest_measurement_time'] === null || $measuredAt > $stats['latest_measurement_time']) {
                            $stats['latest_measurement_time'] = $measuredAt;
                        }
                    }
                }
                
                // Extract battery level if available
                if ($type === 'Battery' && !empty($measurements)) {
                    $stats['battery_level'] = $measurements[0]['value'] ?? null;
                }
            }
        }

        return $stats;
    }

    /**
     * Count total measurements across all telemetry types
     *
     * @param array $telemetry
     * @return int
     */
    private function countTotalMeasurements(array $telemetry): int
    {
        $count = 0;
        foreach ($telemetry as $measurements) {
            if (is_array($measurements)) {
                $count += count($measurements);
            }
        }
        return $count;
    }
}
