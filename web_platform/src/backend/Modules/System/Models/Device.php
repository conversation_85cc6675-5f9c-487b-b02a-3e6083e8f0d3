<?php

namespace Modules\System\Models;

use DB;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Redis;
use Modules\System\Repositories\TelemetryValueRepository;
use Modules\System\Models\Upload;
use function cache;

/**
 * @OA\Schema(
 *     schema="System.DeviceTenantAdminPivot",
 *     title="System.DeviceTenantAdminPivot",
 *     description="Pivot between device and tenant admin.",
 *     type="object",
 *     @OA\Property(property="can_use", type="boolean", description="This tenant admin can use this device."),
 *     @OA\Property(property="device_id", type="integer", description="key to device"),
 *     @OA\Property(property="tenant_admin_id", type="integer", description="key to tenant admin"),
 * )
 * @OA\Schema(
 *     schema="System.DeviceTenantAdminWithPivot",
 *     title="System.DeviceTenantAdminWithPivot",
 *     description="Tenant admins belong to the device.",
 *     type="object",
 *     allOf={@OA\Schema(ref="#/components/schemas/System.TenantAdminResource")},
 *     @OA\Property(property="pivot", allOf={@OA\Schema(ref="#/components/schemas/System.DeviceTenantAdminPivot")},),
 * )
 *
 * @OA\Schema(
 *     schema="System.Device",
 *     title="System.Device",
 *     description="Device model of System module.",
 *     type="object",
 *     @OA\Property(property="id", ref="#/components/schemas/id"),
 *     @OA\Property(property="name", type="string", description="The name of a device."),
 *     @OA\Property(property="platform", type="string", description="The third party platform name of a device."),
 *     @OA\Property(property="identifier", type="string", description="The identifier of a device."),
 *     @OA\Property(property="position", type="object", description="The position of the device in the watch point. Top-Left is zero point.",
 *          @OA\Property(property="x", type="number",description="[0~100]"),
 *          @OA\Property(property="y", type="number",description="[0~100]"),
 *     ),
 *     @OA\Property(property="status", type="integer", description="The status of the device. 0代表停止 1代表运行  2代表有错误 3代表通讯故障"),
 *     @OA\Property(property="watch_point_ids", type="array", description="The foreign key to a watch point.",
 *          @OA\Items(type="integer", description="Watch point id"),
 *      ),
 *     @OA\Property(property="device_mold_id", type="integer", description="The foreign key to a device mold."),
 *     @OA\Property(property="tenant_id", type="integer", description="The foreign key to a tenant."),
 *
 *     @OA\Property(property="watch_points", type="array", description="Device Watch Points.",
 *          @OA\Items(ref="#/components/schemas/System.WatchPointResource"),
 *     ),
 *     @OA\Property(property="device_mold", type="object",
 *          allOf={@OA\Schema(ref="#/components/schemas/System.DeviceMoldResource")},
 *     ),
 *     @OA\Property(property="tenant", type="object",
 *          allOf={@OA\Schema(ref="#/components/schemas/System.TenantWithRelationships")},
 *     ),
 *     @OA\Property(property="space", type="object",
 *          allOf={@OA\Schema(ref="#/components/schemas/System.SpaceResource")},
 *     ),
 *     @OA\Property(property="building_ids", type="array", description="Building ids attached to a controller.",
 *          @OA\Items(type="integer", description="building Id"),
 *      ),
 *     @OA\Property(property="building", type="object",
 *          allOf={@OA\Schema(ref="#/components/schemas/System.BuildingResource")},
 *     ),
 *     @OA\Property(property="controller_buildings", type="array", description="controller related buildings.",
 *          @OA\Items(ref="#/components/schemas/System.BuildingResource")
 *     ),
 *     @OA\Property(property="device_property_values", type="array", description="Device property values.",
 *          @OA\Items(ref="#/components/schemas/System.DevicePropertyValueResource")
 *     ),
 *     @OA\Property(property="device_property_fields", type="array", description="Device property fields.",
 *          @OA\Items(ref="#/components/schemas/System.DevicePropertyFieldResource")
 *     ),
 *     @OA\Property(property="device_telemetry_fields", type="array", description="Device telemetry fields.",
 *          @OA\Items(ref="#/components/schemas/System.DeviceTelemetryFieldResource")
 *     ),
 *     @OA\Property(property="telemetry_values", type="array", description="Telemetry Values.",
 *          @OA\Items(ref="#/components/schemas/System.TelemetryValue")
 *     ),
 *     @OA\Property(property="device_actions", type="array", description="Device actions.",
 *          @OA\Items(ref="#/components/schemas/System.DeviceActionResource")
 *     ),
 *     @OA\Property(property="rule_criteria", type="array", description="Attached rule criteria.",
 *          @OA\Items(ref="#/components/schemas/System.RuleCriteria")
 *     ),
 *     @OA\Property(property="rule_definition_criteria", type="array", description="Attached rule criteria of definition.",
 *          @OA\Items(ref="#/components/schemas/System.RuleCriteria")
 *     ),
 *     @OA\Property(property="gateway", type="object", description="If this is connected to a gateway, then this property is the gateway.",
 *          allOf={@OA\Schema(ref="#/components/schemas/System.DeviceResource")},
 *     ),
 *     @OA\Property(property="sub_devices", type="array", description="If this is a gateway device, then all connected sub devices to this property.",
 *          @OA\Items(ref="#/components/schemas/System.DeviceResource")
 *     ),
 *     @OA\Property(property="device_latest_calibration_record", type="array", description="Device latest calibration record. only one record.",
 *          @OA\Items(ref="#/components/schemas/System.DeviceCalibrationRecordResource")
 *     ),
 *     @OA\Property(property="device_calibration_record", type="array", description="Device latest calibration record.",
 *          @OA\Items(ref="#/components/schemas/System.DeviceCalibrationRecordResource")
 *     ),
 *     @OA\Property(property="tenant_admins", type="array", description="Tenant admins relates to this device. This device might be private. `is_public == false` ",
 *          @OA\Items(ref="#/components/schemas/System.DeviceTenantAdminWithPivot")
 *     ),
 *     @OA\Property(property="field_latest_values", type="array", description="Telemetry Values common.",
 *          @OA\Items(ref="#/components/schemas/System.TelemetryValueCommon")
 *     ),
 *     @OA\Property(property="last_operator_device_usage", type="object", description="Operator Device Usages.",
 *          allOf={@OA\Schema(ref="#/components/schemas/System.OperatorDeviceUsages")}
 *     ),
 *     @OA\Property(property="last_members", type="string", description="Last members."),
 *     @OA\Property(property="last_save_time", type="string", description="上次连接时间，与最新使用记录的时间一致"),
 *     @OA\Property(property="deleted_at", ref="#/components/schemas/deleted_at"),
 *     @OA\Property(property="created_at", ref="#/components/schemas/created_at"),
 *     @OA\Property(property="updated_at", ref="#/components/schemas/updated_at"),
 *     @OA\Property(property="is_gateway", type="boolean", description="Determine if this device was a gateway."),
 *     @OA\Property(property="gateway_device_id", type="integer", description="If this device connected to a gateway device, then this field keeps the gateway device primary key"),
 *     @OA\Property(property="space_id", type="integer", description="The foreign key to a space."),
 *     @OA\Property(property="is_public", type="boolean", description="是否为公共设备（true默认）；false为专用设备。"),
 *     @OA\Property(property="is_online", type="boolean", description="true在线，false为离线。"),
 *     @OA\Property(property="longitude", type="string", description="经度。"),
 *     @OA\Property(property="latitude", type="string", description="纬度。"),
 *     @OA\Property(property="address", type="string", description="详细地址。"),
 *     @OA\Property(property="channel_type", type="string", description="channel type."),
 *     @OA\Property(property="building_id", type="integer", description="The foreign key to a building."),
 *     @OA\Property(property="floor", type="integer", description="设备所在楼层，为 null 时，表示可能不在建筑内"),
 *     @OA\Property(property="anomaly", type="string", description="异常状态。可能的值：fault/A1/A2/warning/info, 空字符串""表示无异常值（正常）"),
 *     @OA\Property(property="inhibit", type="integer", description="抑制状态。0：正常，1：异常"),
 *     @OA\Property(property="gas_channel", type="string", description="气体通道。"),
 *     @OA\Property(property="measurement_range", type="string", description="测量范围。"),
 *     @OA\Property(property="last_calibration_time", type="string", description="上次校准时间。"),
 *     @OA\Property(property="next_calibration_time", type="string", description="下次校准时间。"),
 *     @OA\Property(property="calibration_cycle", type="integer", description="校正周期。"),
 *     @OA\Property(property="tag_no", type="string", description="设备标签号。"),
 *     @OA\Property(property="channel_id", type="string", description="通道ID。"),
 *     @OA\Property(property="manufacture_date", type="string", description="出厂日期。"),
 *     @OA\Property(property="sim_expired_date", type="string", description="SIM卡过期日期。"),
 *     @OA\Property(property="sn", type="string", description="设备序列号。"),
 *     @OA\Property(property="remark", type="string", description="备注。"),
 * )
 * @property integer $id
 * @property string $identifier
 * @property string $position
 * @property string $longitude
 * @property string $latitude
 * @property string $address
 * @property integer $status
 * @property integer $watch_point_id
 * @property integer $device_mold_id
 * @property integer $tenant_id
 * @property string $name
 * @property string $deleted_at
 * @property string $created_at
 * @property string $updated_at
 * @property boolean $is_gateway
 * @property integer $gateway_device_id
 * @property integer $space_id
 * @property boolean $is_public
 * @property boolean $is_online
 * @property string $platform
 * @property string $last_save_time
 * @property integer $building_id
 * @property string $anomaly
 * @property integer $inhibit
 * @property integer $floor
 * @property string $gas_channel
 * @property string $measurement_range
 * @property string $last_calibration_time
 * @property string $next_calibration_time
 * @property integer $calibration_cycle
 * @property string $channel_type
 * @property string $tag_no
 * @property Space $space
 * @property string $last_members
 * @property string $sn
 * @property string $remark
 */
class Device extends Model
{
    use SoftDeletes;
    public $table = 'devices';
    protected $appends = ['channel_id'];
    public $fillable = [
        'identifier',
        'position',
        'status',
        'device_mold_id',
        'tenant_id',
        'name',
        'is_gateway',
        'gateway_device_id',
        'space_id',
        'is_public',
        'is_online',
        'longitude',
        'latitude',
        'address',
        'platform',
        'last_save_time',
        'building_id',
        'anomaly',
        'inhibit',
        'floor',
        'gas_channel',
        'measurement_range',
        'last_calibration_time',
        'next_calibration_time',
        'channel_type',
        'calibration_cycle',
        'tag_no',
        'manufacture_date',
        'sim_expired_date',
        'last_members',
        'sn',
        'remark'
    ];
    public $searchable = [
        'id',
        'identifier',
        'name',
        'status',
        'device_mold_id',
        'is_gateway',
        'gateway_device_id',
        'space_id',
        'is_public',
        'is_online',
        'created_at',
        'updated_at',
        'watchPoint',
        'deviceMold',
        'tenant',
        'space',
        'devicePropertyValues',
        'devicePropertyFields',
        'lastOperatorDeviceUsage',
        'telemetryValues',
        'gateway',
        'subDevices',
        'longitude',
        'latitude',
        'address',
        'subOnlineDevices',
        'platform',
        'last_save_time',
        'building_id',
        'anomaly',
        'inhibit',
        'floor',
        'gas_channel',
        'measurement_range',
        'last_calibration_time',
        'next_calibration_time',
        'channel_type',
        'calibration_cycle',
        'tag_no',
        'manufacture_date',
        'sim_expired_date',
        'last_members',
        'sn',
        'remark'
    ];
    protected $casts = [
        'id'                => 'integer',
        'identifier'        => 'string',
        'position'          => 'array',
        'status'            => 'integer',
        'device_mold_id'    => 'integer',
        'tenant_id'         => 'integer',
        'name'              => 'string',
        'is_gateway'        => 'boolean',
        'gateway_device_id' => 'integer',
        'space_id'          => 'integer',
        'is_public'         => 'boolean',
        'is_online'         => 'boolean',
        'longitude'         => 'decimal:6',
        'latitude'          => 'decimal:6',
        'platform'          => 'string',
        'building_id'       => 'integer',
        'anomaly'           => 'string',
        'inhibit'           => 'integer',
        'floor'             => 'integer',
        'gas_channel'       => 'string',
        'measurement_range' => 'string',
        'last_calibration_time' => 'datetime',
        'next_calibration_time' => 'datetime',
        'channel_type'       => 'string',
        'calibration_cycle'  => 'integer',
        'tag_no'             => 'string',
        'manufacture_date'   => 'date',
        'sim_expired_date'   => 'date',
        'last_members'       => 'string',
        'sn'                 => 'string',
        'remark'             => 'string'
    ];
    protected $dates = ['deleted_at', 'last_calibration_time', 'next_calibration_time'];
    public static $rules = [
        'identifier'        => [],
        'position'          => [],
        'status'            => [],
        'device_mold_id'    => [],
        'tenant_id'         => [],
        'name'              => [],
        'is_gateway'        => ['boolean'],
        'gateway_device_id' => ['nullable'],
        'space_id'          => [],
        'is_public'         => ['boolean'],
        'is_online'         => ['boolean'],
        'longitude'         => ['numeric'],
        'latitude'          => ['numeric'],
        'address'           => [],
        'platform'          => [],
        'building_id'       => [],
        'anomaly'           => [],
        'inhibit'           => [],
        'floor'             => [],
        'gas_channel'       => [],
        'measurement_range' => [],
        'last_calibration_time' => [],
        'next_calibration_time' => [],
        'channel_type'       => [],
        'calibration_cycle'  => [],
        'tag_no'             => [],
        'manufacture_date'   => [],
        'sim_expired_date'   => [],
        'last_members'       => [],
        'sn'                 => [],
        'remark'             => []
    ];

    public static function GetCacheKeyByModeAndDevice($deviceIdentifier)
    {
        return "device__${deviceIdentifier}";
    }

    /**
     * @param integer $deviceId
     * @return Device Device with deviceMold
     * @throws Exception
     */
    public static function GetDeviceCacheById(int $deviceId)
    {
        $key = self::GetCacheKeyByModeAndDevice('id__' . $deviceId);
        $result = cache()->sear($key, function () use ($deviceId) {
            return self::query()
                ->with('deviceMold', 'gateway', 'subDevices')
                ->where('id', '=', $deviceId)
                ->first();
        });
        return $result;
    }

    /**
     * @param int $deviceId
     * @return bool
     * @throws Exception
     */
    public static function RemoveDeviceCacheById(int $deviceId)
    {
        $key = self::GetCacheKeyByModeAndDevice('id__' . $deviceId);
        $result = cache()->forget($key);
        return $result;
    }


    /**
     * Gateway shall be cached.
     * Device does not belong to a gateway ( gateway_device_id is null ) shall be cached.
     * @param string $deviceIdentifier
     * @return Device Device with deviceMold
     * @throws Exception
     */
    public static function GetDeviceCacheRecord(string $deviceIdentifier)
    {
        $key = self::GetCacheKeyByModeAndDevice($deviceIdentifier);
        $result = cache()->sear($key, function () use ($deviceIdentifier) {
            return self::query()
                ->with('deviceMold', 'gateway', 'subDevices')
                ->where('identifier', '=', $deviceIdentifier)
                ->first();
        });
        return $result;
    }

    /**
     * @param string $deviceIdentifier
     * @return bool
     * @throws Exception
     */
    public static function RemoveDeviceCacheRecord(string $deviceIdentifier)
    {
        $key = self::GetCacheKeyByModeAndDevice($deviceIdentifier);
        $result = cache()->forget($key);
        return $result;
    }

    /**
     * @param string $deviceModelCode
     * @return bool
     * @throws Exception
     */
    public static function RemoveDeviceCacheRecordByMode(string $deviceModelCode)
    {
        $deviceIdentifiers = Device::query()->whereHas('deviceMold', function ($q) use ($deviceModelCode) {
            $q->where('model_code', '=', $deviceModelCode);
        })->pluck('identifier', 'id');
        foreach ($deviceIdentifiers as $deviceId => $deviceIdentifier) {
            Device::RemoveDeviceCacheRecord($deviceIdentifier);
            Device::RemoveDeviceCacheById($deviceId);
        };
    }

    /**
     * @param Device $device
     * @return void
     * @throws Exception
     */
    public static function RemoveDeviceCache(Device $device)
    {
        Device::RemoveDeviceCacheRecord($device->identifier);
        Device::RemoveDeviceCacheById($device->id);
    }

    /**
     * Devices from a tenant (and sub tenants)
     * @param Builder $query
     * @param int $tenantId
     * @param bool $includeSubTenants
     * @return Builder
     */
    public function scopeTenantId(Builder $query, int $tenantId, $includeSubTenants = false)
    {
        if ($includeSubTenants) {
            $tenantAliasName = "cascade_tenants_" . time();
            $tenantQuery = DB::table($tenantAliasName)->select('*')->fromSub("WITH RECURSIVE CTE AS (
    SELECT A.* FROM tenants A WHERE A.id=" . $tenantId . "
    UNION ALL
    SELECT B.* FROM tenants B INNER JOIN CTE C ON C.id = B.parent_tenant_id )
    SELECT CTE.id FROM CTE", $tenantAliasName);
            return $query->whereIn($this->table . '.tenant_id', $tenantQuery);
        } else {
            return $query->where($this->table . '.tenant_id', $tenantId);
        }
    }

    /**
     * Devices from a watch point (and sub watch points)
     * @param Builder $query
     * @param int $watchPointId
     * @param bool $includeSubWatchPoints
     * @return Builder
     */
    public function scopeWatchPointId(Builder $query, int $watchPointId, $includeSubWatchPoints = false)
    {
        if ($includeSubWatchPoints) {
            $watchPointAliasName = "cascade_watchpoints_" . time();
            $watchPointQuery = DB::table($watchPointAliasName)->select('*')->fromSub("WITH RECURSIVE CTE AS (
        SELECT A.* FROM watch_points A WHERE A.id=" . $watchPointId . "
        UNION ALL
        SELECT B.* FROM watch_points B INNER JOIN CTE C ON C.id = B.parent_id )
        SELECT CTE.id FROM CTE", $watchPointAliasName);
            return $query->whereIn($this->table . '.watch_point_id', $watchPointQuery);
        } else {
            return $query->where($this->table . '.watch_point_id', $watchPointId);
        }
    }

    public function getPropertyFieldCodeValuePairs()
    {
        $fields = $this->devicePropertyFields()->pluck('code', 'device_property_fields.id');
        $values = $this->devicePropertyValues()->get()->pluck('property_value', 'device_property_field_id');
        $result = [];
        $fields->each(function ($fieldCode, $fieldId) use ($values, &$result) {
            $propertyValue = $values[$fieldId];
            $result[$fieldCode] = $propertyValue;
        });
        return $result;
    }

    /**
     * @param $deviceIds array
     */
    public static function updateDevicesLastSaveTime($deviceIds)
    {
        DB::table('devices')->whereIn('id', $deviceIds)->update(['last_save_time' => date('Y-m-d H:i:s', time())]);
    }

    public function watchPoints()
    {
        return $this->belongsToMany('Modules\\System\\Models\\WatchPoint', 'watch_point_has_devices');
    }

    // TODO Delete
    public function watchPoint()
    {
        return $this->belongsToMany('Modules\\System\\Models\\WatchPoint', 'watch_point_has_devices');
    }

    public function deviceMold()
    {
        return $this->belongsTo('Modules\\System\\Models\\DeviceMold');
    }

    public function tenant()
    {
        return $this->belongsTo('Modules\\System\\Models\\Tenant');
    }
    
    public function building()
    {
        return $this->belongsTo('Modules\\System\\Models\\Building');
    }

    public function controllerBuildings()
    {
        return $this->belongsToMany('Modules\\System\\Models\\Building', 'building_has_controllers');
    }

    public function devicePropertyValues()
    {
        return $this->hasMany('Modules\\System\\Models\\DevicePropertyValue');
    }

    public function deviceCalibrationRecords()
    {
        return $this->hasMany('Modules\\System\\Models\\DeviceCalibrationRecord');
    }

    public function deviceLatestCalibrationRecord()
    {
        return $this->hasMany('Modules\\System\\Models\\DeviceCalibrationRecord')->orderBy('created_at', 'desc')->limit(1);
    }

    public function devicePropertyFields()
    {
        return $this->hasManyThrough('Modules\\System\\Models\\DevicePropertyField', 'Modules\\System\\Models\\DeviceMold',
            'id', 'device_mold_id', 'device_mold_id', 'id');
    }

    public function deviceTelemetryFields()
    {
        return $this->hasManyThrough('Modules\\System\\Models\\DeviceTelemetryField', 'Modules\\System\\Models\\DeviceMold',
            'id', 'device_mold_id', 'device_mold_id', 'id');
    }

    public function operatorDeviceUsages()
    {
        return $this->hasMany('Modules\\System\\Models\\OperatorDeviceUsages', 'device_id')->orderBy('usage_time', 'desc');
    }

    public function lastOperatorDeviceUsage()
    {
        return $this->hasOne('Modules\\System\\Models\\OperatorDeviceUsages', 'device_id')->orderBy('usage_time', 'desc');
    }

    public function telemetryValues()
    {
        return $this->hasMany('Modules\\System\\Models\\TelemetryValueCommon', 'device_identifier', 'device_identifier')->orderBy('measured_at', 'desc')->limit(20);
    }

    public function telemetryValuesNew()
    {
        return $this->hasMany('Modules\\System\\Models\\TelemetryValues', 'device_identifier', 'device_identifier')->orderBy('measured_at', 'desc')->limit(20);
    }

    public function deviceActions()
    {
        return $this->hasManyThrough('Modules\\System\\Models\\DeviceAction', 'Modules\\System\\Models\\DeviceMold',
            'id', 'device_mold_id', 'device_mold_id', 'id');
    }

    public function ruleCriteria()
    {
        return $this->hasMany('Modules\\System\\Models\\RuleCriteria', 'device_id');
    }

    public function ruleDefinitionCriteria()
    {
        return $this->ruleCriteria()->whereNull('parent_id');
    }

    public function ruleSpecifiedCriteria()
    {
        return $this->ruleCriteria()->whereNotNull('parent_id');
    }

    public function gateway()
    {
        return $this->belongsTo('Modules\\System\\Models\\Device', 'gateway_device_id');
    }

    public function subDevices()
    {
        return $this->hasMany('Modules\\System\\Models\\Device', 'gateway_device_id');
    }

    public function space()
    {
        return $this->belongsTo('Modules\\System\\Models\\Space');
    }

    public function tenantAdmins()
    {
        return $this->belongsToMany(
            'Modules\\System\\Models\\TenantAdmin',
            'tenant_admins_devices',
            'device_id',
            'tenant_admin_id')->withPivot('can_use');
    }

    public function tenantAdminsCanUse()
    {
        return $this->tenantAdmins()->wherePivot('can_use', true);
    }

    public function lastOnlineRecord(){
        return $this->hasOne('Modules\\System\\Models\\DeviceOnlineRecord','device_identifier','id')->orderByRaw('start_time DESC, id DESC');
    }

    public function subOnlineDevices()
    {
        return $this->hasMany('Modules\\System\\Models\\Device', 'gateway_device_id')->where('last_save_time', '>', now()->subSeconds(40)->setTimezone('UTC'));
    }

    /**
     * Get all device associations
     */
    public function deviceAssociations()
    {
        return $this->hasMany('Modules\\System\\Models\\MemberDeviceAssociation', 'device_identifier', 'identifier');
    }

    /**
     * Get current active device associations
     */
    public function currentDeviceAssociations()
    {
        return $this->deviceAssociations()->where('is_active', true);
    }

    /**
     * Get current associated member
     */
    public function currentAssociatedMember()
    {
        return $this->hasOneThrough(
            'Modules\\System\\Models\\Member',
            'Modules\\System\\Models\\MemberDeviceAssociation',
            'device_identifier', // Foreign key on member_device_associations table
            'card_id', // Foreign key on members table  
            'identifier', // Local key on devices table
            'member_id' // Local key on member_device_associations table
        )->where('member_device_associations.is_active', true);
    }

    /**
     * Get current associated members with their teams
     */
    public function currentAssociatedMembersWithTeam()
    {
        return $this->hasManyThrough(
            'Modules\\System\\Models\\Member',
            'Modules\\System\\Models\\MemberDeviceAssociation',
            'device_identifier', // Foreign key on member_device_associations table
            'card_id', // Foreign key on members table
            'identifier', // Local key on devices table
            'member_id' // Local key on member_device_associations table
        )->where('member_device_associations.is_active', true)
         ->with('team');
    }

    public function channelType()
    {
        return $this->belongsTo('Modules\\System\\Models\\ChannelType', 'channel_type', 'key');
    }

    public function alarmLogs()
    {
        return $this->hasMany('Modules\\System\\Models\\AlarmLog', 'device_id');
    }

    public function events()
    {
        return $this->hasMany('Modules\\System\\Models\\Event');
    }

    public function uploads()
    {
        return $this->morphMany('Modules\\System\\Models\\Upload', 'uploadable');
    }

    public function images()
    {
        return $this->uploads()->where('type', Upload::TYPE_DEVICE_IMAGE);
    }

    public function setAlarmDuration($value)
    {
        if (is_array($value) && $value['alarm'] !== '') {
            $alarmRedis = Redis::Connection('alarm-devices');
            $anomaly = device::getAnomalyLevel($value);
            $alarmKey = $value['device_identifier'] . "_" . $value['field_code'] . "_" . $anomaly;
            $alarmDeviceCode = $alarmRedis->get($alarmKey);
            if ($alarmDeviceCode) {
                $alarmDeviceCode = unserialize($alarmDeviceCode);
                $value['duration'] = $alarmDeviceCode['duration'];
            } else {
                $value['duration'] = 0;
            }
        }
        return $value;
    }

    public function getChannelIdAttribute($value)
    {
        // 示例：00:e0:b4:65:14:b41，前17个字符是 mac, 其余的是 channelId
        // 截取不到返回空字符串
        $channelId = substr($this->identifier, 17) ?? '';
        return $channelId;
    }

    /**
     * @return Builder|Model|object
     */
    public function getIsOnlineAttribute($value)
    {
        if ($this->is_gateway) {
            $onlineDevice = Device::query()
            ->where('gateway_device_id', $this->id)
            ->where('last_save_time', '>', now()->subSeconds(100)->setTimezone('UTC'))
            ->first();
            return $onlineDevice ? true : false;
        } else {
            $redis = Redis::Connection('online-devices');
            $isOnline = $redis->get($this->identifier);
            return $isOnline === 1 || $isOnline === '1';
        }
    }

    public function getChannelTypeAttribute($value)
    {
        $channelTypeValue = '';
        if ($value) {
            $channelType = ChannelType::where('key', $value)->first();
            $channelTypeValue = $channelType ? $channelType->value : '';
        }
        return $channelTypeValue;
    }

    public function getAnomalyAttribute($value)
    {
        if ($this->last_save_time > now()->subSeconds(40)->setTimezone('UTC')) {
            return $value;
        }
        return '';
    }

    public function getInhibitAttribute($value)
    {
        if ($this->last_save_time > now()->subSeconds(40)->setTimezone('UTC')) {
            return $value;
        }
        return 0;
    }

    public static function getAnomalyLevel($data)
    {
        switch (true) {
            case $data['alarm'] > 0:
                $infos = ['', 'A1', 'A2'];
                $key = $infos[$data['alarm']];
                break;
            case $data['fault'] > 0:
                $key = 'fault';
                break;
            case $data['warning'] > 0:
                $key = 'warning';
                break;
            case $data['info'] > 0:
                $key = 'info';
                break;
            default:
                $key = '';
                break;
        }
        return $key;
    }

    public static function getInhibit($data)
    {
        if($data['inhibit'] > 0){
            return 'inhibit';
        }
        return '';
    }
}
