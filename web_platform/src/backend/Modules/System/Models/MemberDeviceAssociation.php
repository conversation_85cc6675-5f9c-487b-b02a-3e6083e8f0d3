<?php

namespace Modules\System\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * @OA\Schema(
 *     schema="System.MemberDeviceAssociation",
 *     title="System.MemberDeviceAssociation",
 *     description="Member Device Association model for tracking device-member relationships.",
 *     type="object",
 *     @OA\Property(property="id", ref="#/components/schemas/id"),
 *     @OA\Property(property="member_id", type="string", description="Member ID from data stream"),
 *     @OA\Property(property="device_identifier", type="string", description="Device identifier from data stream"),
 *     @OA\Property(property="device_type", type="string", description="Device type: wristband, pressure_gauge, gas_detector, tic, etc."),
 *     @OA\Property(property="gateway_device_identifier", type="string", description="Gateway device identifier"),
 *     @OA\Property(property="association_start", type="string", format="date-time", description="When this association started"),
 *     @OA\Property(property="association_end", type="string", format="date-time", description="When this association ended"),
 *     @OA\Property(property="is_active", type="boolean", description="Whether this association is currently active"),
 *     @OA\Property(property="device_metadata", type="object", description="Device metadata from data stream"),
 *     @OA\Property(property="created_at", ref="#/components/schemas/created_at"),
 *     @OA\Property(property="updated_at", ref="#/components/schemas/updated_at"),
 * )
 *
 * @property integer $id
 * @property string $member_id
 * @property string $device_identifier
 * @property string $device_type
 * @property string $gateway_device_identifier
 * @property string $association_start
 * @property string $association_end
 * @property boolean $is_active
 * @property array $device_metadata
 * @property string $created_at
 * @property string $updated_at
 */
class MemberDeviceAssociation extends Model
{
    public $table = 'member_device_associations';
    
    public $fillable = [
        'member_id',
        'device_identifier', 
        'device_type',
        'gateway_device_identifier',
        'association_start',
        'association_end',
        'is_active',
        'device_metadata'
    ];
    
    public $searchable = [
        'member_id',
        'device_identifier',
        'device_type', 
        'gateway_device_identifier',
        'association_start',
        'association_end',
        'is_active',
        'created_at',
        'updated_at'
    ];
    
    protected $casts = [
        'id' => 'integer',
        'member_id' => 'string',
        'device_identifier' => 'string',
        'device_type' => 'string',
        'gateway_device_identifier' => 'string',
        'association_start' => 'datetime',
        'association_end' => 'datetime',
        'is_active' => 'boolean',
        'device_metadata' => 'array'
    ];
    
    protected $dates = ['association_start', 'association_end'];
    
    public static $rules = [
        'member_id' => ['required', 'string', 'max:255'],
        'device_identifier' => ['required', 'string', 'max:255'],
        'device_type' => ['required', 'string', 'max:50'],
        'gateway_device_identifier' => ['nullable', 'string', 'max:255'],
        'association_start' => ['required', 'date'],
        'association_end' => ['nullable', 'date'],
        'is_active' => ['boolean'],
        'device_metadata' => []
    ];
    
    /**
     * Get the member associated with this device association
     */
    public function member()
    {
        return $this->belongsTo('Modules\\System\\Models\\Member', 'member_id', 'card_id');
    }
    
    /**
     * Get the device associated with this association
     */
    public function device()
    {
        return $this->belongsTo('Modules\\System\\Models\\Device', 'device_identifier', 'identifier');
    }
    
    /**
     * Get the gateway device associated with this association
     */
    public function gatewayDevice()
    {
        return $this->belongsTo('Modules\\System\\Models\\Device', 'gateway_device_identifier', 'identifier');
    }
    
    /**
     * Scope to get only active associations
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }
    
    /**
     * Scope to get associations for a specific device
     */
    public function scopeForDevice($query, $deviceIdentifier)
    {
        return $query->where('device_identifier', $deviceIdentifier);
    }
    
    /**
     * Scope to get associations for a specific member
     */
    public function scopeForMember($query, $memberId)
    {
        return $query->where('member_id', $memberId);
    }
}
