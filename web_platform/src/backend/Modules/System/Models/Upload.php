<?php

namespace Modules\System\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Storage;
use zgldh\UploadManager\UploadManager;

/**
 * @OA\Schema(
 *     schema="System.Upload",
 *     title="System.Upload",
 *     description="Upload model of System module.",
 *     type="object",
 *     @OA\Property(property="id", ref="#/components/schemas/id"),
 *     @OA\Property(property="name", type="string", description="The name of the upload"),
 *     @OA\Property(property="description", type="string", description="The description of the upload"),
 *     @OA\Property(property="disk", type="string", description="The disk stored the file", readOnly=true,),
 *     @OA\Property(property="path", type="string", description="The relative path", readOnly=true,),
 *     @OA\Property(property="size", type="integer", description="The file size. Byte", readOnly=true,),
 *     @OA\Property(property="type", type="string", description="Upload file type. Like avatar, flat_map.", readOnly=true,),
 *     @OA\Property(property="account_id", type="integer", description="Who uploaded this file.", readOnly=true,),
 *     @OA\Property(property="uploadable_id", type="integer", description="The uploadable_id of the upload", readOnly=true,),
 *     @OA\Property(property="uploadable_type", type="string", description="The uploadable_type of the upload", readOnly=true,),
 *     @OA\Property(property="account", type="object",
 *      allOf={@OA\Schema(ref="#/components/schemas/System.Account")},
 *     ),
 *     @OA\Property(property="url", type="string", description="The URL to access this file", readOnly=true,),
 *     @OA\Property(property="created_at", ref="#/components/schemas/created_at"),
 *     @OA\Property(property="updated_at", ref="#/components/schemas/updated_at"),
 * )
 * @property $id                 integer
 * @property $name               string
 * @property $description        string
 * @property $disk               string
 * @property $path               string
 * @property $size               integer
 * @property $type               string
 * @property $account_id         integer
 * @property $uploadable_id      integer
 * @property $uploadable_type    string
 * @property $url                string
 * @property $created_at         string
 * @property $updated_at         string
 */
class Upload extends Model
{
    const TYPE_AVATAR = 'avatar';
    const TYPE_DEVICE_IMAGE = 'device_image';

    public $table = 'uploads';

    public $fillable = [
        'name',
        'description',
        'disk',
        'path',
        'size',
        'type',
        'account_id',
    ];

    /**
     * The attributes that should be casted to native types.
     *
     * @var array
     */
    protected $casts = [
        'name'            => 'string',
        'description'     => 'string',
        'disk'            => 'string',
        'path'            => 'string',
        'size'            => 'integer',
        'type'            => 'string',
        'account_id'      => 'integer',
        'uploadable_id'   => 'integer',
        'uploadable_type' => 'string',
    ];

    /**
     * Validation rules
     *
     * @var array
     */
    public static $rules = [
        'disk' => '',
    ];

    protected $appends = [
        'url',
    ];

    /**
     * @return BelongsTo
     **/
    public function account()
    {
        return $this->belongsTo(Account::class, 'account_id', 'id');
    }


    public function uploadable()
    {
        return $this->morphTo();
    }

    public function scopeUsed($query)
    {
        return $query->has('uploadable');
    }

    public function scopeUnUsed($query)
    {
        return $query->whereNull('uploadable_id')->whereNull('uploadable_type');
    }

    public function getUrlAttribute()
    {
        $manager = UploadManager::getInstance();
        $url = $manager->getUploadUrl($this->disk, $this->path);
        return $url;
    }

    public function deleteFile($autoSave = true)
    {
        if ($this->path) {
            $disk = Storage::disk($this->disk);
            if ($disk->exists($this->path)) {
                $disk->delete($this->path);
                $this->path = '';
                if ($autoSave) {
                    $this->save();
                }
            }
        }
    }

    public function isInDisk($diskName)
    {
        return $this->disk == $diskName ? true : false;
    }

    public function isUnUsed()
    {
        return !$this->uploadable_id && !$this->uploadable_type;
    }

    public function moveToDisk($newDiskName)
    {
        if ($newDiskName == $this->disk) {
            return true;
        }
        $currentDisk = Storage::disk($this->disk);
        $content = $currentDisk->get($this->path);

        $newDisk = Storage::disk($newDiskName);
        $newDisk->put($this->path, $content);
        if ($newDisk->exists($this->path)) {
            $this->disk = $newDiskName;
            $this->save();
            $currentDisk->delete($this->path);
            return true;
        }
        return false;
    }
}
