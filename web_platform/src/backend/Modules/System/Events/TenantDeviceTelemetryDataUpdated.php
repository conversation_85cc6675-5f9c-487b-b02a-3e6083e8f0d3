<?php

namespace Modules\System\Events;

use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;

/**
 * Class TenantDeviceTelemetryDataUpdated
 * @package Modules\System\Events
 *
 * Event fired when device telemetry data is updated for any device within a tenant
 * This event is used for tenant-level management interfaces and dashboards
 * that need to monitor all devices within their tenant scope
 */
class TenantDeviceTelemetryDataUpdated implements ShouldBroadcast
{
    /**
     * The tenant ID
     *
     * @var int
     */
    public $tenantID;

    /**
     * The device telemetry data payload
     *
     * @var array
     */
    public $data;

    /**
     * Create a new event instance.
     *
     * @param int $tenantID
     * @param array $data
     */
    public function __construct(int $tenantID, array $data)
    {
        $this->tenantID = $tenantID;
        $this->data = $data;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return \Illuminate\Broadcasting\Channel|array
     */
    public function broadcastOn()
    {
        return new PrivateChannel("tenant.device.telemetry.{$this->tenantID}");
    }

    /**
     * Get the data to broadcast.
     *
     * @return array
     */
    public function broadcastWith()
    {
        return [
            'tenant_id' => $this->tenantID,
            'device_identifier' => $this->data['device_identifier'],
            'device_id' => $this->data['device_id'],
            'device_name' => $this->data['device_name'] ?? null,
            'device_mold_code' => $this->data['device_mold_code'],
            'member_name' => $this->data['member_name'],
            'source' => $this->data['source'],
            'gateway' => $this->data['gateway'],
            'association_period' => $this->data['association_period'],
            'metadata' => $this->data['metadata'],
            'telemetry' => $this->data['telemetry'],
            'stats' => $this->data['stats'] ?? [],
            'timestamp' => $this->data['timestamp']
        ];
    }

    /**
     * The event's broadcast name.
     *
     * @return string
     */
    public function broadcastAs()
    {
        return 'tenant.device.telemetry.updated';
    }

    /**
     * Determine if this event should broadcast.
     *
     * @return bool
     */
    public function shouldBroadcast()
    {
        // Only broadcast if we have valid telemetry data and tenant ID
        return isset($this->data['telemetry']) 
            && is_array($this->data['telemetry'])
            && !empty($this->tenantID)
            && !empty($this->data['device_identifier']);
    }
}
