<?php

namespace Modules\System\Events;

use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;

/**
 * Class DeviceTelemetryDataUpdated
 * @package Modules\System\Events
 *
 * Event fired when device telemetry data is received
 * This event broadcasts telemetry data for a specific device
 * including device metadata and sensor measurements
 */
class DeviceTelemetryDataUpdated implements ShouldBroadcast
{
    /**
     * The device identifier
     *
     * @var string
     */
    public $deviceIdentifier;

    /**
     * The device telemetry data payload
     *
     * @var array
     */
    public $data;

    /**
     * Create a new event instance.
     *
     * @param string $deviceIdentifier
     * @param array $data
     */
    public function __construct(string $deviceIdentifier, array $data)
    {
        $this->deviceIdentifier = $deviceIdentifier;
        $this->data = $data;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return \Illuminate\Broadcasting\Channel|array
     */
    public function broadcastOn()
    {
        return new PrivateChannel("device.telemetry.{$this->deviceIdentifier}");
    }

    /**
     * Get the data to broadcast.
     *
     * @return array
     */
    public function broadcastWith()
    {
        return [
            'device_identifier' => $this->deviceIdentifier,
            'device_id' => $this->data['device_id'],
            'device_mold_code' => $this->data['device_mold_code'],
            'member_name' => $this->data['member_name'],
            'source' => $this->data['source'],
            'gateway' => $this->data['gateway'],
            'association_period' => $this->data['association_period'],
            'metadata' => $this->data['metadata'],
            'telemetry' => $this->data['telemetry'],
            'stats' => $this->data['stats'] ?? [],
            'timestamp' => $this->data['timestamp']
        ];
    }

    /**
     * The event's broadcast name.
     *
     * @return string
     */
    public function broadcastAs()
    {
        return 'device.telemetry.updated';
    }

    /**
     * Determine if this event should broadcast.
     *
     * @return bool
     */
    public function shouldBroadcast()
    {
        // Only broadcast if we have valid telemetry data
        return isset($this->data['telemetry']) 
            && is_array($this->data['telemetry'])
            && !empty($this->deviceIdentifier);
    }
}
