<?php

namespace Modules\System\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Modules\System\Jobs\MemberRealtimeDataJob;
use Modules\System\Jobs\DeviceTelemetryDataJob;
use PhpAmqpLib\Connection\AMQPStreamConnection;
use PhpAmqpLib\Exception\AMQPRuntimeException;
use PhpAmqpLib\Message\AMQPMessage;

class SystemRealtimeBroadcastConsumer extends Command
{
    protected $disableLogging = false;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'system:amqp';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Start the Realtime Broadcast Consumer';

    /**
     * @var AMQPStreamConnection
     */
    private $connection = null;

    public function __construct()
    {
        parent::__construct();
    }

    public function __destruct()
    {
        if ($this->connection) {
            $this->line(" [*] Closing realtime broadcast connection...");
            $this->connection->close();
        }
    }

    public function line($string, $style = null, $verbosity = null)
    {
        if ($this->disableLogging) {
            return;
        }
        parent::line($string, $style, $verbosity);
    }

    private function makeConnection()
    {
        $host = config('scaffold.amqp.host');
        $port = config('scaffold.amqp.port');
        $username = config('scaffold.amqp.username');
        $password = config('scaffold.amqp.password');
        $vhost = config('scaffold.amqp.vhost', '/');
        $this->line("AMQP Configuration:");
        $this->line("  Host: {$host}");
        $this->line("  Port: {$port}");
        $this->line("  Username: {$username}");
        $this->line("  Password: {$password}");
        $this->line("  Vhost: {$vhost}");
        $connection_timeout = round(config('scaffold.amqp.connection_timeout'), 1);
        $read_write_timeout = round(config('scaffold.amqp.read_write_timeout'), 1);
        $heartbeat = intval(config('scaffold.amqp.heartbeat'));

        return new AMQPStreamConnection($host, $port, $username, $password, $vhost, false,
            'AMQPLAIN', null, 'en_US', $connection_timeout, $read_write_timeout,
            null, true, $heartbeat);
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     * @throws \Illuminate\Contracts\Container\BindingResolutionException
     */
    public function handle()
    {
        $this->line("Starting realtime broadcast consumer...");
        
        while(true) {
            try {
                $this->line("Attempting to connect to AMQP server...");
                $this->connection = $this->makeConnection();
                $this->line("Connected to AMQP server successfully.");
                
                register_shutdown_function(function () {
                    $this->shutdown();
                }, $this->connection);
                
                $this->do();
            } catch(AMQPRuntimeException $e) {
                $this->error('Realtime broadcast AMQPRuntime exception: ' . $e->getMessage());
                logger()->error('Realtime broadcast AMQPRuntime exception', [$e->getMessage()]);
                $this->cleanupConnection();
                sleep(30);
            } catch(\RuntimeException $e) {
                $this->error('Realtime broadcast Runtime exception: ' . $e->getMessage());
                logger()->error('Realtime broadcast Runtime exception', [$e->getMessage()]);
                $this->cleanupConnection();
                sleep(30);
            } catch(\ErrorException $e) {
                $this->error('Realtime broadcast Error exception: ' . $e->getMessage());
                logger()->error('Realtime broadcast Error exception', [$e->getMessage()]);
                $this->cleanupConnection();
                sleep(30);
            } catch(\Exception $e) {
                $this->error('Realtime broadcast General exception: ' . $e->getMessage());
                logger()->error('Realtime broadcast General exception', [$e->getMessage()]);
                $this->cleanupConnection();
                sleep(30);
            }
        }

        return true;
    }

    /**
     * Business logic
     */
    public function do()
    {
        $this->line("Creating AMQP channel...");
        $channel = $this->connection->channel();
        
        // Configuration for realtime broadcast queue
        $queueName = config('scaffold.amqp.realtime_broadcast.queue_name', 'lcs.realtime_data.realtime_broadcast');
        $exchangeName = config('scaffold.amqp.realtime_broadcast.exchange_name', 'lcs.realtime_data');
        $routingKey = config('scaffold.amqp.realtime_broadcast.routing_key', 'realtime.member.*');
        
        $this->line("Configuration:");
        $this->line("  Exchange: {$exchangeName}");
        $this->line("  Queue: {$queueName}");
        $this->line("  Routing Key: {$routingKey}");
        
        // Declare exchange
        $this->line("Declaring exchange: {$exchangeName}");
        $channel->exchange_declare($exchangeName, 'topic', false, false, false);
        $this->line("Exchange declared successfully.");
        
        // Declare queue
        $this->line("Declaring queue: {$queueName}");
        $channel->queue_declare($queueName, false, true, false, false, false, [
            'x-message-ttl' => array('I', 300000) // 5 minutes TTL for realtime messages
        ]);
        $this->line("Queue declared successfully.");
        
        // Bind queue to exchange
        $this->line("Binding queue to exchange with routing key: {$routingKey}");
        $channel->queue_bind($queueName, $exchangeName, $routingKey);
        $this->line("Queue bound successfully.");
        
        logger()->info("Waiting for realtime broadcast messages from {$queueName}");
        $this->line(" [*] Waiting for realtime broadcast messages from {$queueName}. To exit press CTRL+C");
        
        $channel->basic_qos(null, env('REALTIME_PREFETCH_COUNT', 10), false);
        $channel->basic_consume($queueName, '', false, false, false, false, function ($msg) {
            $this->consume($msg);
        });
        
        while ($channel->is_consuming()) {
            $channel->wait();
        }
        
        $this->line(" [*] Realtime broadcast connection closed.");
    }

    /**
     * Consume the realtime broadcast message
     *
     * @param AMQPMessage $message The message
     */
    public function consume(AMQPMessage $message)
    {
        try {
            $payload = $this->parsePayload($message->body);
            
            // Determine data format and route to appropriate handler
            if ($this->isDeviceTelemetryFormat($payload)) {
                $this->handleDeviceTelemetryMessage($payload);
            } elseif ($this->validateRealtimePayload($payload)) {
                $this->handleMemberRealtimeMessage($payload);
            } else {
                $this->warn("Unknown or invalid message format received");
                logger()->warning("Unknown message format", [
                    'payload_keys' => array_keys($payload),
                    'sample_data' => array_slice($payload, 0, 3, true)
                ]);
            }
            
            $this->ack($message);
        } catch (\Exception $e) {
            $this->warn("Failed to handle broadcast message: " . $e->getMessage());
            $this->warn(
                sprintf("%s On %s Line %s", $e->getMessage(), $e->getFile(), $e->getLine())
            );
            Log::error('Failed to handle broadcast message: '. $e->getMessage(). $e->getFile().$e->getLine());
            $this->ack($message);
        } finally {
            unset($payload, $message);
        }
    }

    /**
     * Check if payload is in device telemetry format
     *
     * @param array $payload
     * @return bool
     */
    private function isDeviceTelemetryFormat(array $payload)
    {
        // Device telemetry format has device_identifier and telemetry fields
        // and does not have the member_realtime_data type field
        return isset($payload['device_identifier']) 
            && isset($payload['telemetry']) 
            && isset($payload['metadata'])
            && !isset($payload['type']);
    }

    /**
     * Handle device telemetry message
     *
     * @param array $payload
     */
    private function handleDeviceTelemetryMessage(array $payload)
    {
        if (!$this->validateDeviceTelemetryPayload($payload)) {
            $this->warn("Invalid device telemetry payload received");
            return;
        }
        
        // Dispatch job to handle device telemetry data
        DeviceTelemetryDataJob::dispatch($payload)->onQueue('device_telemetry');
        
        // Device telemetry message dispatched successfully
    }

    /**
     * Handle member realtime message
     *
     * @param array $payload
     */
    private function handleMemberRealtimeMessage(array $payload)
    {
        // Dispatch job to handle realtime broadcasting
        MemberRealtimeDataJob::dispatch($payload)->onQueue('realtime_broadcast');
        
        // Member realtime broadcast message dispatched successfully
    }

    /**
     * Validate realtime payload structure
     *
     * @param array $payload
     * @return bool
     */
    private function validateRealtimePayload(array $payload)
    {
        $requiredFields = ['type', 'member_id', 'tenant_id', 'member_data', 'device_data'];
        
        foreach ($requiredFields as $field) {
            if (!isset($payload[$field])) {
                logger()->warning("Missing required field in realtime payload", ['field' => $field]);
                return false;
            }
        }
        
        // Validate message type
        if ($payload['type'] !== 'member_realtime_data') {
            logger()->warning("Invalid message type in realtime payload", ['type' => $payload['type']]);
            return false;
        }
        
        // Validate data structure - now directly in payload, no 'data' wrapper
        if (!is_array($payload['member_data']) || !is_array($payload['device_data'])) {
            logger()->warning("Invalid data structure in realtime payload");
            return false;
        }
        
        return true;
    }

    /**
     * Validate device telemetry payload structure
     *
     * @param array $payload
     * @return bool
     */
    private function validateDeviceTelemetryPayload(array $payload)
    {
        $requiredFields = ['device_identifier', 'metadata', 'telemetry'];
        
        foreach ($requiredFields as $field) {
            if (!isset($payload[$field])) {
                logger()->warning("Missing required field in device telemetry payload", ['field' => $field]);
                return false;
            }
        }
        
        // Validate metadata structure
        if (!is_array($payload['metadata'])) {
            logger()->warning("Invalid metadata structure in device telemetry payload");
            return false;
        }
        
        // Check for required metadata fields
        $requiredMetadataFields = ['device_id', 'device_mold_code', 'source'];
        foreach ($requiredMetadataFields as $field) {
            if (!isset($payload['metadata'][$field])) {
                logger()->warning("Missing required metadata field in device telemetry payload", ['field' => $field]);
                return false;
            }
        }
        
        // Validate telemetry data structure
        if (!is_array($payload['telemetry']) || empty($payload['telemetry'])) {
            logger()->warning("Invalid or empty telemetry data in device telemetry payload");
            return false;
        }
        
        // Validate telemetry data format - each type should contain array of measurements
        foreach ($payload['telemetry'] as $type => $measurements) {
            if (!is_array($measurements)) {
                logger()->warning("Invalid telemetry measurement format for type: {$type}");
                return false;
            }
            
            // Validate each measurement has required fields
            foreach ($measurements as $measurement) {
                if (!is_array($measurement) || !isset($measurement['value']) || !isset($measurement['measured_at'])) {
                    logger()->warning("Invalid measurement structure for telemetry type: {$type}");
                    return false;
                }
            }
        }
        
        return true;
    }

    /**
     * Send ack response to AMQP server
     *
     * @param AMQPMessage $message
     */
    protected function ack(AMQPMessage $message)
    {
        try {
            $message->delivery_info['channel']->basic_ack($message->delivery_info['delivery_tag']);
        } catch (\Exception $e) {
            $this->error("Fail to send ACK to server: " . $e->getMessage());
        }
    }

    /**
     * @param string $body
     * @return array
     */
    private function parsePayload(string $body)
    {
        return json_decode($body, true);
    }

    function shutdown()
    {
        if($this->connection !== null) {
            $this->connection->close();
        }
    }

    function cleanupConnection() {
        // Connection might already be closed.
        // Ignoring exceptions.
        try {
            if($this->connection !== null) {
                $this->connection->close();
            }
        } catch (\ErrorException $e) {
            $this->warn(" Connection exception: " . $e->getMessage());
            $this->warn(
                sprintf("%s On %s Line %s", $e->getMessage(), $e->getFile(), $e->getLine())
            );
            Log::error('Realtime broadcast connection exception:', [$e->getMessage(), $e->getFile(), $e->getLine()]);
        }
    }
}