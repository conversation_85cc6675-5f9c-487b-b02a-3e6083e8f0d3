<?php

namespace Modules\System\Controllers;

use App\Http\Controllers\Controller;
use Modules\System\Repositories\MemberDataRepository;
use Modules\System\Models\Member;
use Modules\System\Contracts\TenantPolicyManagerInterface;
use Illuminate\Http\Request;
use Illuminate\Auth\Access\AuthorizationException;
use Carbon\Carbon;

class MemberDataController extends Controller
{
    private $memberDataRepository;
    
    public function __construct(MemberDataRepository $memberDataRepository)
    {
        $this->memberDataRepository = $memberDataRepository;
    }
    
    /**
     * @OA\Get(
     *      path="/system/tenant/{tenantId}/member-data/telemetry",
     *      operationId="MemberDataController::getMemberTelemetryData",
     *      tags={"System.MemberData"},
     *      summary="Get telemetry data for a specific member",
     *      description="Returns telemetry data for a specific member. Can return data within specified time range or just the latest values for each field when latest_only=true. Includes member's own data and associated device data.",
     *      security={{"api_http_auth": {}}},
     *      @OA\Parameter(
     *          name="tenantId",
     *          description="Tenant ID",
     *          required=true,
     *          in="path",
     *          @OA\Schema(type="integer")
     *      ),
     *      @OA\Parameter(
     *          name="member_id",
     *          description="Member ID (card_id)",
     *          required=true,
     *          in="query",
     *          @OA\Schema(type="string")
     *      ),
     *      @OA\Parameter(
     *          name="start_time",
     *          description="Start time for data query (ISO 8601 format). Required when latest_only=false",
     *          required=false,
     *          in="query",
     *          @OA\Schema(type="string", format="date-time", example="2025-08-04T17:35:37")
     *      ),
     *      @OA\Parameter(
     *          name="end_time",
     *          description="End time for data query (ISO 8601 format). Required when latest_only=false",
     *          required=false,
     *          in="query",
     *          @OA\Schema(type="string", format="date-time", example="2025-08-20T15:12:30")
     *      ),
     *      @OA\Parameter(
     *          name="latest_only",
     *          description="Whether to retrieve only the latest data for each field (ignores time range if true)",
     *          required=false,
     *          in="query",
     *          @OA\Schema(type="boolean", default=false)
     *      ),
     *      @OA\Parameter(
     *          name="include_sub_tenants",
     *          description="Whether to include sub-tenants in query",
     *          required=false,
     *          in="query",
     *          @OA\Schema(type="boolean", default=false)
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="Successful operation",
     *          @OA\JsonContent(
     *              type="object",
     *              required={"success", "data", "query_info"},
     *              @OA\Property(property="success", type="boolean", example=true),
     *              @OA\Property(
     *                  property="data",
     *                  type="object",
     *                  @OA\Property(
     *                      property="member_data",
     *                      type="object",
     *                      description="Member's own telemetry data with measurements over time",
     *                      @OA\AdditionalProperties(
     *                          type="array",
     *                          @OA\Items(
     *                              type="object",
     *                              @OA\Property(property="value", description="Measurement value (string, number, or mixed)"),
     *                              @OA\Property(property="unit", type="string", nullable=true, description="Unit of measurement"),
     *                              @OA\Property(property="measured_at", type="string", format="date-time", description="Timestamp when measurement was taken"),
     *                              @OA\Property(property="alarm", type="integer", nullable=true, description="Alarm status (0=normal, 1=warning, 2=critical)"),
     *                              @OA\Property(property="fault", type="integer", nullable=true, description="Fault status")
     *                          ),
     *                          description="Array of time-series data points for each telemetry field (e.g., Longitude, Latitude, UserName, etc.)"
     *                      )
     *                  ),
     *                  @OA\Property(
     *                      property="device_data",
     *                      type="object",
     *                      description="Associated devices' telemetry data grouped by device type",
     *                      @OA\Property(
     *                          property="wristband",
     *                          type="array",
     *                          @OA\Items(
     *                              type="object",
     *                              @OA\Property(property="device_identifier", type="string", description="Device MAC address or unique identifier"),
     *                              @OA\Property(
     *                                  property="association_period",
     *                                  type="object",
     *                                  @OA\Property(property="start", type="string", format="date-time", description="When device was associated with member"),
     *                                  @OA\Property(property="end", type="string", format="date-time", nullable=true, description="When device association ended")
     *                              ),
     *                              @OA\Property(property="gateway", type="string", description="Gateway device identifier"),
     *                              @OA\Property(
     *                                  property="metadata",
     *                                  type="object",
     *                                  description="Device-specific metadata",
     *                                  @OA\Property(property="WristbandID", type="string")
     *                              ),
     *                              @OA\Property(
     *                                  property="telemetry",
     *                                  type="object",
     *                                  description="Device telemetry data with time-series measurements",
     *                                  @OA\AdditionalProperties(
     *                                      type="array",
     *                                      @OA\Items(
     *                                          type="object",
     *                                          @OA\Property(property="value", description="Measurement value"),
     *                                          @OA\Property(property="unit", type="string", nullable=true),
     *                                          @OA\Property(property="measured_at", type="string", format="date-time"),
     *                                          @OA\Property(property="alarm", type="integer", nullable=true),
     *                                          @OA\Property(property="fault", type="integer", nullable=true)
     *                                      )
     *                                  )
     *                              )
     *                          )
     *                      ),
     *                      @OA\Property(
     *                          property="pressure_gauge",
     *                          type="array",
     *                          @OA\Items(
     *                              type="object",
     *                              @OA\Property(property="device_identifier", type="string"),
     *                              @OA\Property(
     *                                  property="association_period",
     *                                  type="object",
     *                                  @OA\Property(property="start", type="string", format="date-time"),
     *                                  @OA\Property(property="end", type="string", format="date-time", nullable=true)
     *                              ),
     *                              @OA\Property(property="gateway", type="string"),
     *                              @OA\Property(
     *                                  property="metadata",
     *                                  type="object",
     *                                  @OA\Property(property="GaugeID", type="string"),
     *                                  @OA\Property(property="GaugeUnit", type="string"),
     *                                  @OA\Property(property="LastMaintainDate", type="string", format="date"),
     *                                  @OA\Property(property="PressureGaugeType", type="integer")
     *                              ),
     *                              @OA\Property(
     *                                  property="telemetry",
     *                                  type="object",
     *                                  @OA\AdditionalProperties(
     *                                      type="array",
     *                                      @OA\Items(
     *                                          type="object",
     *                                          @OA\Property(property="value", description="Measurement value"),
     *                                          @OA\Property(property="unit", type="string", nullable=true),
     *                                          @OA\Property(property="measured_at", type="string", format="date-time"),
     *                                          @OA\Property(property="alarm", type="integer", nullable=true),
     *                                          @OA\Property(property="fault", type="integer", nullable=true)
     *                                      )
     *                                  )
     *                              )
     *                          )
     *                      ),
     *                      @OA\Property(
     *                          property="gas_detector",
     *                          type="array",
     *                          @OA\Items(
     *                              type="object",
     *                              @OA\Property(property="device_identifier", type="string"),
     *                              @OA\Property(
     *                                  property="association_period",
     *                                  type="object",
     *                                  @OA\Property(property="start", type="string", format="date-time"),
     *                                  @OA\Property(property="end", type="string", format="date-time", nullable=true)
     *                              ),
     *                              @OA\Property(property="gateway", type="string"),
     *                              @OA\Property(
     *                                  property="metadata",
     *                                  type="object",
     *                                  @OA\Property(property="DetectorID", type="string"),
     *                                  @OA\Property(property="DetectorName", type="string"),
     *                                  @OA\Property(property="GasCount", type="integer")
     *                              ),
     *                              @OA\Property(
     *                                  property="telemetry",
     *                                  type="object",
     *                                  @OA\AdditionalProperties(
     *                                      type="array",
     *                                      @OA\Items(
     *                                          type="object",
     *                                          @OA\Property(property="value", description="Measurement value"),
     *                                          @OA\Property(property="unit", type="string", nullable=true),
     *                                          @OA\Property(property="measured_at", type="string", format="date-time"),
     *                                          @OA\Property(property="alarm", type="integer", nullable=true),
     *                                          @OA\Property(property="fault", type="integer", nullable=true)
     *                                      )
     *                                  )
     *                              )
     *                          )
     *                      ),
     *                      @OA\Property(
     *                          property="tic",
     *                          type="array",
     *                          @OA\Items(
     *                              type="object",
     *                              @OA\Property(property="device_identifier", type="string"),
     *                              @OA\Property(
     *                                  property="association_period",
     *                                  type="object",
     *                                  @OA\Property(property="start", type="string", format="date-time"),
     *                                  @OA\Property(property="end", type="string", format="date-time", nullable=true)
     *                              ),
     *                              @OA\Property(property="gateway", type="string"),
     *                              @OA\Property(
     *                                  property="metadata",
     *                                  type="object",
     *                                  @OA\Property(property="TICID", type="string")
     *                              ),
     *                              @OA\Property(
     *                                  property="telemetry",
     *                                  type="array",
     *                                  description="TIC telemetry data",
     *                                  @OA\Items(type="object")
     *                              )
     *                          )
     *                      )
     *                  )
     *              ),
     *              @OA\Property(
     *                  property="query_info",
     *                  type="object",
     *                  @OA\Property(property="member_id", type="string"),
     *                  @OA\Property(property="tenant_id", type="integer"),
     *                  @OA\Property(property="latest_only", type="boolean", description="Whether only latest data was requested"),
     *                  @OA\Property(
     *                      property="time_range",
     *                      type="object",
     *                      description="Time range (only present when latest_only=false)",
     *                      @OA\Property(property="start", type="string", format="date-time"),
     *                      @OA\Property(property="end", type="string", format="date-time")
     *                  )
     *              )
     *          )
     *      ),
     *      @OA\Response(response=400, description="Bad request"),
     *      @OA\Response(response=403, description="Forbidden"),
     *      @OA\Response(response=500, description="Internal server error")
     *  )
     *
     * Get telemetry data for a member - either within time range or latest values only
     * 
     * @param int $tenantId
     * @param Request $request
     * @param TenantPolicyManagerInterface $tenantPolicyManager
     * @return \Illuminate\Http\JsonResponse
     * @throws AuthorizationException
     */
    public function getMemberTelemetryData(int $tenantId, Request $request, TenantPolicyManagerInterface $tenantPolicyManager)
    {
        $this->authorize('viewAnyOfTenant', [Member::class, $tenantId]);
        
        $latestOnly = $request->boolean('latest_only', false);
        
        // Conditional validation based on latest_only parameter
        if ($latestOnly) {
            $request->validate([
                'member_id' => 'required|string',
                // 'latest_only' => 'boolean'
            ]);
            
            // For latest data, use current time as reference
            $startTime = null;
            $endTime = null;
        } else {
            $request->validate([
                'member_id' => 'required|string',
                'start_time' => 'required|date',
                'end_time' => 'required|date|after:start_time',
                // 'latest_only' => 'boolean'
            ]);
            
            // Convert input time from China timezone to UTC for database query
            $startTime = $this->convertChineseToUtcTime($request->get('start_time'));
            $endTime = $this->convertChineseToUtcTime($request->get('end_time'));
        }
        
        $memberId = $request->get('member_id');
        
        // Get policy-adjusted tenant ID for filtering
        $account = $request->user();
        $policyAlteredTenantId = $tenantPolicyManager->getAlteredTenantId('member', 'view', $tenantId);
        $includeSubTenants = $request->get('include_sub_tenants', $account->isTenantAdmin());
        
        try {
            $data = $this->memberDataRepository->getMemberTelemetryData(
                $memberId, 
                $startTime, 
                $endTime, 
                $policyAlteredTenantId, 
                $includeSubTenants,
                $latestOnly
            );
            
            $queryInfo = [
                'member_id' => $memberId,
                'tenant_id' => $tenantId,
                'latest_only' => $latestOnly
            ];
            
            if (!$latestOnly) {
                $queryInfo['time_range'] = [
                    'start' => $startTime->toISOString(),
                    'end' => $endTime->toISOString()
                ];
            }
            
            return response()->json([
                'success' => true,
                'data' => $data,
                'query_info' => $queryInfo
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving member data: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * @OA\Get(
     *      path="/system/tenant/{tenantId}/member-data/all-telemetry",
     *      operationId="MemberDataController::getAllMembersTelemetryData",
     *      tags={"System.MemberData"},
     *      summary="Get telemetry data for all members in a tenant",
     *      description="Returns telemetry data for all members in a tenant. Can return data within specified time range or just the latest values for each field when latest_only=true. Includes each member's own data and associated device data.",
     *      security={{"api_http_auth": {}}},
     *      @OA\Parameter(
     *          name="tenantId",
     *          description="Tenant ID",
     *          required=true,
     *          in="path",
     *          @OA\Schema(type="integer")
     *      ),
     *      @OA\Parameter(
     *          name="start_time",
     *          description="Start time for data query (ISO 8601 format). Required when latest_only=false",
     *          required=false,
     *          in="query",
     *          @OA\Schema(type="string", format="date-time", example="2025-08-04T17:35:37")
     *      ),
     *      @OA\Parameter(
     *          name="end_time",
     *          description="End time for data query (ISO 8601 format). Required when latest_only=false",
     *          required=false,
     *          in="query",
     *          @OA\Schema(type="string", format="date-time", example="2025-08-20T15:12:30")
     *      ),
     *      @OA\Parameter(
     *          name="latest_only",
     *          description="Whether to retrieve only the latest data for each field (ignores time range if true)",
     *          required=false,
     *          in="query",
     *          @OA\Schema(type="boolean", default=false)
     *      ),
     *      @OA\Parameter(
     *          name="include_sub_tenants",
     *          description="Whether to include sub-tenants in query",
     *          required=false,
     *          in="query",
     *          @OA\Schema(type="boolean", default=false)
     *      ),
     *      @OA\Parameter(
     *          name="member_ids",
     *          description="Member IDs (database id field) to filter results. Can be comma-separated string. If not provided, returns data for all members in tenant.",
     *          required=false,
     *          in="query",
     *          @OA\Schema(
     *              oneOf={
     *                  @OA\Schema(type="string", example="1,2,3")
     *              }
     *          )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="Successful operation",
     *          @OA\JsonContent(
     *              type="object",
     *              required={"success", "data", "query_info"},
     *              @OA\Property(property="success", type="boolean", example=true),
     *              @OA\Property(
     *                  property="data",
     *                  type="object",
     *                  @OA\Property(property="members_count", type="integer", description="Number of members with data in time range"),
     *                  @OA\Property(property="total_members_in_tenant", type="integer", description="Total number of members in tenant"),
     *                  @OA\Property(
     *                      property="time_range",
     *                      type="object",
     *                      @OA\Property(property="start", type="string", format="date-time"),
     *                      @OA\Property(property="end", type="string", format="date-time")
     *                  ),
     *                  @OA\Property(
     *                      property="members",
     *                      type="array",
     *                      @OA\Items(
     *                          type="object",
     *                          @OA\Property(
     *                              property="member_info",
     *                              type="object",
     *                              @OA\Property(property="id", type="integer"),
     *                              @OA\Property(property="member_id", type="string"),
     *                              @OA\Property(property="name", type="string"),
     *                              @OA\Property(property="tenant_id", type="integer"),
     *                              @OA\Property(property="team_id", type="integer", description="Team ID that the member belongs to")
     *                          ),
     *                          @OA\Property(
     *                              property="member_data",
     *                              type="object",
     *                              description="Member's own telemetry data with measurements over time",
     *                              @OA\AdditionalProperties(
     *                                  type="array",
     *                                  @OA\Items(
     *                                      type="object",
     *                                      @OA\Property(property="value", description="Measurement value (string, number, or mixed)"),
     *                                      @OA\Property(property="unit", type="string", nullable=true, description="Unit of measurement"),
     *                                      @OA\Property(property="measured_at", type="string", format="date-time", description="Timestamp when measurement was taken"),
     *                                      @OA\Property(property="alarm", type="integer", nullable=true, description="Alarm status (0=normal, 1=warning, 2=critical)"),
     *                                      @OA\Property(property="fault", type="integer", nullable=true, description="Fault status")
     *                                  ),
     *                                  description="Array of time-series data points for each telemetry field (e.g., Longitude, Latitude, UserName, etc.)"
     *                              )
     *                          ),
     *                          @OA\Property(
     *                              property="device_data",
     *                              type="object",
     *                              description="Associated devices' telemetry data grouped by device type",
     *                              @OA\Property(
     *                                  property="wristband",
     *                                  type="array",
     *                                  @OA\Items(ref="#/components/schemas/DeviceWristbandData")
     *                              ),
     *                              @OA\Property(
     *                                  property="pressure_gauge",
     *                                  type="array",
     *                                  @OA\Items(ref="#/components/schemas/DevicePressureGaugeData")
     *                              ),
     *                              @OA\Property(
     *                                  property="gas_detector",
     *                                  type="array",
     *                                  @OA\Items(ref="#/components/schemas/DeviceGasDetectorData")
     *                              ),
     *                              @OA\Property(
     *                                  property="tic",
     *                                  type="array",
     *                                  @OA\Items(ref="#/components/schemas/DeviceTicData")
     *                              )
     *                          )
     *                      )
     *                  )
     *              ),
     *              @OA\Property(
     *                  property="query_info",
     *                  type="object",
     *                  @OA\Property(property="query_type", type="string", example="all_members", description="'all_members' or 'specific_members'"),
     *                  @OA\Property(property="tenant_id", type="integer"),
     *                  @OA\Property(
     *                      property="member_ids",
     *                      type="array",
     *                      @OA\Items(type="integer"),
     *                      nullable=true,
     *                      description="Array of member IDs used in filter (null if all members)"
     *                  ),
     *                  @OA\Property(property="latest_only", type="boolean", description="Whether only latest data was requested"),
     *                  @OA\Property(
     *                      property="time_range",
     *                      type="object",
     *                      description="Time range (only present when latest_only=false)",
     *                      @OA\Property(property="start", type="string", format="date-time"),
     *                      @OA\Property(property="end", type="string", format="date-time")
     *                  )
     *              )
     *          )
     *      ),
     *      @OA\Response(response=400, description="Bad request"),
     *      @OA\Response(response=403, description="Forbidden"),
     *      @OA\Response(response=500, description="Internal server error")
     *  )
     *
     * Get telemetry data for all members in a tenant - either within time range or latest values only
     * 
     * @param int $tenantId
     * @param Request $request
     * @param TenantPolicyManagerInterface $tenantPolicyManager
     * @return \Illuminate\Http\JsonResponse
     * @throws AuthorizationException
     */
    public function getAllMembersTelemetryData(int $tenantId, Request $request, TenantPolicyManagerInterface $tenantPolicyManager)
    {
        $this->authorize('viewAnyOfTenant', [Member::class, $tenantId]);
        
        $latestOnly = $request->boolean('latest_only', false);
        
        // Conditional validation based on latest_only parameter
        if ($latestOnly) {
            $request->validate([
                'member_ids' => 'nullable|string',
                'member_ids.*' => 'integer',
                // 'latest_only' => 'boolean'
            ]);
            
            // For latest data, use current time as reference
            $startTime = null;
            $endTime = null;
        } else {
            $request->validate([
                'start_time' => 'required|date',
                'end_time' => 'required|date|after:start_time',
                'member_ids' => 'nullable|string',
                'member_ids.*' => 'integer',
                // 'latest_only' => 'boolean'
            ]);
            
            // Convert input time from China timezone to UTC for database query
            $startTime = $this->convertChineseToUtcTime($request->get('start_time'));
            $endTime = $this->convertChineseToUtcTime($request->get('end_time'));
        }
        
        $memberIds = $request->get('member_ids');
        
        // Convert comma-separated string to array if needed
        if (is_string($memberIds)) {
            $memberIds = array_map('intval', array_filter(explode(',', $memberIds)));
        }
        
        // Get policy-adjusted tenant ID for filtering
        $account = $request->user();
        $policyAlteredTenantId = $tenantPolicyManager->getAlteredTenantId('member', 'view', $tenantId);
        $includeSubTenants = $request->get('include_sub_tenants', $account->isTenantAdmin());
        
        try {
            $data = $this->memberDataRepository->getAllMembersTelemetryData(
                $startTime, 
                $endTime, 
                $policyAlteredTenantId, 
                $includeSubTenants,
                $memberIds,
                $latestOnly
            );
            
            $queryInfo = [
                'query_type' => $memberIds ? 'specific_members' : 'all_members',
                'tenant_id' => $tenantId,
                'member_ids' => $memberIds,
                'latest_only' => $latestOnly
            ];
            
            if (!$latestOnly) {
                $queryInfo['time_range'] = [
                    'start' => $startTime->toISOString(),
                    'end' => $endTime->toISOString()
                ];
            }
            
            return response()->json([
                'success' => true,
                'data' => $data,
                'query_info' => $queryInfo
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving all members data: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * @OA\Get(
     *      path="/system/tenant/{tenantId}/member-data/active-devices",
     *      operationId="MemberDataController::getMemberActiveDevices",
     *      tags={"System.MemberData"},
     *      summary="Get member's currently active devices",
     *      description="Returns list of devices currently associated with a specific member",
     *      security={{"api_http_auth": {}}},
     *      @OA\Parameter(
     *          name="tenantId",
     *          description="Tenant ID",
     *          required=true,
     *          in="path",
     *          @OA\Schema(type="integer")
     *      ),
     *      @OA\Parameter(
     *          name="member_id",
     *          description="Member ID (card_id)",
     *          required=true,
     *          in="query",
     *          @OA\Schema(type="string")
     *      ),
     *      @OA\Parameter(
     *          name="include_sub_tenants",
     *          description="Whether to include sub-tenants in query",
     *          required=false,
     *          in="query",
     *          @OA\Schema(type="boolean", default=false)
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="Successful operation",
     *          @OA\JsonContent(
     *              type="object",
     *              required={"success", "data"},
     *              @OA\Property(property="success", type="boolean", example=true),
     *              @OA\Property(
     *                  property="data",
     *                  type="object",
     *                  @OA\Property(property="member_id", type="string"),
     *                  @OA\Property(property="tenant_id", type="integer"),
     *                  @OA\Property(property="active_devices", type="object", description="Active devices grouped by device type")
     *              )
     *          )
     *      ),
     *      @OA\Response(response=400, description="Bad request"),
     *      @OA\Response(response=403, description="Forbidden"),
     *      @OA\Response(response=500, description="Internal server error")
     *  )
     *
     * Get member's currently active devices
     * 
     * @param int $tenantId
     * @param Request $request
     * @param TenantPolicyManagerInterface $tenantPolicyManager
     * @return \Illuminate\Http\JsonResponse
     * @throws AuthorizationException
     */
    public function getMemberActiveDevices(int $tenantId, Request $request, TenantPolicyManagerInterface $tenantPolicyManager)
    {
        $this->authorize('viewAnyOfTenant', [Member::class, $tenantId]);
        
        $request->validate([
            'member_id' => 'required|string'
        ]);
        
        $memberId = $request->get('member_id');
        
        // Get policy-adjusted tenant ID for filtering
        $account = $request->user();
        $policyAlteredTenantId = $tenantPolicyManager->getAlteredTenantId('member', 'view', $tenantId);
        $includeSubTenants = $request->get('include_sub_tenants', $account->isTenantAdmin());
        
        try {
            $activeDevices = $this->memberDataRepository->getMemberActiveDevices(
                $memberId, 
                $policyAlteredTenantId, 
                $includeSubTenants
            );
            
            return response()->json([
                'success' => true,
                'data' => [
                    'member_id' => $memberId,
                    'tenant_id' => $tenantId,
                    'active_devices' => $activeDevices
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving active devices: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * @OA\Post(
     *      path="/system/tenant/{tenantId}/member-data/process-stream",
     *      operationId="MemberDataController::processDataStream",
     *      tags={"System.MemberData"},
     *      summary="Process incoming data stream",
     *      description="Webhook endpoint for processing incoming member and device telemetry data stream from Go service",
     *      security={{"api_http_auth": {}}},
     *      @OA\Parameter(
     *          name="tenantId",
     *          description="Tenant ID",
     *          required=true,
     *          in="path",
     *          @OA\Schema(type="integer")
     *      ),
     *      @OA\RequestBody(
     *          description="Parsed telemetry data payload",
     *          required=true,
     *          @OA\JsonContent(
     *              type="object",
     *              required={"Parsed"},
     *              @OA\Property(
     *                  property="Parsed",
     *                  type="object",
     *                  required={"TimeSeries"},
     *                  @OA\Property(
     *                      property="TimeSeries",
     *                      type="object",
     *                      @OA\Property(property="Timestamp", type="string", format="date-time"),
     *                      @OA\Property(
     *                          property="Gateway",
     *                          type="object",
     *                          @OA\Property(property="DeviceIdentifier", type="string")
     *                      ),
     *                      @OA\Property(
     *                          property="Members",
     *                          type="array",
     *                          @OA\Items(type="object", description="Member telemetry data")
     *                      ),
     *                      @OA\Property(
     *                          property="Devices",
     *                          type="array",
     *                          @OA\Items(type="object", description="Device telemetry data")
     *                      )
     *                  )
     *              )
     *          )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="Data processed successfully",
     *          @OA\JsonContent(
     *              type="object",
     *              required={"success", "message", "tenant_id"},
     *              @OA\Property(property="success", type="boolean", example=true),
     *              @OA\Property(property="message", type="string", example="Data processed successfully"),
     *              @OA\Property(property="tenant_id", type="integer")
     *          )
     *      ),
     *      @OA\Response(response=400, description="Bad request - Invalid data format"),
     *      @OA\Response(response=403, description="Forbidden"),
     *      @OA\Response(response=500, description="Internal server error")
     *  )
     *
     * Process incoming data stream (webhook endpoint)
     * 
     * @param int $tenantId
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws AuthorizationException
     */
    public function processDataStream(int $tenantId, Request $request)
    {
        // Note: This is a webhook endpoint, typically called by the Go service
        // We still need tenant authorization for security
        $this->authorize('viewAnyOfTenant', [Member::class, $tenantId]);
        
        $parsedData = $request->get('Parsed');
        
        if (!$parsedData || !isset($parsedData['TimeSeries'])) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid data format'
            ], 400);
        }
        
        try {
            $this->memberDataRepository->processDataStream($parsedData, $tenantId);
            
            return response()->json([
                'success' => true,
                'message' => 'Data processed successfully',
                'tenant_id' => $tenantId
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error processing data: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Convert input time from China timezone to UTC for database query
     * 
     * @param string $timeString
     * @return Carbon
     */
    private function convertChineseToUtcTime($timeString)
    {
        // Parse the input time as China timezone and convert to UTC
        return Carbon::parse($timeString, 'Asia/Shanghai')->utc();
    }
}

/**
 * @OA\Schema(
 *     schema="DeviceWristbandData",
 *     type="object",
 *     description="Wristband device data structure",
 *     @OA\Property(property="device_identifier", type="string", description="Device MAC address or unique identifier"),
 *     @OA\Property(
 *         property="association_period",
 *         type="object",
 *         @OA\Property(property="start", type="string", format="date-time", description="When device was associated with member"),
 *         @OA\Property(property="end", type="string", format="date-time", nullable=true, description="When device association ended")
 *     ),
 *     @OA\Property(property="gateway", type="string", description="Gateway device identifier"),
 *     @OA\Property(
 *         property="metadata",
 *         type="object",
 *         description="Device-specific metadata",
 *         @OA\Property(property="WristbandID", type="string")
 *     ),
 *     @OA\Property(
 *         property="telemetry",
 *         type="object",
 *         description="Device telemetry data with time-series measurements",
 *         @OA\AdditionalProperties(
 *             type="array",
 *             @OA\Items(ref="#/components/schemas/TelemetryDataPoint")
 *         )
 *     )
 * )
 * 
 * @OA\Schema(
 *     schema="DevicePressureGaugeData",
 *     type="object",
 *     description="Pressure gauge device data structure",
 *     @OA\Property(property="device_identifier", type="string"),
 *     @OA\Property(
 *         property="association_period",
 *         type="object",
 *         @OA\Property(property="start", type="string", format="date-time"),
 *         @OA\Property(property="end", type="string", format="date-time", nullable=true)
 *     ),
 *     @OA\Property(property="gateway", type="string"),
 *     @OA\Property(
 *         property="metadata",
 *         type="object",
 *         @OA\Property(property="GaugeID", type="string"),
 *         @OA\Property(property="GaugeUnit", type="string"),
 *         @OA\Property(property="LastMaintainDate", type="string", format="date"),
 *         @OA\Property(property="PressureGaugeType", type="integer")
 *     ),
 *     @OA\Property(
 *         property="telemetry",
 *         type="object",
 *         @OA\AdditionalProperties(
 *             type="array",
 *             @OA\Items(ref="#/components/schemas/TelemetryDataPoint")
 *         )
 *     )
 * )
 * 
 * @OA\Schema(
 *     schema="DeviceGasDetectorData",
 *     type="object",
 *     description="Gas detector device data structure",
 *     @OA\Property(property="device_identifier", type="string"),
 *     @OA\Property(
 *         property="association_period",
 *         type="object",
 *         @OA\Property(property="start", type="string", format="date-time"),
 *         @OA\Property(property="end", type="string", format="date-time", nullable=true)
 *     ),
 *     @OA\Property(property="gateway", type="string"),
 *     @OA\Property(
 *         property="metadata",
 *         type="object",
 *         @OA\Property(property="DetectorID", type="string"),
 *         @OA\Property(property="DetectorName", type="string"),
 *         @OA\Property(property="GasCount", type="integer")
 *     ),
 *     @OA\Property(
 *         property="telemetry",
 *         type="object",
 *         @OA\AdditionalProperties(
 *             type="array",
 *             @OA\Items(ref="#/components/schemas/TelemetryDataPoint")
 *         )
 *     )
 * )
 * 
 * @OA\Schema(
 *     schema="DeviceTicData",
 *     type="object",
 *     description="TIC device data structure",
 *     @OA\Property(property="device_identifier", type="string"),
 *     @OA\Property(
 *         property="association_period",
 *         type="object",
 *         @OA\Property(property="start", type="string", format="date-time"),
 *         @OA\Property(property="end", type="string", format="date-time", nullable=true)
 *     ),
 *     @OA\Property(property="gateway", type="string"),
 *     @OA\Property(
 *         property="metadata",
 *         type="object",
 *         @OA\Property(property="TICID", type="string")
 *     ),
 *     @OA\Property(
 *         property="telemetry",
 *         type="array",
 *         description="TIC telemetry data",
 *         @OA\Items(type="object")
 *     )
 * )
 * 
 * @OA\Schema(
 *     schema="TelemetryDataPoint",
 *     type="object",
 *     description="Single telemetry measurement data point",
 *     @OA\Property(property="value", description="Measurement value (string, number, or mixed)"),
 *     @OA\Property(property="unit", type="string", nullable=true, description="Unit of measurement"),
 *     @OA\Property(property="measured_at", type="string", format="date-time", description="Timestamp when measurement was taken"),
 *     @OA\Property(property="alarm", type="integer", nullable=true, description="Alarm status (0=normal, 1=warning, 2=critical)"),
 *     @OA\Property(property="fault", type="integer", nullable=true, description="Fault status")
 * )
 */