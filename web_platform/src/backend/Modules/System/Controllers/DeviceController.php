<?php

namespace Modules\System\Controllers;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Str;
use Modules\System\Models\Tenant;
use Modules\System\QueryBuilders\DeviceQueryBuilder;
use Modules\System\Requests\Device\DeviceCheckRequest;
use Modules\System\Requests\Device\DeviceCommandRequest;
use Modules\System\Requests\Device\DeviceOverviewRequest;
use Modules\System\Requests\Device\DeviceRemoveTenantAdminRequestBody;
use Modules\System\Requests\Device\DeviceAssignTenantAdminRequest;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Modules\System\Repositories\DeviceTelemetryFieldRepository;
use Modules\System\Requests\Device\DeviceTelemetryValueRequest;
use Modules\System\Extensions\DeviceMoldDrivers\DeviceMoldDriverManager;
use Modules\System\Requests\Device\DeviceActionRequest;
use Modules\System\Requests\Device\DeviceUpdatePropertyFieldValueRequest;
use Illuminate\Auth\Access\AuthorizationException;
use Modules\System\Contracts\TenantPolicyManagerInterface;
use Scaffold\QueryBuilder\QueryBuilder;
use Scaffold\BaseController;
use Modules\System\Requests\Device\DeviceUpdateRequest;
use Modules\System\Requests\Device\DeviceListRequest;
use Modules\System\Requests\Device\DeviceGetRequest;
use Modules\System\Requests\Device\DeviceDeleteRequest;
use Modules\System\Repositories\DeviceRepository;
use Modules\System\Resources\DeviceResource;
use Modules\System\Resources\DeviceCollection;
use Modules\System\Models\Device;
use Illuminate\Http\JsonResponse;
use Modules\System\Imports\DevicesImport;
use Maatwebsite\Excel\Facades\Excel;
use Modules\System\Requests\Device\DeviceImportRequest;
use Modules\System\Models\DeviceOverview;
use Modules\System\Services\DeviceDataRouterCacheService;
use Modules\System\Events\TenantDeviceTelemetryDataUpdated;

class DeviceController extends BaseController
{
    private DeviceDataRouterCacheService $cacheService;
    
    public function __construct(DeviceRepository $repository, DeviceDataRouterCacheService $cacheService)
    {
        $this->repository = $repository;
        $this->cacheService = $cacheService;
    }

    /**
     * @OA\Get(
     *      path="/system/device",
     *      operationId="DeviceController::index",
     *      tags={"System.Device"},
     *      summary="Get device list based on current user tenant resource policy.",
     *      description="Get device list based on current user tenant resource policy.<br>Super admin & Platform admins will load all devices. <br>
    Tenant admin will load all devices based on the policy setting 'scaffold.tenant_admin_to_tenant_resource_policy.device.view':<br>
    <b>self</b>: Load devices from the tenant of current user. No cascade.<br>
    <b>cascade</b>:  Load devices from the tenant and sub-tenants of current user.<br>
    <b>top-cascade</b>: Load devices from the top tenant and sub-tenants of current user.",
     *      security={{"api_http_auth": {}}},
     *      @OA\Parameter(name="is_gateway", description="is gateway",  in="query",
     *          @OA\Schema(type="boolean")
     *      ),
     *      @OA\Parameter(name="status", description="device online status",  in="query",
     *          @OA\Schema(type="integer")
     *      ),
     *      @OA\Parameter(ref="#/components/parameters/IndexQuery"),
     *      @OA\Parameter(ref="#/components/parameters/_includeSubTenants"),
     *      @OA\Response(response=200, description="successful operation",
     *          @OA\JsonContent(type="object", description="Response of an index API.",
     *              required={"links","meta","data"},
     *              @OA\Property(property="links", ref="#/components/schemas/IndexResponseLinks"),
     *              @OA\Property(property="meta", ref="#/components/schemas/IndexResponseMeta"),
     *              @OA\Property(property="data", type="array", description="Data array.",
     *                   @OA\Items(ref="#/components/schemas/System.DeviceResource")
     *              ),
     *          ),
     *      ),
     *      @OA\Response(response=400, description="Bad request")
     *  )
     *
     * @param DeviceListRequest $request
     * @param TenantPolicyManagerInterface $tenantPolicyManager
     * @return DeviceCollection
     * @throws AuthorizationException
     */
    public function index(DeviceListRequest $request, TenantPolicyManagerInterface $tenantPolicyManager)
    {
        $this->authorize('viewAny', Device::class);
        $account = $request->user();
        $is_gateway = $request->boolean('is_gateway', false);
        $status = $request->input('status', false);
        if ($account->isTenantAdmin()) {
            $policyAlteredTenantId = $tenantPolicyManager->getAlteredTenantId('device', 'view', $account->accountable->tenant_id);
            $includeSubTenants = $request->includeSubTenants(true);
            $query = Device::query()->tenantId($policyAlteredTenantId, $includeSubTenants);
        } else {
            $query = Device::query();
        }

        if ($is_gateway) {
            $query = $query->where('is_gateway', $is_gateway)->where(function (Builder $builder) use ($status, $is_gateway) {
                if ($status !== false && intval($status) === 0) {
                    $builder->doesntHave('subOnlineDevices');
                } elseif ($status !== false && intval($status) === 1) {
                    $builder->has("subOnlineDevices");
                }
            });
        } else {
            $query = $query->where('is_gateway', $is_gateway)->where(function (Builder $builder) use ($status) {
                if ($status !== false) {
                    $builder->where('status', intval($status));
                }
            });
        }
        $query = DeviceQueryBuilder::for($query)->getQuery();
        $paginated = DeviceQueryBuilder::paginate($query);
        $data = new DeviceCollection($paginated);
        return $data;
    }

    /**
     * for PCM system calls
     */
    public function indexForOverview(DeviceListRequest $request, TenantPolicyManagerInterface $tenantPolicyManager)
    {
        $this->authorize('viewAny', Device::class);
        $account = $request->user();
        $is_gateway = $request->boolean('is_gateway', false);
        $status = $request->input('status', false);
        if ($account->isTenantAdmin()) {
            $policyAlteredTenantId = $tenantPolicyManager->getAlteredTenantId('device', 'view', $account->accountable->tenant_id);
            $includeSubTenants = $request->includeSubTenants(true);
            $query = DeviceOverview::query()->tenantId($policyAlteredTenantId, $includeSubTenants);
        } else {
            $query = DeviceOverview::query();
        }

        if ($is_gateway) {
            $query = $query->where('is_gateway', $is_gateway)->where(function (Builder $builder) use ($status, $is_gateway) {
                if ($status !== false && intval($status) === 0) {
                    $builder->doesntHave('subOnlineDevices');
                } elseif ($status !== false && intval($status) === 1) {
                    $builder->has("subOnlineDevices");
                }
            });
        } else {
            $query = $query->where('is_gateway', $is_gateway)->where(function (Builder $builder) use ($status) {
                if ($status !== false) {
                    $builder->where('status', intval($status));
                }
            });
        }
        $query = QueryBuilder::for($query)->getQuery();
        $paginated = QueryBuilder::paginate($query);
        $data = new DeviceCollection($paginated);
        return $data;
    }

    /**
     * @OA\Get(
     *      path="/system/device/{id}",
     *      operationId="DeviceController::show",
     *      tags={"System.Device"},
     *      summary="Get one Device",
     *      description="Get one Device by id<br>Super admin & Platform admins shall pass.<br>
    Tenant admin permission is based on the policy setting 'scaffold.tenant_admin_to_tenant_resource_policy.device.view':<br>
    <b>self</b>: View a device from the tenant of current user. No cascade.<br>
    <b>cascade</b>:  View a device from the tenant and sub-tenants of current user.<br>
    <b>top-cascade</b>: View a device from the top tenant and sub-tenants of current user.<br><br>
    <b>Real-time Data:</b> When include_latest_data=true, the device will include a latest_data field containing real-time telemetry, online status, and gateway/member relationships from the device-data-router cache.",
     *      security={{"api_http_auth": {}}},
     *      @OA\Parameter(
     *          name="id",
     *          description="Device id",
     *          required=true,
     *          in="path",
     *          @OA\Schema(
     *              type="integer"
     *          )
     *      ),
     *      @OA\Parameter(ref="#/components/parameters/with"),
     *      @OA\Parameter(name="include_latest_data", description="Include real-time device data from cache", in="query",
     *          @OA\Schema(type="boolean", default=false)
     *      ),
     *      @OA\Response(response=200, description="successful operation",
     *          @OA\JsonContent(type="object", description="Fetched record.",
     *              required={"data"},
     *              @OA\Property(property="data", ref="#/components/schemas/System.DeviceResource")
     *          ),
     *      ),
     *      @OA\Response(response=400, description="Bad request")
     *  )
     *
     * @param int $id
     *
     * @param DeviceGetRequest $request
     * @return JsonResponse|DeviceResource
     * @throws AuthorizationException
     */
    public function show($id, DeviceGetRequest $request, DeviceDataRouterCacheService $cacheService)
    {
        $item = $this->repository->findWithoutFail($id);
        if (empty($item)) {
            return $this->sendError('Device not found');
        }
        $this->authorize('view', $item);
        
        // Always load deviceMold for streaming URL generation
        $withRelations = $request->getWith();
        if (!in_array('deviceMold', $withRelations)) {
            $withRelations[] = 'deviceMold';
        }
        $item->load($withRelations);
        
        // Check if latest data is requested
        $includeLatestData = $request->boolean('include_latest_data', false);
        // dd($includeLatestData, $item->identifier);
        if ($includeLatestData && $item->identifier) {
            // Get latest data from cache
            $latestData = $cacheService->getDeviceLatestData($item->identifier);
            if ($latestData) {
                $item->latest_data = $latestData;
            }
        }
        
        return new DeviceResource($item);
    }

    /**
     * @OA\Get(
     *      path="/system/device/identifier/{identifier}",
     *      operationId="DeviceController::showByIdentifier",
     *      tags={"System.Device"},
     *      summary="Get one Device by identifier",
     *      description="Get one Device by identifier<br>Super admin & Platform admins shall pass.<br>
    Tenant admin permission is based on the policy setting 'scaffold.tenant_admin_to_tenant_resource_policy.device.view':<br>
    <b>self</b>: View a device from the tenant of current user. No cascade.<br>
    <b>cascade</b>:  View a device from the tenant and sub-tenants of current user.<br>
    <b>top-cascade</b>: View a device from the top tenant and sub-tenants of current user.<br><br>
    <b>Real-time Data:</b> When include_latest_data=true, the device will include a latest_data field containing real-time telemetry, online status, and gateway/member relationships from the device-data-router cache.",
     *      security={{"api_http_auth": {}}},
     *      @OA\Parameter(
     *          name="identifier",
     *          description="Device identifier",
     *          required=true,
     *          in="path",
     *          @OA\Schema(
     *              type="string"
     *          )
     *      ),
     *      @OA\Parameter(ref="#/components/parameters/with"),
     *      @OA\Parameter(name="include_latest_data", description="Include real-time device data from cache", in="query",
     *          @OA\Schema(type="boolean", default=false)
     *      ),
     *      @OA\Response(response=200, description="successful operation",
     *          @OA\JsonContent(type="object", description="Fetched record.",
     *              required={"data"},
     *              @OA\Property(property="data", ref="#/components/schemas/System.DeviceResource")
     *          ),
     *      ),
     *      @OA\Response(response=400, description="Bad request")
     *  )
     *
     * @param string $identifier
     * @param DeviceGetRequest $request
     * @param DeviceDataRouterCacheService $cacheService
     * @return JsonResponse|DeviceResource
     * @throws AuthorizationException
     */
    public function showByIdentifier($identifier, DeviceGetRequest $request, DeviceDataRouterCacheService $cacheService)
    {
        $item = $this->repository->getDeviceWithIdentifier($identifier);
        if (empty($item)) {
            return $this->sendError('Device not found');
        }
        $this->authorize('view', $item);
        
        // Always load deviceMold for streaming URL generation
        $withRelations = $request->getWith();
        if (!in_array('deviceMold', $withRelations)) {
            $withRelations[] = 'deviceMold';
        }
        $item->load($withRelations);
        
        // Check if latest data is requested
        $includeLatestData = $request->boolean('include_latest_data', false);
        if ($includeLatestData && $item->identifier) {
            // Get latest data from cache
            $latestData = $cacheService->getDeviceLatestData($item->identifier);
            if ($latestData) {
                $item->latest_data = $latestData;
            }
        }
        
        return new DeviceResource($item);
    }

    /**
     * @OA\Put(
     *      path="/system/device/{id}",
     *      operationId="DeviceController::update",
     *      tags={"System.Device"},
     *      summary="Update one Device",
     *      description="Update one Device by id<br> 'device_mold_id', 'tenant_id' can not be updated.<br>
    Super admin shall pass.<br>Platform admins shall NOT.<br>
    Tenant admin with permission 'taManageDevice' is validated based on the policy setting 'scaffold.tenant_admin_to_tenant_resource_policy.device.manage'<br>
    <b>self</b>: Able to update devices belong to the tenant of current user.<br>
    <b>cascade</b>: Able to update devices belong to the tenant and sub-tenants of current user.",
     *      security={{"api_http_auth": {}}},
     *      @OA\Parameter(
     *          name="id",
     *          description="Device id",
     *          required=true,
     *          in="path",
     *          @OA\Schema(
     *              type="integer"
     *          )
     *      ),
     *      @OA\RequestBody(
     *          description="Payload to update Device",
     *          required=true,
     *          @OA\JsonContent(ref="#/components/schemas/System.Device")
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation"
     *      ),
     *      @OA\Response(response=400, description="Bad request")
     *  )
     *
     * @param int $id
     * @param DeviceUpdateRequest $request
     *
     * @return JsonResponse
     * @throws AuthorizationException
     */
    public function update($id, DeviceUpdateRequest $request)
    {
        /**
         * @var Device $item
         */
        $item = $this->repository->findWithoutFail($id);
        if (empty($item)) {
            return $this->sendError('Device not found');
        }
        $this->authorize('update', $item);
        $parameters = $request->except('tenant_id');
        $this->repository->update($parameters, $id);
        
        // Update device cache in device-data-router
        $updatedItem = $this->repository->findWithoutFail($id);
        if ($updatedItem) {
            $this->cacheService->cacheDeviceInfo($updatedItem);
        }

        activity_log()->withObject($item)->add("修改设备 {$item->name} ({$item->id})", $parameters);
        return $this->sendResponse(null, 'Device updated successfully.');
    }

    /**
     * @OA\Delete(
     *      path="/system/device/{id}",
     *      operationId="DeviceController::destroy",
     *      tags={"System.Device"},
     *      summary="Delete one Device",
     *      description="Delete one Device by id<br>Super admin shall pass.<br>Platform admins shall NOT.<br>
    Tenant admin with permission 'taManageDevice' is validated based on the policy setting 'scaffold.tenant_admin_to_tenant_resource_policy.device.manage'<br>
    <b>self</b>: Able to delete devices belong to the tenant of current user.<br>
    <b>cascade</b>: Able to delete devices belong to the tenant and sub-tenants of current user.",
     *      security={{"api_http_auth": {}}},
     *      @OA\Parameter(
     *          name="id",
     *          description="Device id",
     *          required=true,
     *          in="path",
     *          @OA\Schema(
     *              type="integer"
     *          )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation"
     *      ),
     *      @OA\Response(response=400, description="Bad request"),
     *      @OA\Response(response=403, description="Sub devices exist."),
     *  )
     *
     * @param int $id
     *
     * @param DeviceDeleteRequest $request
     * @return JsonResponse
     * @throws AuthorizationException
     */
    public function destroy($id, DeviceDeleteRequest $request)
    {
        $item = $this->repository->findWithoutFail($id);
        if (empty($item)) {
            return $this->sendError('Device not found');
        }
        $this->authorize('delete', $item);
        
        // Store identifier for cache invalidation before deletion
        $deviceIdentifier = $item->identifier;
        
        $device = $this->repository->delete($id);
        if ($device instanceof \Exception) {
            return $this->sendError($device->getMessage());
        }
        
        // Invalidate device cache in device-data-router
        $this->cacheService->invalidateDeviceCache($deviceIdentifier);

        activity_log()->withObject($item)->add("删除设备 {$item->name} ({$item->id})");
        return $this->sendResponse(null, 'Device deleted successfully.');
    }

    /**
     * @OA\Put(
     *      path="/system/device/{id}/property_field/{fieldId}",
     *      operationId="DeviceController::updatePropertyFieldValue",
     *      tags={"System.Device"},
     *      summary="Update a property value by given device ID and property field ID.",
     *      description="Super admin shall pass.<br>Platform admins shall NOT.<br>
    Tenant admin with permission 'taManageDevice' is validated based on the policy setting 'scaffold.tenant_admin_to_tenant_resource_policy.device.manage'<br>
    <b>self</b>: Able to update devices belong to the tenant of current user.<br>
    <b>cascade</b>: Able to update devices belong to the tenant and sub-tenants of current user.",
     *      security={{"api_http_auth": {}}},
     *      @OA\Parameter(name="id", description="id", required=true, in="path",
     *          @OA\Schema(type="integer")
     *      ),
     *      @OA\Parameter(name="fieldId", description="fieldId", required=true, in="path",
     *          @OA\Schema(type="integer")
     *      ),
     *      @OA\RequestBody(ref="#/components/requestBodies/System.DeviceUpdatePropertyFieldValueRequestBody"),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation"
     *      ),
     *      @OA\Response(response=400, description="Bad request")
     *  )
     *
     * @param int $id
     * @param int $fieldId
     * @param DeviceUpdatePropertyFieldValueRequest $request
     * @return JsonResponse
     * @throws AuthorizationException
     */
    public function updatePropertyFieldValue($id, $fieldId, DeviceUpdatePropertyFieldValueRequest $request)
    {
        /**
         * @var Device $device
         */
        $device = $this->repository->findWithoutFail($id);
        if (empty($device)) {
            return $this->sendError('Device not found');
        }
        /**
         * @var DevicePropertyField $propertyField
         */
        $propertyField = $device->devicePropertyFields()->find($fieldId);
        if (empty($propertyField)) {
            return $this->sendError('Property field not found');
        }
        $this->authorize('updatePropertyFieldValueByDeviceAndField', [
            $device,
            $propertyField,
        ]);
        $propertyValue = $request->input('property_value');
        $this->repository->updatePropertyFieldValue($device, $propertyField, $propertyValue);
        
        // Update device cache in device-data-router
        $this->cacheService->cacheDeviceInfo($device);

        activity_log()->withObject($propertyField)->add("修改设备属性值 {$device->name} ({$device->id}) 字段: {$propertyField->id}", ['property_value' => $propertyValue]);
        return $this->sendResponse(null, 'propertyField processed successfully.');
    }

    /**
     * @OA\Put(
     *      path="/system/device/{deviceId}/action/{actionId}",
     *      operationId="DeviceController::action",
     *      tags={"System.Device"},
     *      summary="Request an action to a device.",
     *      description="Request an action to a device.<br>super admin shall pass.<br>Platform admins shall NOT.<br>
    Tenant admin with permission 'taManageDevice' is validated based on the policy setting 'scaffold.tenant_admin_to_tenant_resource_policy.device.manage'<br>
    <b>self</b>: Able to update devices belong to the tenant of current user.<br>
    <b>cascade</b>: Able to update devices belong to the tenant and sub-tenants of current user.<br>
    If the Device $instance is public, then tenant admins of the space of the device shall pass<br>
    If the Device $instance is NOT public, then tenant admins of the device with can_use==true shall pass<br>",
     *      security={{"api_http_auth": {}}},
     *      @OA\Parameter(name="deviceId", description="Device Id", required=true, in="path",
     *          @OA\Schema(type="integer")
     *      ),
     *      @OA\Parameter(name="actionId", description="Action Id", required=true, in="path",
     *          @OA\Schema(type="integer")
     *      ),
     *      @OA\RequestBody(ref="#/components/requestBodies/System.CallDeviceActionRequestBody"),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation"
     *      ),
     *      @OA\Response(response=400, description="Bad request")
     *  )
     *
     * @param int $deviceId
     * @param int $actionId
     * @param DeviceActionRequest $request
     * @return JsonResponse
     * @throws AuthorizationException
     * @throws BindingResolutionException
     */
    public function action($deviceId, $actionId, DeviceActionRequest $request)
    {
        /**
         * @var $device Device
         */
        $device = $this->repository->findWithoutFail($deviceId);
        if (empty($device)) {
            return $this->sendError('Device not found');
        }
        $this->authorize('action', $device);

        $parameters = $request->all();
        $actionResponse = $this->repository->action($device, $actionId, $parameters);

        activity_log()->withObject($device)->add("执行设备指令 {$device->name} ({$device->id}) 指令: {$actionId}", $parameters);
        return $this->sendResponse($actionResponse, 'action processed successfully.');
    }

    /**
     * @OA\Get(
     *      path="/system/device/{id}/telemetry_value",
     *      operationId="DeviceController::telemetryValue",
     *      tags={"System.Device"},
     *      summary="Retrieve telemetry values from a device",
     *      description="Retrieve telemetry values from a device<br>super admin shall pass.<br>Platform admins shall NOT.<br>
    Tenant admin with permission 'taManageDevice' is validated based on the policy setting 'scaffold.tenant_admin_to_tenant_resource_policy.device.manage'<br>
    <b>self</b>: Able to update devices belong to the tenant of current user.<br>
    <b>cascade</b>: Able to update devices belong to the tenant and sub-tenants of current user.",
     *      security={{"api_http_auth": {}}},
     *      @OA\Parameter(name="id", description="Device ID", required=true, in="path",
     *          @OA\Schema(type="integer")
     *      ),
     *      @OA\Parameter(ref="#/components/parameters/System.DeviceTelemetryValueRequestBody"),
     *      @OA\Response(response=200, description="successful operation",
     *          @OA\JsonContent(type="object", description="Response of an index API.",
     *              required={"data"},
     *              @OA\Property(property="data", type="object", description="Data hashmap.",
     *                  @OA\AdditionalProperties(
     *                      type="array",
     *                      @OA\Items(ref="#/components/schemas/System.TelemetryValueResource")
     *                  ),
     *              ),
     *          ),
     *      ),
     *      @OA\Response(response=400, description="Bad request")
     *  )
     *
     * @param int $id
     * @param DeviceTelemetryValueRequest $request
     * @return JsonResponse|AnonymousResourceCollection
     * @throws AuthorizationException
     */
    public function telemetryValue($id, DeviceTelemetryValueRequest $request)
    {
        /**
         * @var Device $item
         */
        $item = $this->repository->findWithoutFail($id);
        if (empty($item)) {
            return $this->sendError('Device not found');
        }
        $this->authorize('telemetryValue', $item);

        $telemetryFields = DeviceTelemetryFieldRepository::LoadRegularFields(
            $item->device_mold_id, QueryBuilder::ArrayifyBySeparator($request->get('fields'))
        );
        $startDate = $request->get('start');
        $endDate = $request->get('end');
        $startDate = Str::contains($startDate, '+08') ? $startDate : $startDate . '+0800';
        $endDate = Str::contains($endDate, '+08') ? $endDate : $endDate . '+0800';
        $counts = max(0, intval($request->get('counts')));
//         $data = $this->repository->telemetryValues($item, $telemetryFields, $startDate, $endDate);
        $data = $this->repository->telemetryValuesByCodes($id, $telemetryFields, $startDate, $endDate, $counts);
        return $this->sendResponse($data, 'ok');
    }

    /**
     * @OA\Put(
     *      path="/system/device/{id}/assign_tenant_admin",
     *      operationId="DeviceController::assignTenantAdmin",
     *      tags={"System.Device"},
     *      summary="",
     *      description="TODO DeviceController::assignTenantAdmin ",
     *      security={{"api_http_auth": {}}},
     *      @OA\Parameter(name="id", description="Device ID", required=true, in="path",
     *          @OA\Schema(type="integer")
     *      ),
     *      @OA\RequestBody(ref="#/components/requestBodies/System.DeviceAssignTenantAdminRequestBody"),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation"
     *      ),
     *      @OA\Response(response=400, description="Bad request")
     *  )
     *
     * @param int $id
     * @param DeviceAssignTenantAdminRequest $request
     * @return JsonResponse
     * @throws \Illuminate\Auth\Access\AuthorizationException
     */
    public function assignTenantAdmin($id, DeviceAssignTenantAdminRequest $request)
    {
        /**
         * @var $device Device
         */
        $device = $this->repository->findWithoutFail($id);
        if (empty($device)) {
            return $this->sendError('Device not found');
        }
        $this->authorize('assignTenantAdmin', $device);
        $tenantAdminIdArray = $request->getTenantAdminIdArray();
        $canUse = $request->getCanUse();

        $this->repository->assignTenantAdmin($device, $tenantAdminIdArray, $canUse);

        return $this->sendResponse(null, 'Tenant Admin has been assigned to the device.');
    }

    /**
     * @OA\Delete(
     *      path="/system/device/{id}/tenant_admin/{tenantAdminId}",
     *      operationId="DeviceController::removeTenantAdmin",
     *      tags={"System.Device"},
     *      summary="",
     *      description="TODO DeviceController::removeTenantAdmin ",
     *      security={{"api_http_auth": {}}},
     *      @OA\Parameter(name="id", description="Device ID", required=true, in="path",
     *          @OA\Schema(type="integer")
     *      ),
     *      @OA\Parameter(name="tenantAdminId", description="tenantAdminId", required=true, in="path",
     *          @OA\Schema(type="integer")
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation"
     *      ),
     *      @OA\Response(response=400, description="Bad request")
     *  )
     *
     * @param int $id
     * @param int $tenantAdminId
     * @param DeviceRemoveTenantAdminRequestBody $request
     * @return JsonResponse
     * @throws \Illuminate\Auth\Access\AuthorizationException
     */
    public function removeTenantAdmin($id, $tenantAdminId, DeviceRemoveTenantAdminRequestBody $request)
    {
        /**
         * @var $device Device
         */
        $device = $this->repository->findWithoutFail($id);
        if (empty($device)) {
            return $this->sendError('Device not found');
        }
        $this->authorize('removeTenantAdmin', [$device, $tenantAdminId]);

        $this->repository->removeTenantAdmin($device, $tenantAdminId);

        return $this->sendResponse(null, 'Tenant Admin has been removed.');
    }

    /**
     * @OA\Get(
     *      path="/system/device/overview/overview",
     *      operationId="DeviceController::overview",
     *      tags={"System.Device"},
     *      summary="总览",
     *      description="总览",
     *      security={{"api_http_auth": {}}},
     *      @OA\Parameter(name="tenant_id", description="tenant id",  in="query",
     *          @OA\Schema(type="integer")
     *      ),
     *      @OA\Response(response=200, description="successful operation",
     *          @OA\JsonContent(type="object", description="Response of an index API.",
     *              required={"success","message","data"},
     *              @OA\Property(property="success", type="boolean", description="Did the request success or not.",),
     *              @OA\Property(property="message", type="string", description="Response message.",),
     *              @OA\Property(property="data", type="object", description="Data .",
     *                  allOf={@OA\Schema(ref="#/components/schemas/System.DeviceOverviewResponse")},
     *              ),
     *          ),
     *      ),
     *      @OA\Response(response=400, description="Bad request")
     *  )
     *
     * @param DeviceOverviewRequest $request
     * @return JsonResponse
     * @throws \Illuminate\Auth\Access\AuthorizationException
     */
    public function overview(DeviceOverviewRequest $request)
    {
        $user = $request->user();
        $tenant_id = $request->input('tenant_id', null);
        if ($user->isTenantAdmin()) {
            $this->authorize('overview', [Device::class, $tenant_id ? $tenant_id : $user->accountable->tenant_id]);
            $tenant_ids = Tenant::query()->tenantId($tenant_id ? $tenant_id : $user->accountable->tenant_id, true)->pluck('id')->toArray();
        } else {
            $this->authorize('overview', [Device::class, $tenant_id]);
            $tenant_ids = $tenant_id ? Tenant::query()->tenantId($tenant_id ? $tenant_id : $user->accountable->tenant_id, true)->pluck('id')->toArray() : Tenant::query()->pluck('id')->toArray();
        }
        $result = $this->repository->overview($tenant_ids);
        return $this->sendResponse($result, 'overview processed successfully.');
    }

    /**
     * @param $identifier
     * @return JsonResponse
     */
    public function relatedRules($identifier)
    {
        $device = $this->repository->getDeviceWithIdentifier($identifier);
        if (empty($device)) {
            return $this->sendError('Device not found');
        }
        $device->load('watchPoints.strategies.rules.ruleActions');
        $device->load('deviceMold.deviceType');
        $data = [
            'rules' => []
        ];
        $device->watchPoints->each(function ($watchPoint) use (&$data, $device) {
            $watchPoint->strategies->each(function ($strategy) use (&$data, $device, $watchPoint) {
                $strategy->rules->each(function ($rule) use (&$data, $device, $watchPoint) {
                    $actionIds = $rule->ruleActions->pluck('id')->all();
                    $data['rules'][] = [
                        'rule_id' => $rule->id,
                        'action_ids' => $actionIds
                    ];
                });
//                // TODO 需要固定设备类型的 code
//                if ($device->deviceMold->deviceType->code === 'pir_sensor_1') {
//                    $deviceMoldIds = DB::table('device_molds')->where('device_type_id', $device->deviceMold->deviceType->id)->pluck('id')->all();
//                    $deviceIdentifiers = $watchPoint->devices->whereIn('device_mold_id', $deviceMoldIds)->pluck('identifier')->all();
//                    $pirLatestTime = DB::table('telemetry_values')
////                        // TODO 只查询15分钟内， 15分钟是设置的参数
////                        ->where('measured_at', '>', Carbon::now()->subMinutes(15))
//                        ->where(['field_code'=>'pir', 'string_value' => 'pir'])
//                        ->whereIn('device_identifier', $deviceIdentifiers)
//                        ->orderBy('measured_at', 'desc')
//                        ->pluck('measured_at')
//                        ->first();
//                    $data['additive'] = ['pirLatestTime' => $pirLatestTime];
//                }
            });
        });
        return $this->sendResponse($data, 'overview processed successfully.');
    }

    /**
     * @OA\Post(
     *      path="/system/device/send_command",
     *      operationId="DeviceController::sendCommand",
     *      tags={"System.Device"},
     *      summary="",
     *      description="send command to device",
     *      security={{"api_http_auth": {}}},
     *      @OA\RequestBody(ref="#/components/requestBodies/System.DeviceCommandRequestBody"),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation"
     *      ),
     *      @OA\Response(response=400, description="Bad request")
     *  )
     *
     * @param DeviceCommandRequest $request
     * @return JsonResponse
     * @throws \Illuminate\Auth\Access\AuthorizationException
     */
    public function sendCommand(DeviceCommandRequest $request)
    {
//        $this->authorize('sendCommand');
        $data = $request->input('commands');
        $dtfRepository = app(DeviceTelemetryFieldRepository::class);
        $sendData = $dtfRepository->changeToRealCommands($data);
        $exitCode = Artisan::call('system:amqp-send', [
            'data' => $sendData,
            'type' => 'send_command'
        ]);
        
        // Update device caches in device-data-router instead of the old cache mechanism
        $identifiers = array_column($sendData, 'identifier');
        $this->cacheService->batchCacheDevicesByIdentifiers($identifiers);
        
        return $this->sendResponse($exitCode, 'send command successfully.');
    }

    /**
     * @param DeviceCommandRequest $request
     * @return JsonResponse
     */
    public function updateDevicesStatus(DeviceCommandRequest $request)
    {
        $input = $request->post();
        $res = $this->repository->batchUpdateByIdentifier($input);
        activity_log()->add('批量更新 Device 状态', $input);
        return $this->sendResponse($res, 'Devices saved successfully.');
    }

    /**
     * @OA\Post(
     *      path="/system/device/check",
     *      operationId="DeviceController::check",
     *      tags={"System.Device"},
     *      summary="",
     *      description="用于设备端上传数据前的设备确认，此过程会确保'保存遥测数据所依赖的数据'提前已创建好",
     *      security={{"api_http_auth": {}}},
     *      @OA\RequestBody(ref="#/components/requestBodies/System.DeviceCheckRequestBody"),
     *      @OA\Response(
     *          response=200,
     *          description="check complete",
     *          @OA\JsonContent(ref="#/components/schemas/BaseStoreResponse200")
     *      ),
     *      @OA\Response(response=400, description="Bad request"),
     *  )
     *
     * @param DeviceCheckRequest $request
     * @return JsonResponse
     */
    public function check(DeviceCheckRequest $request)
    {
        $success = $this->repository->splitPlcAndDevices($request->all());
        if (!$success) {
            activity_log()->add('设备检查：设备上传了异常的设备类型数据', $request->all());
        }
        return $this->sendResponse('', 'Devices checked.');
    }

    /**
     * @OA\Post(
     *      path="/system/device/import",
     *      operationId="DeviceController::import",
     *      tags={"System.Device"},
     *      summary="Submit an plc import",
     *      description="Submit an plc import. tenant admin shall pass.",
     *      security={{"api_http_auth": {}}},
     *      @OA\RequestBody(ref="#/components/requestBodies/System.DeviceImportRequestBody"),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation"
     *      ),
     *      @OA\Response(response=400, description="Bad request")
     *  )
     *
     * @param DeviceImportRequest $request
     * @return JsonResponse
     * @throws AuthorizationException
     */
    public function import(DeviceImportRequest $request)
    {
        $this->authorize('import', Device::class);
        $file = $request->file( 'file' );

        if( $file->getClientOriginalExtension() != 'xlsx' && $file->getClientOriginalExtension() != 'xls' ) {
            return $this->sendError( '文件类型错误' );
        }
        $accountCurrent = auth()->user();
        $currentTenantAdmin = $accountCurrent->accountable;

        $collection = Excel::toCollection(null, $file);
        $sheet = $collection->first();
        $row = $sheet[6];
        // 校验模版是否正确
        if ($row[0] != '控制器名称') {
            return $this->sendError(__('System/Device.messages.template_type'));
        }
        try {
            Excel::import(new DevicesImport($currentTenantAdmin), $file);
        }catch (\Exception $e) {
            if ($e->getMessage() === 'Start row (2) is beyond highest row (1)') {
                return $this->sendError(__('System/Account.messages.start_row_beyond_highest_row'));
            }
            throw $e;
        }
        $account = $request->user();

        activity_log()->withObject($account)->add("导入PLC");
        return $this->sendResponse(null, 'imported successfully.');
    }

    /**
     * 已经接近下次校准时间或已经逾期下次校准时间的设备列表
     * @OA\Get(
     *      path="/system/device/calibration",
     *      operationId="DeviceController::calibration",
     *      tags={"System.Device"},
     *      summary="已经接近下次校准时间或已经逾期下次校准时间的设备列表",
     *      description="已经接近下次校准时间或已经逾期下次校准时间的设备列表",
     *      security={{"api_http_auth": {}}},
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\JsonContent(ref="#/components/schemas/BaseStoreResponse200")
     *      ),
     *      @OA\Response(response=400, description="Bad request")
     *  )
     * @param DeviceListRequest $request
     * @return JsonResponse
     */
    public function calibration(DeviceListRequest $request)
    {
        $account = $request->user();
        if ($account->isTenantAdmin()) {
            $query = $this->repository->getCalibrationDevices($account->accountable->tenant_id);
            $paginated = DeviceQueryBuilder::paginate($query);
            $data = new DeviceCollection($paginated);
            return $data;
        } else {
            return $this->sendError('you are not tenant admin');
        }
    }

    /**
     * @OA\Post(
     *      path="/system/device/destroy_list",
     *      operationId="DeviceController::destroyList",
     *      tags={"System.Device"},
     *      summary="批量删除设备",
     *      description="批量删除设备",
     *      security={{"api_http_auth": {}}},
     *     @OA\RequestBody(
     *          description="Request body for delete multiple devices",
     *          required=true,
     *          @OA\JsonContent(
     *              type="object",
     *              @OA\Property(property="ids", type="array", @OA\Items(type="integer"))
     *          )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation"
     *      ),
     *      @OA\Response(response=400, description="Bad request")
     *  )
     * )
     */
    public function destroyList(DeviceDeleteRequest $request)
    {
        $ids = $request->input('ids');
        
        // Get identifiers before deletion for cache invalidation
        $identifiers = Device::whereIn('id', $ids)->pluck('identifier')->toArray();
        
        $result = $this->repository->batchDelete($ids);
        if ($result instanceof \Exception) {
            return $this->sendError($result->getMessage());
        }
        
        // Invalidate device caches in device-data-router
        $this->cacheService->batchInvalidateDevicesByIdentifiers($identifiers);

        activity_log()->add("批量删除设备 (".implode(',', $ids).")");
        return $this->sendResponse(null, 'Devices deleted successfully.');
    }

    /**
     * @OA\Post(
     *      path="/system/device/upload_image_by_identifier",
     *      operationId="DeviceController::uploadImageByIdentifier",
     *      tags={"System.Device"},
     *      summary="Upload device image by identifier",
     *      description="Upload device image by identifier in one step. This method combines file upload with device association.",
     *      security={{"api_sign_auth": {}}},
     *      @OA\RequestBody(
     *          required=true,
     *          @OA\MediaType(
     *              mediaType="multipart/form-data",
     *              @OA\Schema(
     *                  required={"identifier", "image"},
     *                  @OA\Property(property="identifier", type="string", description="Device identifier"),
     *                  @OA\Property(property="image", type="string", format="binary", description="Device image file"),
     *                  @OA\Property(property="description", type="string", description="Optional image description"),
     *              )
     *          )
     *      ),
     *      @OA\Response(response=200, description="Image uploaded and associated successfully",
     *          @OA\JsonContent(ref="#/components/schemas/BaseStoreResponse200")),
     *      @OA\Response(response=400, description="Bad request"),
     *      @OA\Response(response=404, description="Device not found"),
     *      @OA\Response(response=422, description="Validation error")
     *  )
     *
     * @param \Illuminate\Http\Request $request
     * @return JsonResponse
     * @throws AuthorizationException
     */
    public function uploadImageByIdentifier(\Illuminate\Http\Request $request)
    {
        // Validate request
        $request->validate([
            'identifier' => 'required|string',
            'image' => 'required|file|mimes:jpeg,jpg,png,gif|max:10240', // Max 10MB
            'description' => 'nullable|string|max:255',
        ]);

        $identifier = $request->input('identifier');
        $description = $request->input('description', '');
        
        // Find device by identifier
        $device = $this->repository->getDeviceWithIdentifier($identifier);
        if (empty($device)) {
            return $this->sendError('Device not found');
        }

        // Check authorization
        // $this->authorize('update', $device);

        try {
            // Create upload record using UploadRepository
            $uploadInput = [
                'file' => $request->file('image'),
                'type' => \Modules\System\Models\Upload::TYPE_DEVICE_IMAGE,
                'description' => $description,
                'disk' => config('upload.base_storage_disk'),
            ];
            
            $uploadRepository = app(\Modules\System\Repositories\UploadRepository::class);
            $upload = $uploadRepository->create($uploadInput, null, $request->user());
            
            // Associate upload with device
            $upload->uploadable_id = $device->id;
            $upload->uploadable_type = \Modules\System\Models\Device::class;
            $upload->save();

            activity_log()->withObject($device)->add("上传设备图片 {$device->name} ({$device->id})", [
                'identifier' => $identifier,
                'upload_id' => $upload->id,
                'description' => $description
            ]);

            // Trigger tenant device telemetry data updated event
            // Only include device_identifier and device_id as requested
            $eventData = [
                'device_identifier' => $identifier,
                'device_id' => $device->id,
                'device_name' => '',
                'device_mold_code' => '',
                'member_name' => '',
                'source' => '',
                'gateway' => '',
                'association_period' => '',
                'metadata' => [],
                'telemetry' => [],
                'stats' => [],
                'timestamp' => now()->toISOString()
            ];
            
            event(new TenantDeviceTelemetryDataUpdated($device->tenant_id, $eventData));

            return $this->sendResponse([
                'upload_id' => $upload->id,
                'device_id' => $device->id,
                'url' => $upload->url
            ], 'Device image uploaded successfully.');
            
        } catch (\zgldh\UploadManager\UploadException $e) {
            return response()->json([
                'errors' => ['image' => $e->errors],
                'message' => 'Upload failed'
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'errors' => ['upload' => 'Upload failed: ' . $e->getMessage()],
                'message' => 'Upload failed'
            ], 500);
        }
    }
}
