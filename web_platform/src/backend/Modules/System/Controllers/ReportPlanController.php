<?php

namespace Modules\System\Controllers;

use Modules\System\Contracts\TenantPolicyManagerInterface;
use Modules\System\Models\Strategy;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Scaffold\QueryBuilder\QueryBuilder;
use Scaffold\BaseController;
use Modules\System\Requests\ReportPlan\ReportPlanUpdateRequest;
use Modules\System\Requests\ReportPlan\ReportPlanListRequest;
use Modules\System\Requests\ReportPlan\ReportPlanGetRequest;
use Modules\System\Requests\ReportPlan\ReportPlanDeleteRequest;
use Modules\System\Requests\ReportPlan\ReportPlanCreateRequest;
use Modules\System\Repositories\ReportPlanRepository;
use Modules\System\Resources\ReportPlanResource;
use Modules\System\Resources\ReportPlanCollection;
use Modules\System\Models\ReportPlan;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class ReportPlanController extends BaseController
{
    public function __construct(ReportPlanRepository $repository)
    {
        $this->repository = $repository;
    }

    /**
     * @OA\Get(
     *      path="/system/report_plan",
     *      operationId="ReportPlanController::index",
     *      tags={"System.ReportPlan"},
     *      summary="Get list of ReportPlans",
     *      description="Returns list of ReportPlans",
     *      security={{"api_http_auth": {}}},
     *      @OA\Parameter(ref="#/components/parameters/IndexQuery"),
     *      @OA\Response(response=200, description="successful operation",
     *          @OA\JsonContent(type="object", description="Response of an index API.",
     *              required={"links","meta","data"},
     *              @OA\Property(property="links", ref="#/components/schemas/IndexResponseLinks"),
     *              @OA\Property(property="meta", ref="#/components/schemas/IndexResponseMeta"),
     *              @OA\Property(property="data", type="array", description="Data array.",
     *                   @OA\Items(ref="#/components/schemas/System.ReportPlanResource")
     *              ),
     *          ),
     *      ),
     *      @OA\Response(response=400, description="Bad request")
     *  )
     *
     * @param ReportPlanListRequest $request
     * @param TenantPolicyManagerInterface $tenantPolicyManager
     * @return ReportPlanCollection
     * @throws \Illuminate\Auth\Access\AuthorizationException
     */
    public function index(ReportPlanListRequest $request, TenantPolicyManagerInterface $tenantPolicyManager)
    {
        $this->authorize('viewAny', ReportPlan::class);
        $account = $request->user();
        $query = QueryBuilder::for(ReportPlan::class)->getQuery();
        if ($account->isTenantAdmin()) {
            $policyAlteredTenantId = $tenantPolicyManager->getAlteredTenantId('strategy', 'view', $account->accountable->tenant_id);
            $includeSubTenants = $request->includeSubTenants(true);
            $query = $query->tenantId($policyAlteredTenantId, $includeSubTenants);
        }
        $paginated = QueryBuilder::paginate($query);
        $data = new ReportPlanCollection($paginated);
        return $data;
    }
    /**
     * @OA\Post(
     *      path="/system/report_plan",
     *      operationId="ReportPlanController::store",
     *      tags={"System.ReportPlan"},
     *      summary="Store a newly created ReportPlan in storage.",
     *      description="Store a new ReportPlan",
     *      security={{"api_http_auth": {}}},
     *      @OA\RequestBody(
     *          description="Payload to create ReportPlan",
     *          required=true,
     *          @OA\JsonContent(ref="#/components/schemas/System.ReportPlan")
     *      ),
     *      @OA\Response(response=200, description="Store complete",  @OA\JsonContent(ref="#/components/schemas/BaseStoreResponse200")),
     *      @OA\Response(response=400, description="Bad request")
     *  )
     *
     * @param ReportPlanCreateRequest $request
     * @return JsonResponse
     * @throws \Illuminate\Auth\Access\AuthorizationException
     */
    public function store(ReportPlanCreateRequest $request)
    {
        $this->authorize('create', ReportPlan::class);
        $input = $request->all();
        /**
         * @var $item ReportPlan
         */
        $item = $this->repository->create($input);
        activity_log()->withObject($item)->add('创建 ReportPlan', $input);
        return $this->sendResponse($item->getKey(), 'ReportPlan saved successfully.');
    }
    /**
     * @OA\Get(
     *      path="/system/report_plan/{id}",
     *      operationId="ReportPlanController::show",
     *      tags={"System.ReportPlan"},
     *      summary="Display the specified ReportPlan",
     *      description="Get one ReportPlan by id",
     *      security={{"api_http_auth": {}}},
     *      @OA\Parameter(
     *          name="id",
     *          description="ReportPlan id",
     *          required=true,
     *          in="path",
     *          @OA\Schema(
     *              type="integer"
     *          )
     *      ),
     *      @OA\Parameter(ref="#/components/parameters/with"),
     *      @OA\Response(response=200, description="successful operation",
     *          @OA\JsonContent(type="object", description="Fetched record.",
     *              required={"data"},
     *              @OA\Property(property="data", ref="#/components/schemas/System.ReportPlanResource")
     *          ),
     *      ),
     *      @OA\Response(response=400, description="Bad request")
     *  )
     *
     * @param int $id
     *
     * @param ReportPlanGetRequest $request
     * @return JsonResponse|ReportPlanResource
     * @throws \Illuminate\Auth\Access\AuthorizationException
     */
    public function show($id, ReportPlanGetRequest $request)
    {
        /**
         * @var $item ReportPlan
         */
        $item = $this->repository->findWithoutFail($id);
        if (empty($item)) {
            return $this->sendError('ReportPlan not found');
        }
        $this->authorize('view', $item);
        $item->load($request->getWith());
        return new ReportPlanResource($item);
    }
    /**
     * @OA\Put(
     *      path="/system/report_plan/{id}",
     *      operationId="ReportPlanController::update",
     *      tags={"System.ReportPlan"},
     *      summary="Update the specified ReportPlan in storage.",
     *      description="Update one ReportPlan by id",
     *      security={{"api_http_auth": {}}},
     *      @OA\Parameter(
     *          name="id",
     *          description="ReportPlan id",
     *          required=true,
     *          in="path",
     *          @OA\Schema(
     *              type="integer"
     *          )
     *      ),
     *      @OA\RequestBody(
     *          description="Payload to update ReportPlan",
     *          required=true,
     *          @OA\JsonContent(ref="#/components/schemas/System.ReportPlan")
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation"
     *      ),
     *      @OA\Response(response=400, description="Bad request")
     *  )
     *
     * @param int $id
     * @param ReportPlanUpdateRequest $request
     *
     * @return JsonResponse
     * @throws \Illuminate\Auth\Access\AuthorizationException
     */
    public function update($id, ReportPlanUpdateRequest $request)
    {
        /**
         * @var $item ReportPlan
         */
        $item = $this->repository->findWithoutFail($id);
        if (empty($item)) {
            return $this->sendError('ReportPlan not found');
        }
        $this->authorize('update', $item);
        $input = $request->all();
        $this->repository->update($input, $id);
        activity_log()->withObject($item)->add('修改 ReportPlan', $input);
        return $this->sendResponse(null, 'ReportPlan updated successfully.');
    }
    /**
     * @OA\Delete(
     *      path="/system/report_plan/{id}",
     *      operationId="ReportPlanController::destroy",
     *      tags={"System.ReportPlan"},
     *      summary="Remove the specified ReportPlan from storage.",
     *      description="Delete one ReportPlan by id",
     *      security={{"api_http_auth": {}}},
     *      @OA\Parameter(
     *          name="id",
     *          description="ReportPlan id",
     *          required=true,
     *          in="path",
     *          @OA\Schema(
     *              type="integer"
     *          )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation"
     *      ),
     *      @OA\Response(response=400, description="Bad request")
     *  )
     *
     * @param int $id
     *
     * @param ReportPlanDeleteRequest $request
     * @return JsonResponse
     * @throws \Illuminate\Auth\Access\AuthorizationException
     */
    public function destroy($id, ReportPlanDeleteRequest $request)
    {
        /**
         * @var $item ReportPlan
         */
        $item = $this->repository->findWithoutFail($id);
        if (empty($item)) {
            return $this->sendError('ReportPlan not found');
        }
        $this->authorize('delete', $item);
        $this->repository->delete($id);
        activity_log()->withObject($item)->add('删除 ReportPlan', $item->toArray());
        return $this->sendResponse(null, 'ReportPlan deleted successfully.');
    }

    /**
     * @OA\Post(
     *      path="/system/report_plan/{id}/copy",
     *      operationId="ReportPlanController::copy",
     *      tags={"System.ReportPlan"},
     *      summary="copy the specified ReportPlan from storage.",
     *      description="copy one ReportPlan by id",
     *      security={{"api_http_auth": {}}},
     *      @OA\Parameter(
     *          name="id",
     *          description="ReportPlan id",
     *          required=true,
     *          in="path",
     *          @OA\Schema(
     *              type="integer"
     *          )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation"
     *      ),
     *      @OA\Response(response=400, description="Bad request")
     *  )
     *
     * @param int $id
     * @return JsonResponse
     * @throws \Illuminate\Auth\Access\AuthorizationException
     */
    public function copy($id)
    {
        /**
         * @var $item ReportPlan
         */
        $item = $this->repository->findWithoutFail($id);
        if (empty($item)) {
            return $this->sendError('ReportPlan not found');
        }
        $this->authorize('create', $item);
        $newId = $this->repository->copy($id);
        activity_log()->withObject($item)->add('复制 ReportPlan', $item->toArray());
        return $this->sendResponse($newId, 'ReportPlan copied successfully.');
    }

    /**
     * @OA\Get(
     *      path="/system/report_plan_models",
     *      operationId="RuleActionController::reportPlanModels",
     *      tags={""},
     *      summary="获取报表计划可用的模型",
     *      description="",
     *      security={{"api_http_auth": {}}},
     *      @OA\Response(response=200, description="successful operation",
     *          @OA\JsonContent(type="object", description="Fetched record.",
     *              required={"data"},
     *              @OA\Property(property="data", type="object"),
     *          ),
     *      ),
     *      @OA\Response(response=400, description="Bad request")
     *  )
     *
     * @return JsonResponse
     */
    public function reportPlanModels()
    {
        return $this->sendResponse($this->repository->reportModels(), 'Get report plan models successfully.');
    }

    /**
     * @OA\Post(
     *      path="/system/report_plan/batch_delete",
     *      operationId="ReportPlanController::batchDestroy",
     *      tags={"System.ReportPlan"},
     *      summary="批量删除指定的ReportPlan",
     *      description="通过ID数组批量删除ReportPlan",
     *      security={{"api_http_auth": {}}},
     *      @OA\RequestBody(
     *          description="要删除的ReportPlan ID数组",
     *          required=true,
     *          @OA\JsonContent(
     *              type="object",
     *              @OA\Property(property="ids", type="array", @OA\Items(type="integer"))
     *          )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation"
     *      ),
     *      @OA\Response(response=400, description="Bad request")
     *  )
     *
     * @param Request $request
     * @return JsonResponse
     * @throws \Illuminate\Auth\Access\AuthorizationException
     */
    public function batchDestroy(Request $request)
    {
        $ids = $request->input('ids');
        if (empty($ids) || !is_array($ids)) {
            return $this->sendError('Invalid input');
        }
        $items = $this->repository->findWhereInIds($ids);
        if ($items->isEmpty()) {
            return $this->sendError('No ReportPlans found');
        }

        foreach ($items as $item) {
            $this->authorize('delete', $item);
        }

        $deletedCount = $this->repository->deleteWhereIn($ids);
        
        activity_log()->add("批量删除 ReportPlan (".implode(',', $ids).")");

        return $this->sendResponse($deletedCount, 'ReportPlans deleted successfully.');
    }
}
