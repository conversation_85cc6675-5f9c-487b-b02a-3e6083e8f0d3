<?php

namespace Modules\System\Controllers;

use Modules\System\Models\Strategy;
use Modules\System\QueryBuilders\DeviceQueryBuilder;
use Modules\System\Requests\Strategy\StrategyListRequest;
use Modules\System\Requests\Tenant\TenantDemoAdminRequest;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Contracts\Container\BindingResolutionException;
use Modules\System\Models\Space;
use Modules\System\Repositories\SpaceRepository;
use Modules\System\Requests\Space\SpaceCreateRequest;
use Modules\System\Requests\Space\SpaceListRequest;
use Modules\System\Requests\Tenant\TenantDeviceStatisticsRequest;
use Modules\System\Requests\Tenant\TenantResourcePolicyRequest;
use Modules\System\Contracts\TenantPolicyManagerInterface;
use Modules\System\Models\Account;
use Modules\System\Models\Device;
use Modules\System\Models\Rule;
use Modules\System\Repositories\DeviceRepository;
use Modules\System\Repositories\RuleRepository;
use Modules\System\Requests\Device\DeviceCreateRequest;
use Modules\System\Requests\Device\DeviceListRequest;
use Modules\System\Requests\Rule\RuleCreateRequest;
use Modules\System\Requests\Rule\RuleListRequest;
use Modules\System\Models\Role;
use Modules\System\Models\TenantAdmin;
use Modules\System\Repositories\RoleRepository;
use Modules\System\Repositories\TenantAdminRepository;
use Modules\System\Requests\Role\RoleCreateRequest;
use Modules\System\Requests\Role\RoleListRequest;
use Modules\System\Requests\Tenant\TenantCreateSubTenantRequest;
use Modules\System\Requests\Tenant\TenantOperatorStatusRequest;
use Modules\System\Requests\TenantAdmin\TenantAdminCreateRequest;
use Modules\System\Requests\TenantAdmin\TenantAdminListRequest;
use Modules\System\Resources\DeviceCollection;
use Modules\System\Resources\RoleCollection;
use Modules\System\Resources\RuleCollection;
use Modules\System\Resources\SpaceCollection;
use Modules\System\Resources\StrategyCollection;
use Modules\System\Resources\StrategyTemplateResource;
use Modules\System\Resources\TenantAdminCollection;
use Scaffold\QueryBuilder\QueryBuilder;
use Scaffold\BaseController;
use Modules\System\Requests\Tenant\TenantUpdateRequest;
use Modules\System\Requests\Tenant\TenantListRequest;
use Modules\System\Requests\Tenant\TenantGetRequest;
use Modules\System\Requests\Tenant\TenantDeleteRequest;
use Modules\System\Requests\Tenant\TenantCreateRequest;
use Modules\System\Services\DeviceDataRouterCacheService;
use Modules\System\Services\StreamingAuthService;
use Modules\System\Repositories\TenantRepository;
use Modules\System\Resources\TenantResource;
use Modules\System\Resources\TenantCollection;
use Modules\System\Models\Tenant;
use Illuminate\Http\JsonResponse;
use Modules\System\QueryBuilders\TenantQueryBuilder;

class TenantController extends BaseController
{
    private DeviceDataRouterCacheService $cacheService;
    
    public function __construct(TenantRepository $repository, DeviceDataRouterCacheService $cacheService)
    {
        $this->repository = $repository;
        $this->cacheService = $cacheService;
    }

    /**
     * @OA\Get(
     *      path="/system/tenant",
     *      operationId="TenantController::index",
     *      tags={"System.Tenant"},
     *      summary="Get list of Tenants",
     *      description="Super admin & Platform admins will load all tenants. <br>
    Tenant admin will load all tenant based on the policy setting 'scaffold.tenant_admin_to_tenant_resource_policy.tenant.view':<br>
    <b>self</b>: Able to load tenant from the tenant of current user. No cascade.<br>
    <b>cascade</b>: Able to load tenants from the tenant and sub-tenants of current user.<br>
    <b>top-cascade</b>: Able to load tenants from the top tenant and sub-tenants of current user.",
     *      security={{"api_http_auth": {}}},
     *      @OA\Parameter(ref="#/components/parameters/IndexQuery"),
     *      @OA\Parameter(ref="#/components/parameters/_includeSubTenants"),
     *      @OA\Response(response=200, description="successful operation",
     *          @OA\JsonContent(type="object", description="Response of an index API.",
     *              required={"links","meta","data"},
     *              @OA\Property(property="links", ref="#/components/schemas/IndexResponseLinks"),
     *              @OA\Property(property="meta", ref="#/components/schemas/IndexResponseMeta"),
     *              @OA\Property(property="data", type="array", description="Data array.",
     *                   @OA\Items(ref="#/components/schemas/System.TenantResource")
     *              ),
     *          ),
     *      ),
     *      @OA\Response(response=400, description="Bad request")
     *  )
     *
     * @param TenantListRequest $request
     * @param TenantPolicyManagerInterface $tenantPolicyManager
     * @return TenantCollection
     * @throws AuthorizationException
     */
    public function index(TenantListRequest $request, TenantPolicyManagerInterface $tenantPolicyManager)
    {
        $this->authorize('viewAny', Tenant::class);

        $account = $request->user();
        if ($account->isTenantAdmin()) {
            $policyAlteredTenantId = $tenantPolicyManager->getAlteredTenantId('tenant', 'view', $account->accountable->tenant_id);
            $includeSubTenants = $request->includeSubTenants(true);
            $query = Tenant::query()->tenantId($policyAlteredTenantId, $includeSubTenants);
        } else if ($account->isPlatformAdmin()) {
            $query = Tenant::query()
            ->whereHas('platformAdmins', function ($query) use ($account) {
                $query->where('platform_admin_id', $account->accountable->id);
            });
        } else {
            $query = Tenant::query();
        }
        $query = TenantQueryBuilder::for($query)->getQuery();
        $paginated = TenantQueryBuilder::paginate($query);
        $data = new TenantCollection($paginated);
        return $data;
    }

    /**
     * @OA\Post(
     *      path="/system/tenant",
     *      operationId="TenantController::store",
     *      tags={"System.Tenant"},
     *      summary="Store a new Tenant",
     *      description="Super admin, platform admins have permission 'paManageTenant' shall pass.",
     *      security={{"api_http_auth": {}}},
     *      @OA\RequestBody(ref="#/components/requestBodies/System.TenantCreateRequestBody"),
     *      @OA\Response(response=200, description="Store complete",  @OA\JsonContent(ref="#/components/schemas/BaseStoreResponse200")),
     *      @OA\Response(response=400, description="Bad request")
     *  )
     *
     * @param TenantCreateRequest $request
     * @return JsonResponse
     * @throws AuthorizationException
     */
    public function store(TenantCreateRequest $request)
    {
        $this->authorize('create', Tenant::class);
        $input = $request->getTenantAttributes();
        $tenantLoginName = $request->getTenantLoginName();
        $tenantPassword = $request->getTenantPassword();
        /**
         * @var $item Tenant
         */
        $item = $this->repository->createWithTenantAdmin($input, $tenantLoginName, $tenantPassword);

        activity_log()->withObject($item)->add("创建租户 {$item->name} ({$item->id})", $input);
        return $this->sendResponse($item->getKey(), 'Tenant saved successfully.');
    }

    /**
     * @OA\Post(
     *      path="/system/tenant/{id}/sub_tenant",
     *      operationId="TenantController::storeSubTenant",
     *      tags={"System.Tenant"},
     *      summary="Store a new Sub Tenant to a given tenant id",
     *      description="Super admin, platform admins have permission 'paManageTenant' shall pass.<br>
    Tenant admins with permission 'taManageTenant' shall pass.<br>
    Tenant admin permission is based on the policy setting 'scaffold.tenant_admin_to_tenant_resource_policy.tenant.manage'.<br>
    The tenant admin with permission 'taManageTenant' is able to create a tenant to the given $tenantId when: <br>
    <b>self</b>: The $parentTenantId equals to $loginAccount->accountable->tenant_id.<br>
    <b>cascade</b>: The $parentTenantId is one of cascade tenant of $loginAccount->accountable->tenant",
     *      security={{"api_http_auth": {}}},
     *      @OA\Parameter(name="id", description="Parent Tenant id", required=true, in="path",
     *          @OA\Schema(type="integer")
     *      ),
     *      @OA\RequestBody(ref="#/components/requestBodies/System.TenantCreateRequestBody"),
     *      @OA\Response(response=200, description="Store complete",  @OA\JsonContent(ref="#/components/schemas/BaseStoreResponse200")),
     *      @OA\Response(response=400, description="Bad request")
     *  )
     *
     * @param $parentTenantId
     * @param TenantCreateSubTenantRequest $request
     * @return JsonResponse
     * @throws AuthorizationException
     */
    public function storeSubTenant($parentTenantId, TenantCreateSubTenantRequest $request)
    {
        $this->authorize('createSubTenant', [
            Tenant::class,
            $parentTenantId,
        ]);
        $input = $request->getTenantAttributes();
        $tenantLoginName = $request->getTenantLoginName();
        $tenantPassword = $request->getTenantPassword();

        /**
         * @var $item Tenant
         */
        $item = $this->repository->createWithTenantAdmin($input, $tenantLoginName, $tenantPassword);

        activity_log()->withObject($item)->add("创建子租户 {$item->name} ({$item->id})", $input);
        return $this->sendResponse($item->getKey(), 'Sub Tenant saved successfully.');
    }

    /**
     * @OA\Get(
     *      path="/system/tenant/{id}",
     *      operationId="TenantController::show",
     *      tags={"System.Tenant"},
     *      summary="Get one Tenant",
     *      description="Super admin & Platform admins will load any tenant to the $id. <br>
    Tenant admin will load any tenant based on the policy setting 'scaffold.tenant_admin_to_tenant_resource_policy.tenant.view':<br>
    <b>self</b>: Able to load tenant from the tenant only of current user. No cascade.<br>
    <b>cascade</b>: Able to load tenants from the tenant and sub-tenants of current user.<br>
    <b>top-cascade</b>: Able to load tenants from the top tenant and sub-tenants of current user.",
     *      security={{"api_http_auth": {}}},
     *      @OA\Parameter(
     *          name="id",
     *          description="Tenant id",
     *          required=true,
     *          in="path",
     *          @OA\Schema(
     *              type="integer"
     *          )
     *      ),
     *      @OA\Parameter(ref="#/components/parameters/with"),
     *      @OA\Response(response=200, description="successful operation",
     *          @OA\JsonContent(type="object", description="Fetched record.",
     *              required={"data"},
     *              @OA\Property(property="data", ref="#/components/schemas/System.TenantResource")
     *          ),
     *      ),
     *      @OA\Response(response=400, description="Bad request")
     *  )
     *
     * @param int $id
     *
     * @param TenantGetRequest $request
     * @return JsonResponse|TenantResource
     * @throws AuthorizationException
     */
    public function show($id, TenantGetRequest $request)
    {
        $item = $this->repository->findWithoutFail($id);
        if (empty($item)) {
            return $this->sendError('Tenant not found');
        }
        $this->authorize('view', $item);
        $item->load($request->getWith());
        return new TenantResource($item);
    }

    /**
     * @OA\Put(
     *      path="/system/tenant/{id}",
     *      operationId="TenantController::update",
     *      tags={"System.Tenant"},
     *      summary="Update one Tenant",
     *      description="Super admin, platform admins have permission 'paManageTenant' shall pass.
    Tenant admins with permission 'taManageTenant' can update any cascade tenants belong to them.<br>
    Tenant admin permission is based on the policy setting 'scaffold.tenant_admin_to_tenant_resource_policy.tenant.manage'.<br>
    The tenant admin with permission 'taManageTenant' is able to update a tenant to the given $tenantId when: <br>
    <b>self</b>: The $tenantId equals to $loginAccount->accountable->tenant_id.<br>
    <b>cascade</b>: The $tenantId is one of cascade tenant of $loginAccount->accountable->tenant<br>
    Super admin and platform admin can modify tenant Name, Active, Plan and Expire_at; While tenant admin can only change tenant Name.",
     *      security={{"api_http_auth": {}}},
     *      @OA\Parameter(
     *          name="id",
     *          description="Tenant id",
     *          required=true,
     *          in="path",
     *          @OA\Schema(
     *              type="integer"
     *          )
     *      ),
     *      @OA\RequestBody(ref="#/components/requestBodies/System.TenantUpdateRequestBody"),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation"
     *      ),
     *      @OA\Response(response=400, description="Bad request")
     *  )
     *
     * @param int $id
     * @param TenantUpdateRequest $request
     *
     * @return JsonResponse
     * @throws AuthorizationException
     */
    public function update($id, TenantUpdateRequest $request)
    {
        /**
         * @var $item Tenant
         */
        $item = $this->repository->findWithoutFail($id);
        if (empty($item)) {
            return $this->sendError('Tenant not found');
        }
        $this->authorize('update', $item);

        /**
         * @var $user Account
         */
        $user = $request->user();
        $attributes = ['logo' => $request->get('upload_id')];
        $request->has('name') && $attributes['name'] = $request->input('name');
        $request->has('web_name') && $attributes['web_name'] = $request->input('web_name');
        $request->has('longitude') && $attributes['longitude'] = $request->input('longitude');
        $request->has('latitude') && $attributes['latitude'] = $request->input('latitude');
        $request->has('address') && $attributes['address'] = $request->input('address');
        if ($user->isPlatformAdmin() || $user->isSuperAdmin()) {
            $request->has('active') && $attributes['active'] = $request->boolean('active');
            $request->has('company_code') && $attributes['company_code'] = $request->input('company_code');
            $request->has('plan') && $attributes['plan'] = $request->input('plan');
            $request->has('expire_at') && $attributes['expire_at'] = $request->input('expire_at');
        }
        $this->repository->update($attributes, $id);
        activity_log()->withObject($item)->add("修改租户 {$item->name} ({$item->id})", $attributes);
        return $this->sendResponse(null, 'Tenant updated successfully.');
    }

    /**
     * @OA\Delete(
     *      path="/system/tenant/{id}",
     *      operationId="TenantController::destroy",
     *      tags={"System.Tenant"},
     *      summary="Remove the specified Tenant from storage.",
     *      description="TODO Delete one Tenant by id",
     *      security={{"api_http_auth": {}}},
     *      @OA\Parameter(
     *          name="id",
     *          description="Tenant id",
     *          required=true,
     *          in="path",
     *          @OA\Schema(
     *              type="integer"
     *          )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation"
     *      ),
     *      @OA\Response(response=400, description="Bad request")
     *  )
     *
     * @param int $id
     *
     * @param TenantDeleteRequest $request
     * @return JsonResponse
     * @throws AuthorizationException
     */
    public function destroy($id, TenantDeleteRequest $request)
    {
        /**
         * @var $item Tenant
         */
        $item = $this->repository->findWithoutFail($id);
        if (empty($item)) {
            return $this->sendError('Tenant not found');
        }
        $this->authorize('delete', $item);
        $this->repository->delete($id);

        activity_log()->withObject($item)->add("删除租户 {$item->name} ({$item->id})");
        return $this->sendResponse(null, 'Tenant deleted successfully.');
    }

    /**
     * @OA\Get(
     *      path="/system/tenant/{id}/role",
     *      operationId="TenantController::indexTenantRole",
     *      tags={"System.Tenant"},
     *      summary="Get tenant role list from a tenant",
     *      description="Get tenant role list from a tenant. <br>Super admin and platform admin shall pass. <br>
    Tenant admins belong to the $tenantId shall pass.<br> Other can not",
     *      security={{"api_http_auth": {}}},
     *      @OA\Parameter(
     *          name="id",
     *          description="Tenant id",
     *          required=true,
     *          in="path",
     *          @OA\Schema(
     *              type="integer"
     *          )
     *      ),
     *      @OA\Parameter(ref="#/components/parameters/IndexQuery"),
     *      @OA\Parameter(ref="#/components/parameters/_includeSubTenants"),
     *      @OA\Response(response=200, description="successful operation",
     *          @OA\JsonContent(type="object", description="Response of an index API.",
     *              required={"links","meta","data"},
     *              @OA\Property(property="links", ref="#/components/schemas/IndexResponseLinks"),
     *              @OA\Property(property="meta", ref="#/components/schemas/IndexResponseMeta"),
     *              @OA\Property(property="data", type="array", description="Data array.",
     *                   @OA\Items(ref="#/components/schemas/System.RoleResource")
     *              ),
     *          ),
     *      ),
     *      @OA\Response(response=400, description="Bad request")
     *  )
     *
     * @param int $tenantId
     * @param RoleListRequest $request
     * @return RoleCollection
     * @throws AuthorizationException
     */
    public function indexTenantRole(int $tenantId, RoleListRequest $request)
    {
        $this->authorize('viewAnyTenantRole', [
            Role::class,
            $tenantId,
        ]);

        $includeSubTenants = $request->includeSubTenants($request->user()->isTenantAdmin());
        $query = Role::query()->where('name', '!=', 'default_top_head_admin')->tenantId($tenantId, $includeSubTenants);

        $query = QueryBuilder::for($query)->getQuery();
        $paginated = QueryBuilder::paginate($query);
        $data = new RoleCollection($paginated);
        return $data;
    }

    /**
     * @OA\Post(
     *      path="/system/tenant/{id}/role",
     *      operationId="TenantController::storeTenantRole",
     *      tags={"System.Tenant"},
     *      summary="Store a new Tenant Role",
     *      description="Super admin shall pass. <br>Tenant admins belong to the $tenantId and have permission 'taManageRole' shall pass.<br> Other can not",
     *      security={{"api_http_auth": {}}},
     *      @OA\Parameter(
     *          name="id",
     *          description="Tenant id",
     *          required=true,
     *          in="path",
     *          @OA\Schema(
     *              type="integer"
     *          )
     *      ),
     *      @OA\RequestBody(
     *          description="Payload to create Tenant Role",
     *          required=true,
     *          @OA\JsonContent(ref="#/components/schemas/System.Role")
     *      ),
     *      @OA\Response(response=200, description="Store complete",  @OA\JsonContent(ref="#/components/schemas/BaseStoreResponse200")),
     *      @OA\Response(response=400, description="Bad request")
     *  )
     *
     * @param RoleCreateRequest $request
     * @return JsonResponse
     * @throws AuthorizationException
     */
    public function storeTenantRole(int $tenantId, RoleCreateRequest $request, RoleRepository $roleRepository)
    {
        $this->authorize('createTenantRole', [
            Role::class,
            $tenantId,
        ]);
        $input = $request->all();
        $input['tenant_id'] = $tenantId;
        /**
         * @var $item Role
         */
        $item = $roleRepository->create($input);

        activity_log()->withObject($item)->add("创建租户角色 {$item->name} ({$item->id}) 租户: {$item->tenant_id}", $input);
        return $this->sendResponse($item->getKey(), 'Role saved successfully.');
    }

    /**
     * @OA\Get(
     *      path="/system/tenant/{id}/tenant_admin",
     *      operationId="TenantController::indexTenantAdmin",
     *      tags={"System.Tenant"},
     *      summary="Get tenant admin list from a tenant",
     *      description="Super admin and platform admin shall pass. <br>
    Tenant admins own the $tenantId shall pass.<br> Other can not",
     *      security={{"api_http_auth": {}}},
     *      @OA\Parameter(
     *          name="id",
     *          description="Tenant id",
     *          required=true,
     *          in="path",
     *          @OA\Schema(
     *              type="integer"
     *          )
     *      ),
     *      @OA\Parameter(ref="#/components/parameters/IndexQuery"),
     *      @OA\Parameter(ref="#/components/parameters/_includeSubTenants"),
     *      @OA\Response(response=200, description="successful operation",
     *          @OA\JsonContent(type="object", description="Response of an index API.",
     *              required={"links","meta","data"},
     *              @OA\Property(property="links", ref="#/components/schemas/IndexResponseLinks"),
     *              @OA\Property(property="meta", ref="#/components/schemas/IndexResponseMeta"),
     *              @OA\Property(property="data", type="array", description="Data array.",
     *                   @OA\Items(ref="#/components/schemas/System.TenantAdminResource")
     *              ),
     *          ),
     *      ),
     *      @OA\Response(response=400, description="Bad request")
     *  )
     *
     * @param int $tenantId
     * @param TenantAdminListRequest $request
     * @return TenantAdminCollection
     * @throws AuthorizationException
     */
    public function indexTenantAdmin(int $tenantId, TenantAdminListRequest $request)
    {
        $this->authorize('viewAnyOfTenant', [
            TenantAdmin::class,
            $tenantId,
        ]);
        $includeSubTenants = $request->includeSubTenants($request->user()->isTenantAdmin());
        if ($tenantId === 0) {
            // 使超管可查
            $query = TenantAdmin::query();
        } else {
            $query = TenantAdmin::query()->tenantId($tenantId, $includeSubTenants);
        }

        $query = QueryBuilder::for($query)->getQuery();
        // TODO 待优化，多级关联查询，通用化到脚手架
        $filter = $request->get('filter');
        if ($filter && strpos($filter, 'account.roles.id') !== false) {
            if(preg_match('/\b\d+\b/', $filter, $matches)) {
                $query = $query->whereHas('account.roles', function ($query) use ($matches) {
                    $query->where('roles.id', $matches[0]);
                });
            }
        }

        $paginated = QueryBuilder::paginate($query);
        $data = new TenantAdminCollection($paginated);
        return $data;
    }

    /**
     * @OA\Post(
     *      path="/system/tenant/{id}/tenant_admin",
     *      operationId="TenantController::storeTenantAdmin",
     *      tags={"System.Tenant"},
     *      summary="Store a new Tenant admin for a tenant",
     *      description="Super admin shall pass. <br>Tenant admins own the $tenantId and have permission 'taManageTenantAdmin' shall pass.<br> Other can not",
     *      security={{"api_http_auth": {}}},
     *      @OA\Parameter(
     *          name="id",
     *          description="Tenant id",
     *          required=true,
     *          in="path",
     *          @OA\Schema(
     *              type="integer"
     *          )
     *      ),
     *      @OA\RequestBody(ref="#/components/requestBodies/System.TenantAdminCreateRequestBody"),
     *      @OA\Response(response=200, description="Store complete",  @OA\JsonContent(ref="#/components/schemas/BaseStoreResponse200")),
     *      @OA\Response(response=400, description="Bad request")
     *  )
     *
     * @param int $tenantId
     * @param TenantAdminCreateRequest $request
     * @param TenantAdminRepository $tenantAdminRepository
     * @return JsonResponse
     * @throws AuthorizationException
     * @throws BindingResolutionException
     */
    public function storeTenantAdmin(int $tenantId, TenantAdminCreateRequest $request, TenantAdminRepository $tenantAdminRepository)
    {
        $this->authorize('create', [
            TenantAdmin::class,
            $tenantId,
        ]);
        $input = $request->all();
        $input['tenant_id'] = $tenantId;
        /**
         * @var $item TenantAdmin
         */
        $item = $tenantAdminRepository->createWithAccount($input);

        activity_log()->withObject($item)->add("创建租户管理员 {$item->name} ({$item->id}) 租户: {$item->tenant_id}", $input);
        return $this->sendResponse($item->getKey(), 'Tenant admin saved successfully.');
    }

    /**
     * @OA\Get(
     *      path="/system/tenant/{id}/space",
     *      operationId="TenantController::indexSpace",
     *      tags={"System.Tenant"},
     *      summary="Get spaces list from a given tenant.",
     *      description="Super admin shall load all space for the $tenantId. <br>
    Platform admins will NOT pass. <br>
    Tenant admin will load all space based on the policy setting 'scaffold.tenant_admin_to_tenant_resource_policy.space.view':<br>
    <b>self</b>: Able to load spaces from the tenant of current user. No cascade.<br>
    <b>cascade</b>: Able to load spaces from the tenant and sub-tenants of current user.<br>
    <b>top-cascade</b>: Able to load spaces from the top tenant and sub-tenants of current user.",
     *      security={{"api_http_auth": {}}},
     *      @OA\Parameter(name="id", description="Tenant ID", required=true, in="path",
     *          @OA\Schema(type="integer")
     *      ),
     *      @OA\Parameter(ref="#/components/parameters/IndexQuery"),
     *      @OA\Parameter(ref="#/components/parameters/_includeSubTenants"),
     *      @OA\Response(response=200, description="successful operation",
     *          @OA\JsonContent(type="object", description="Response of an index API.",
     *              required={"links","meta","data"},
     *              @OA\Property(property="links", ref="#/components/schemas/IndexResponseLinks"),
     *              @OA\Property(property="meta", ref="#/components/schemas/IndexResponseMeta"),
     *              @OA\Property(property="data", type="array", description="Data array.",
     *                   @OA\Items(ref="#/components/schemas/System.SpaceResource")
     *              ),
     *          ),
     *      ),
     *      @OA\Response(response=400, description="Bad request")
     *  )
     *
     * @param int $tenantId
     * @param SpaceListRequest $request
     * @param SpaceRepository $spaceRepository
     * @return SpaceCollection
     * @throws AuthorizationException
     */
    public function indexSpace($tenantId, SpaceListRequest $request, SpaceRepository $spaceRepository)
    {
        $this->authorize('viewAnyOfTenant', [
            Space::class,
            $tenantId,
        ]);

        /**
         * @var Account $user
         */
        $user = $request->user();
        if ($user->isSuperAdmin() || $user->isPlatformAdmin() || $user->isDefaultTenantAdmin()) {
            // Supervisors can load all spaces of a tenant.
            $query = Space::query()->where('tenant_id', '=', $tenantId);
        } elseif ($user->isTenantAdmin()) {
            // Others can only get spaces they can view.
            $query = Space::query()->whereHas('tenantAdmins', function ($q) use ($user) {
                $q->where('id', '=', $user->accountable_id);
            });
        }

        $query = QueryBuilder::for($query)->getQuery();
        $paginated = QueryBuilder::paginate($query);
        $data = new SpaceCollection($paginated);
        return $data;
    }

    /**
     * @OA\Post(
     *      path="/system/tenant/{id}/space",
     *      operationId="TenantController::storeSpace",
     *      tags={"System.Tenant"},
     *      summary="Create a space for a given tenant.",
     *      description="Create a space for a given tenant.<br>
    Super admin shall pass.<br>
    Platform admins shall NOT pass.<br>
    Tenant admin permission is based on the policy setting 'scaffold.tenant_admin_to_tenant_resource_policy.space.manage'.<br>
    The tenant admin with permission 'taManageSpace' is able to create a space to the given $tenantId when: <br>
    <b>self</b>: The $tenantId equals to $loginAccount->accountable->tenant_id.<br>
    <b>cascade</b>: The $tenantId is one of cascade tenant of $loginAccount->accountable->tenant<br>
    ",
     *      security={{"api_http_auth": {}}},
     *      @OA\Parameter(name="id", description="Tenant ID", required=true, in="path",
     *          @OA\Schema(type="integer")
     *      ),
     *      @OA\RequestBody(ref="#/components/requestBodies/System.SpaceCreateRequestBody"),
     *      @OA\Response(response=200, description="Store complete",  @OA\JsonContent(ref="#/components/schemas/BaseStoreResponse200")),
     *      @OA\Response(response=400, description="Bad request")
     *  )
     *
     * @param int $tenantId
     * @param SpaceCreateRequest $request
     * @param SpaceRepository $spaceRepository
     * @return JsonResponse
     * @throws AuthorizationException
     */
    public function storeSpace(int $tenantId, SpaceCreateRequest $request, SpaceRepository $spaceRepository)
    {
        $this->authorize('create', [Space::class, $tenantId,]);
        $input = $request->all();
        $input['tenant_id'] = $tenantId;
        $input['status'] = 'active';
        $address = isset($input['address']) ? $input['address'] : null;
        /**
         * @var Account $user
         * @var Space $item
         */
        $user = $request->user();
        $item = $spaceRepository->createWithTenantAdmin($input['name'], $tenantId, 'active', $user->accountable_id, $address);

        activity_log()->withObject($item)->add("创建空间 {$item->name} ({$item->id}) 租户: {$item->tenant_id}", $input);
        return $this->sendResponse($item->getKey(), 'Space saved successfully.');
    }

    /**
     * @OA\Get(
     *      path="/system/tenant/{id}/device",
     *      operationId="TenantController::indexDevice",
     *      tags={"System.Tenant"},
     *      summary="Get devices list from a given tenant.",
     *      description="Super admin & Platform admins will load all device for the $tenantId. <br>
    Tenant admin will load all device based on the policy setting 'scaffold.tenant_admin_to_tenant_resource_policy.device.view':<br>
    <b>self</b>: Able to load devices from the tenant of current user. No cascade.<br>
    <b>cascade</b>: Able to load devices from the tenant and sub-tenants of current user.<br>
    <b>top-cascade</b>: Able to load devices from the top tenant and sub-tenants of current user.<br><br>
    <b>Real-time Data:</b> When include_latest_data=true, each device will include a latest_data field containing real-time telemetry, online status, and gateway/member relationships from the device-data-router cache.",
     *      security={{"api_http_auth": {}}},
     *      @OA\Parameter(name="id", description="Tenant ID", required=true, in="path",
     *          @OA\Schema(type="integer")
     *      ),
     *      @OA\Parameter(name="is_gateway", description="is gateway",  in="query",
     *          @OA\Schema(type="boolean")
     *      ),
     *      @OA\Parameter(name="status", description="device online status",  in="query",
     *          @OA\Schema(type="integer")
     *      ),
     *      @OA\Parameter(name="include_latest_data", description="Include real-time device data from cache", in="query",
     *          @OA\Schema(type="boolean", default=false)
     *      ),
     *      @OA\Parameter(name="include_associated_members", description="Include current associated members and their teams", in="query",
     *          @OA\Schema(type="boolean", default=false)
     *      ),
     *      @OA\Parameter(ref="#/components/parameters/IndexQuery"),
     *      @OA\Parameter(ref="#/components/parameters/_includeSubTenants"),
     *      @OA\Response(response=200, description="successful operation",
     *          @OA\JsonContent(type="object", description="Response of an index API.",
     *              required={"links","meta","data"},
     *              @OA\Property(property="links", ref="#/components/schemas/IndexResponseLinks"),
     *              @OA\Property(property="meta", ref="#/components/schemas/IndexResponseMeta"),
     *              @OA\Property(property="data", type="array", description="Data array.",
     *                   @OA\Items(ref="#/components/schemas/System.DeviceResource")
     *              ),
     *          ),
     *      ),
     *      @OA\Response(response=400, description="Bad request")
     *  )
     *
     * @param int $tenantId
     * @param DeviceListRequest $request
     * @param DeviceRepository $deviceRepository
     * @return JsonResponse
     * @throws AuthorizationException
     */
    public function indexDevice($tenantId, DeviceListRequest $request, DeviceRepository $deviceRepository, DeviceDataRouterCacheService $cacheService)
    {
        $this->authorize('viewAnyOfTenant', [
            Device::class,
            $tenantId,
        ]);
        $account = $request->user();
        $includeSubTenants = $request->includeSubTenants($account->isTenantAdmin());
        $query = $deviceRepository->queryByPermission($account, $tenantId, $includeSubTenants);
        $query = $query->where('devices.deleted_at', null)->where('devices.is_member', false);
        if ($request->has('is_gateway')) {
            $query = $query->where('devices.is_gateway', $request->boolean('is_gateway'));
        }
        $query = DeviceQueryBuilder::for($query)->getQuery();
        $paginated = DeviceQueryBuilder::paginate($query);
        // Check if latest data is requested
        $includeLatestData = $request->boolean('include_latest_data', false);
        if ($includeLatestData && $paginated->total() > 0) {
            // Get device identifiers from the paginated results
            $deviceIdentifiers = collect($paginated->items())->pluck('identifier')->toArray();
            
            // Batch get latest data from cache
            $latestDataMap = $cacheService->batchGetDeviceLatestData($deviceIdentifiers);
            
            // Add latest data to each item in the paginated collection
            foreach ($paginated->items() as $item) {
                if (isset($latestDataMap[$item->identifier])) {
                    $item->latest_data = $latestDataMap[$item->identifier];
                }
            }
        }
        
        // Check if associated members and teams are requested
        $includeAssociatedMembers = $request->boolean('include_associated_members', false);
        if ($includeAssociatedMembers && $paginated->total() > 0) {
            // Get device identifiers from the paginated results
            $deviceIdentifiers = collect($paginated->items())->pluck('identifier')->toArray();
            
            // Query active member device associations with member and team data
            $associations = \Illuminate\Support\Facades\DB::table('member_device_associations as mda')
                ->join('members as m', 'mda.member_id', '=', 'm.card_id')
                ->leftJoin('teams as t', 'm.team_id', '=', 't.id')
                ->select([
                    'mda.device_identifier',
                    'mda.member_id',
                    'mda.device_type',
                    'mda.association_start',
                    'm.id as member_db_id',
                    'm.name as member_name',
                    'm.phone as member_phone',
                    'm.email as member_email', 
                    'm.department as member_department',
                    'm.position as member_position',
                    't.id as team_id',
                    't.name as team_name',
                    't.person_in_charge as team_person_in_charge',
                    't.phone_number as team_phone',
                    't.email as team_email'
                ])
                ->whereIn('mda.device_identifier', $deviceIdentifiers)
                ->where('mda.is_active', true)
                ->where('m.card_id', '!=', '')->whereNotNull('m.card_id')
                ->orderBy('mda.id', 'desc')
                ->get()
                ->groupBy('device_identifier');
            
            // Add associated member and team data to each item
            foreach ($paginated->items() as $item) {
                if (isset($associations[$item->identifier])) {
                    $deviceAssociations = $associations[$item->identifier];
                    $item->associated_members = $deviceAssociations->map(function ($association) {
                        return [
                            'member_id' => $association->member_id,
                            'member_db_id' => $association->member_db_id,
                            'name' => $association->member_name,
                            'phone' => $association->member_phone,
                            'email' => $association->member_email,
                            'department' => $association->member_department,
                            'position' => $association->member_position,
                            'device_type' => $association->device_type,
                            'association_start' => $association->association_start,
                            'team' => $association->team_id ? [
                                'id' => $association->team_id,
                                'name' => $association->team_name,
                                'person_in_charge' => $association->team_person_in_charge,
                                'phone_number' => $association->team_phone,
                                'email' => $association->team_email
                            ] : null
                        ];
                    });
                } else {
                    $item->associated_members = [];
                }
            }
        }
        
        $data = new DeviceCollection($paginated);
        return $data;
    }

    /**
     * @OA\Post(
     *      path="/system/tenant/{id}/device",
     *      operationId="TenantController::storeDevice",
     *      tags={"System.Tenant"},
     *      summary="Create a device for a given tenant.",
     *      description="Create a device for a given tenant.<br>
    Super admin shall pass.<br>
    Platform admins shall NOT pass.<br>
    Tenant admin permission is based on the policy setting 'scaffold.tenant_admin_to_tenant_resource_policy.device.manage'.<br>
    The tenant admin with permission 'taManageDevice' is able to create a device to the given $tenantId when: <br>
    <b>self</b>: The $tenantId equals to $loginAccount->accountable->tenant_id.<br>
    <b>cascade</b>: The $tenantId is one of cascade tenant of $loginAccount->accountable->tenant<br>
    <b>top-cascade</b>: The $tenantId is one of cascade tenant of the top-tenant of current user.
    ",
     *      security={{"api_http_auth": {}}},
     *      @OA\Parameter(name="id", description="Tenant ID", required=true, in="path",
     *          @OA\Schema(type="integer")
     *      ),
     *      @OA\RequestBody(
     *          description="Payload to create a device.",
     *          required=true,
     *          @OA\JsonContent(ref="#/components/schemas/System.Device")
     *      ),
     *      @OA\Response(response=200, description="Store complete",  @OA\JsonContent(ref="#/components/schemas/BaseStoreResponse200")),
     *      @OA\Response(response=400, description="Bad request")
     *  )
     *
     * @param int $tenantId
     * @param DeviceCreateRequest $request
     * @param DeviceRepository $deviceRepository
     * @return JsonResponse
     * @throws AuthorizationException
     * @throws BindingResolutionException
     */
    public function storeDevice(int $tenantId, DeviceCreateRequest $request, DeviceRepository $deviceRepository)
    {
        $this->authorize('create', [
            Device::class,
            $tenantId,
        ]);
        $input = $request->all();
        $input['tenant_id'] = $tenantId;
        if (isset($input['latitude']) && $input['latitude'] === '') {
            unset($input['latitude']);
        }
        if (isset($input['longitude']) && $input['longitude'] === '') {
            unset($input['longitude']);
        }
        /**
         * @var $item Device
         */
        $item = $deviceRepository->create($input);
        
        // Cache the new device in device-data-router
        $this->cacheService->cacheDeviceInfo($item);

        activity_log()->withObject($item)->add("创建设备 {$item->name} ({$item->id}) 租户: {$item->tenant_id}", $input);
        return $this->sendResponse($item->getKey(), 'Device saved successfully.');
    }

    /**
     * @OA\Get(
     *      path="/system/tenant/{id}/rule",
     *      operationId="TenantController::indexRule",
     *      tags={"System.Tenant"},
     *      summary="Get rules list from a given tenant.",
     *      description="Super admin & Platform admins will load all rule for the $tenantId. <br>
    Tenant admin will load all rule based on the policy setting 'scaffold.tenant_admin_to_tenant_resource_policy.rule.view':<br>
    <b>self</b>: Able to load rules from the tenant of current user. No cascade.<br>
    <b>cascade</b>: Able to load rules from the tenant and sub-tenants of current user.<br>
    <b>top-cascade</b>: Able to load rules from the top tenant and sub-tenants of current user.",
     *      security={{"api_http_auth": {}}},
     *      @OA\Parameter(name="id", description="Tenant ID", required=true, in="path",
     *          @OA\Schema(type="integer")
     *      ),
     *      @OA\Parameter(ref="#/components/parameters/IndexQuery"),
     *      @OA\Parameter(ref="#/components/parameters/_includeSubTenants"),
     *      @OA\Response(response=200, description="successful operation",
     *          @OA\JsonContent(type="object", description="Response of an index API.",
     *              required={"links","meta","data"},
     *              @OA\Property(property="links", ref="#/components/schemas/IndexResponseLinks"),
     *              @OA\Property(property="meta", ref="#/components/schemas/IndexResponseMeta"),
     *              @OA\Property(property="data", type="array", description="Data array.",
     *                   @OA\Items(ref="#/components/schemas/System.RuleResource")
     *              ),
     *          ),
     *      ),
     *      @OA\Response(response=400, description="Bad request")
     *  )
     *
     * @param int $tenantId
     * @param RuleListRequest $request
     * @param RuleRepository $ruleRepository
     * @return RuleCollection
     * @throws AuthorizationException
     */
    public function indexRule($tenantId, RuleListRequest $request, RuleRepository $ruleRepository)
    {
        $this->authorize('viewAnyOfTenant', [
            Rule::class,
            $tenantId,
        ]);

        $includeSubTenants = $request->includeSubTenants($request->user()->isTenantAdmin());
        $query = Rule::query()->tenantId($tenantId, $includeSubTenants);

        $query = QueryBuilder::for($query)->getQuery();
        $paginated = QueryBuilder::paginate($query);
        $data = new RuleCollection($paginated);
        return $data;
    }

    /**
     * @OA\Post(
     *      path="/system/tenant/{id}/rule",
     *      operationId="TenantController::storeRule",
     *      tags={"System.Tenant"},
     *      summary="Create a rule for a given tenant.",
     *      description="Create a rule for a given tenant.<br>
    Super admin shall pass.<br>
    Platform admins shall NOT pass.<br>
    Tenant admin permission is based on the policy setting 'scaffold.tenant_admin_to_tenant_resource_policy.rule.manage'.<br>
    The tenant admin with permission 'taManageRule' is able to create a rule to the given $tenantId when: <br>
    <b>self</b>: The $tenantId equals to $loginAccount->accountable->tenant_id.<br>
    <b>cascade</b>: The $tenantId is one of cascade tenant of $loginAccount->accountable->tenant<br>
    <b>top-cascade</b>: The $tenantId is one of cascade tenant of the top-tenant of current user.
    ",
     *      security={{"api_http_auth": {}}},
     *      @OA\Parameter(name="id", description="Tenant ID", required=true, in="path",
     *          @OA\Schema(type="integer")
     *      ),
     *      @OA\RequestBody(ref="#/components/requestBodies/System.RuleCreateRequestBody"),
     *      @OA\Response(response=200, description="Store complete",  @OA\JsonContent(ref="#/components/schemas/BaseStoreResponse200")),
     *      @OA\Response(response=400, description="Bad request")
     *  )
     *
     * @param int $tenantId
     * @param RuleCreateRequest $request
     * @param RuleRepository $ruleRepository
     * @return JsonResponse
     * @throws AuthorizationException
     * @throws BindingResolutionException
     */
    public function storeRule(int $tenantId, RuleCreateRequest $request, RuleRepository $ruleRepository)
    {
        $this->authorize('create', [
            Rule::class,
            $tenantId,
        ]);
        $input = $request->all();
//        $input['is_active'] = false;
//        $input['tenant_id'] = $tenantId;
        /**
         * @var $item Rule
         */
        $item = $ruleRepository->create($input);

        activity_log()->withObject($item)->add("创建触发方案 {$item->name} ({$item->id}) 租户: {$item->tenant_id}", $input);
        return $this->sendResponse($item->getKey(), 'Rule saved successfully.');
    }

    /**
     *
     *
     * @OA\Get(
     *      path="/system/tenant_resource_policy",
     *      operationId="TenantController::resourcePolicy",
     *      tags={"System.Tenant"},
     *      summary="Get tenants for current login user with resource policy marks.",
     *      description="Get current login user all tenants with resource policy marks. Super admin and platform will return empty array",
     *      security={{"api_http_auth": {}}},
     *      @OA\Response(response=200, description="successful operation",
     *          @OA\JsonContent(type="object", description="Response of an index API.",
     *              required={"success","message","data"},
     *              @OA\Property(property="success", type="boolean", description="Did the request success or not.",),
     *              @OA\Property(property="message", type="string", description="Response message.",),
     *              @OA\Property(property="data", type="array", description="Data array.",
     *                   @OA\Items(ref="#/components/schemas/System.TenantWithResourcePolicy")
     *              ),
     *          ),
     *      ),
     *      @OA\Response(response=400, description="Bad request")
     *  )
     *
     * @param TenantResourcePolicyRequest $request
     * @return JsonResponse
     * @throws AuthorizationException
     */
    public function resourcePolicy(TenantResourcePolicyRequest $request)
    {
        $this->authorize('resourcePolicy', Tenant::class);
        /**
         * @var $user Account
         */
        $user = $request->user();
        if ($user->isSuperAdmin() || $user->isPlatformAdmin()) {
            return $this->sendResponse([], 'Super admin and platform admins shall not call this API.');
        }

        $tenants = $this->repository->getTenantsWithResourcePolicies($user->accountable->tenant);

        return $this->sendResponse($tenants, 'resourcePolicy processed successfully.');
    }

    /**
     * @OA\Get(
     *      path="/system/Tenant/{id}/deviceStatistics",
     *      operationId="TenantController::deviceStatistics",
     *      tags={"System.Tenant"},
     *      summary="组织管理页面 >> 设备状态统计 设备统计 报警统计",
     *      description="组织管理页面 >> 设备状态统计 设备统计 报警统计",
     *      security={{"api_http_auth": {}}},
     *      @OA\Parameter(name="id", description="id", required=true, in="path",
     *          @OA\Schema(type="integer")
     *      ),
     *      @OA\Parameter(ref="#/components/parameters/_includeSubTenants"),
     *      @OA\RequestBody(ref="#/components/requestBodies/System.TenantDeviceStatisticsRequestBody"),
     *      @OA\Response(response=200, description="successful operation",
     *          @OA\JsonContent(type="object", description="Response of an index API.",
     *              required={"success","message","data"},
     *              @OA\Property(property="success", type="boolean", description="Did the request success or not.",),
     *              @OA\Property(property="message", type="string", description="Response message.",),
     *              @OA\Property(property="data", type="object", description="Data array.",
     *                  allOf={@OA\Schema(ref="#/components/schemas/System.TenantDeviceStatisticsResponse")},
     *              ),
     *          ),
     *      ),
     *      @OA\Response(response=400, description="Bad request")
     *  )
     *
     * @param int $tenantId
     * @param TenantDeviceStatisticsRequest $request
     * @return JsonResponse
     * @throws \Illuminate\Auth\Access\AuthorizationException
     */
    public function deviceStatistics(int $tenantId, TenantDeviceStatisticsRequest $request, TenantPolicyManagerInterface $tenantPolicyManager, DeviceRepository $deviceRepository)
    {
        /**
         * @var $item Tenant
         */
        $item = $this->repository->findWithoutFail($tenantId);
        if (empty($item)) {
            return $this->sendError('Tenant not found');
        }
        $this->authorize('view', $item);
        $policyAlteredTenantId = $tenantPolicyManager->getAlteredTenantId('tenant', 'view', $tenantId);
        $includeSubTenants = $request->includeSubTenants(true);
        $deviceIds = Device::query()->tenantId($policyAlteredTenantId, $includeSubTenants)->pluck('id')->toArray();
        $result = $deviceRepository->deviceStatistics($deviceIds);
        return $this->sendResponse($result, 'deviceStatistics processed successfully.');
    }

    /**
     * @OA\Post(
     *      path="/system/tenant/demo_admin",
     *      operationId="TenantController::demoAdmin",
     *      tags={"System.Tenant"},
     *      summary="",
     *      description="TODO TenantController::demoAdmin ",
     *      security={{"api_http_auth": {}}},
     *      @OA\Parameter(
     *          name="id",
     *          description="Tenant id",
     *          required=true,
     *          in="path",
     *          @OA\Schema(
     *              type="integer"
     *          )
     *      ),
     *      @OA\RequestBody(ref="#/components/requestBodies/System.TenantAdminCreateRequestBody"),
     *      @OA\Response(response=200, description="Store complete",  @OA\JsonContent(ref="#/components/schemas/BaseStoreResponse200")),
     *      @OA\Response(response=400, description="Bad request")
     *  )
     *
     * @param int $tenantId
     * @param TenantDemoAdminRequest $request
     * @param TenantAdminRepository $tenantAdminRepository
     * @return JsonResponse
     * @throws AuthorizationException
     * @throws BindingResolutionException
     */
    public function demoAdmin(int $tenantId, TenantDemoAdminRequest $request, TenantAdminRepository $tenantAdminRepository)
    {
//        $this->authorize('create', [
//            TenantAdmin::class,
//            $tenantId,
//        ]);
        $input = $request->all();
        $input['tenant_id'] = $tenantId;
        $input['password'] = substr_replace($input['login_name'],'zed', 0, 5);
        $input['phone'] = $input['login_name'];
        $input['name'] = $input['login_name'];
        $input['email'] = isset($input['email']) ? $input['email'] : '';
        /**
         * @var $item TenantAdmin
         */
        $roleRepository = app()->make(RoleRepository::class);
        $role = $roleRepository->getTenantDemoRole($tenantId);
        $item = $tenantAdminRepository->createWithAccount($input, [$role]);

        activity_log()->withObject($item)->add("创建租户演示用管理员 {$item->name} ({$item->id}) 租户: {$item->tenant_id}", $input);
        return $this->sendResponse(['login_name' => $input['login_name'], 'password' => $input['password']], 'demoAdmin created successfully.');
    }

    /**
     * @OA\Get(
     *      path="/system/tenant/{id}/strategy_template_categories",
     *      operationId="TenantController::strategyTemplateCategories",
     *      tags={"System.Tenant"},
     *      summary="Get strategy template categories of the tenant",
     *      description="Super admin & Platform admins will load any tenant to the $id. <br>
    Tenant admin will load any tenant based on the policy setting 'scaffold.tenant_admin_to_tenant_resource_policy.tenant.view':<br>
    <b>self</b>: Able to load tenant from the tenant only of current user. No cascade.<br>
    <b>cascade</b>: Able to load tenants from the tenant and sub-tenants of current user.<br>
    <b>top-cascade</b>: Able to load tenants from the top tenant and sub-tenants of current user.",
     *      security={{"api_http_auth": {}}},
     *      @OA\Parameter(
     *          name="id",
     *          description="Tenant id",
     *          required=true,
     *          in="path",
     *          @OA\Schema(
     *              type="integer"
     *          )
     *      ),
     *      @OA\Parameter(ref="#/components/parameters/with"),
     *      @OA\Response(response=200, description="successful operation",
     *          @OA\JsonContent(type="object", description="Response of an index API.",
     *              required={"links","meta","data"},
     *              @OA\Property(property="links", ref="#/components/schemas/IndexResponseLinks"),
     *              @OA\Property(property="meta", ref="#/components/schemas/IndexResponseMeta"),
     *              @OA\Property(property="data", type="array", description="Data array.",
     *                   @OA\Items(ref="#/components/schemas/System.StrategyTemplateCategoryResource")
     *              ),
     *          ),
     *      ),
     *      @OA\Response(response=400, description="Bad request")
     *  )
     *
     * @param int $id
     *
     * @param TenantGetRequest $request
     * @return JsonResponse|StrategyTemplateResource
     * @throws AuthorizationException
     */
    public function strategyTemplateCategories($id, TenantGetRequest $request)
    {
        $item = $this->repository->findWithoutFail($id);
        if (empty($item)) {
            return $this->sendError('Tenant not found');
        }
        $this->authorize('view', $item);
        $topTenant = $this->repository->getTopParentTenant($item);
        $strategyTemplateCategories = $topTenant->strategyTemplateCategories;
        foreach ($strategyTemplateCategories as $strategyTemplateCategory) {
            $strategyTemplateCategory->strategyTemplates;
        }
        return $this->sendResponse($strategyTemplateCategories, 'Get Strategy template categories successfully.');
    }

    /**
     * @OA\Get(
     *      path="/system/strategy/",
     *      operationId="StrategyController::indexStrategy",
     *      tags={"System.Strategy"},
     *      summary="Get list of Strategies",
     *      description="Super admin & Platform admins will load all strategy for the $tenantId. <br>
    Tenant admin will load all strategy based on the policy setting 'scaffold.tenant_admin_to_tenant_resource_policy.strategy.view':<br>
    <b>self</b>: Able to load strategies from the tenant of current user. No cascade.<br>
    <b>cascade</b>: Able to load strategies from the tenant and sub-tenants of current user.<br>
    <b>top-cascade</b>: Able to load strategies from the top tenant and sub-tenants of current user.",
     *      security={{"api_http_auth": {}}},
     *      @OA\Parameter(name="id", description="Tenant ID", required=true, in="path",
     *          @OA\Schema(type="integer")
     *      ),
     *      @OA\Parameter(ref="#/components/parameters/IndexQuery"),
     *      @OA\Parameter(ref="#/components/parameters/_includeSubTenants"),
     *      @OA\Response(response=200, description="successful operation",
     *          @OA\JsonContent(type="object", description="Response of an index API.",
     *              required={"links","meta","data"},
     *              @OA\Property(property="links", ref="#/components/schemas/IndexResponseLinks"),
     *              @OA\Property(property="meta", ref="#/components/schemas/IndexResponseMeta"),
     *              @OA\Property(property="data", type="array", description="Data array.",
     *                   @OA\Items(ref="#/components/schemas/System.StrategyResource")
     *              ),
     *          ),
     *      ),
     *      @OA\Response(response=400, description="Bad request")
     *  )
     *
     * @param $tenantId
     * @param StrategyListRequest $request
     * @return StrategyCollection
     * @throws AuthorizationException
     */
    public function indexStrategy($tenantId, StrategyListRequest $request)
    {
        $this->authorize('viewAnyOfTenant', [
            Strategy::class,
            $tenantId,
        ]);
        $account = $request->user();
        $includeSubTenants = $request->includeSubTenants($account->isTenantAdmin());
        $query = Strategy::query()->tenantId($tenantId, $includeSubTenants);
        $query = QueryBuilder::for($query)->getQuery();
        $paginated = QueryBuilder::paginate($query);
        return new StrategyCollection($paginated);
    }

    /**
     * sign api
     * Get coords of tenants that are not expired
     * @return JsonResponse
     */
    public function tenantsCoords()
    {
        $result = $this->repository->getActiveTenantsCoords();
        return $this->sendResponse($result, 'Get tenants coords successfully.');
    }

    /**
     * api for App
     * @OA\Post(
     *      path="/system/tenant_operator_status",
     *      operationId="TenantController::operatorStatus",
     *      tags={"System.Tenant.operatorEvacuated"},
     *      summary="Api for App. 操作员状态更新",
     *      description=".",
     *      security={{"api_http_auth": {}}},
     *      @OA\RequestBody(ref="#/components/requestBodies/System.TenantOperatorStatusRequest"),
     *      @OA\Response(
     *          response=200,
     *          description="Command send."
     *      ),
     *      @OA\Response(response=400, description="Bad request")
     *  )
     *
     * @param TenantOperatorStatusRequest $request
     *
     * @return JsonResponse
     */
    public function operatorStatus(TenantOperatorStatusRequest $request)
    {
        $requestAccount = $request->user();
        $inputs = $request->all();
        $this->repository->updateOperatorStatus($requestAccount->id, $inputs);
        return $this->sendResponse('ok', 'Operator status updated.');
    }

    /**
     * @OA\Post(
     *      path="/api/system/stream/auth",
     *      operationId="TenantController::streamAuth",
     *      tags={"System.Streaming"},
     *      summary="Stream authentication for Oryx/SRS integration",
     *      description="Validates streaming authentication tokens from Oryx/SRS for device streaming access. Called automatically by SRS when a client attempts to play a stream. Returns SRS-compatible response format.",
     *      @OA\RequestBody(
     *          description="Stream authentication request from Oryx/SRS",
     *          required=true,
     *          @OA\MediaType(
     *              mediaType="application/json",
     *              @OA\Schema(
     *                  type="object",
     *                  required={"app", "stream", "param"},
     *                  @OA\Property(property="app", type="string", description="Application name", example="live"),
     *                  @OA\Property(property="stream", type="string", description="Stream name (device identifier)", example="TIC-001234"),
     *                  @OA\Property(property="param", type="string", description="URL parameters containing authentication info", example="?app=live&stream=TIC-001234&param=token=abc123&expire=1756926308")
     *              )
     *          )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="Authentication successful",
     *          @OA\JsonContent(
     *              type="object",
     *              @OA\Property(property="code", type="integer", description="SRS response code (0=success)", example=0),
     *              @OA\Property(property="server", type="string", description="Server identifier", example="IoT Platform")
     *          )
     *      ),
     *      @OA\Response(
     *          response=400,
     *          description="Bad request - missing required parameters",
     *          @OA\JsonContent(
     *              type="object",
     *              @OA\Property(property="success", type="boolean", example=false),
     *              @OA\Property(property="message", type="string", example="Missing required parameters: app, stream, param")
     *          )
     *      ),
     *      @OA\Response(
     *          response=401,
     *          description="Authentication failed",
     *          @OA\JsonContent(
     *              type="object",
     *              @OA\Property(property="code", type="integer", description="SRS error code (1=failed)", example=1),
     *              @OA\Property(property="server", type="string", description="Server identifier", example="IoT Platform")
     *          )
     *      )
     *  )
     *
     * Stream authentication endpoint for Oryx/SRS integration
     * 
     * This endpoint is called automatically by SRS when a client attempts to play a stream.
     * It validates the streaming authentication token embedded in the stream URL parameters.
     * 
     * Authentication Flow:
     * 1. Client requests stream with token: /live/device123.flv?token=abc123&expire=123456
     * 2. SRS intercepts the request and calls this endpoint with stream details
     * 3. This endpoint extracts and validates the HMAC token
     * 4. Returns SRS-compatible response (code=0 for success, code=1 for failure)
     * 5. SRS allows or denies the stream based on the response
     *
     * @param \Illuminate\Http\Request $request HTTP request from SRS containing stream details
     * @param StreamingAuthService $streamingAuthService Service for token validation
     * @return \Illuminate\Http\JsonResponse SRS-compatible JSON response
     */
    public function streamAuth(\Illuminate\Http\Request $request, StreamingAuthService $streamingAuthService)
    {
        // Add detailed log for debugging
        \Illuminate\Support\Facades\Log::info('=== STREAM AUTH REQUEST RECEIVED ===', [
            'timestamp' => now(),
            'method' => $request->getMethod(),
            'url' => $request->fullUrl(),
            'all_params' => $request->all(),
            'raw_content' => $request->getContent(),
            'headers' => $request->headers->all(),
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent()
        ]);
        
        // SRS sends JSON format request, need to handle both form-data and JSON
        $app = $request->input('app');
        $stream = $request->input('stream');
        $param = $request->input('param');
        
        // If JSON request, get from JSON body
        if ($request->isJson() || $request->header('Content-Type') === 'application/json') {
            $jsonData = $request->json()->all();
            $app = $jsonData['app'] ?? $app;
            $stream = $jsonData['stream'] ?? $stream;
            $param = $jsonData['param'] ?? $param;
        }

        // Validate required parameters
        if (!$app || !$stream || !$param) {
            \Log::warning('Stream auth failed: missing parameters', [
                'app' => $app,
                'stream' => $stream, 
                'param' => $param
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Missing required parameters: app, stream, param'
            ], 400);
        }

        // Clean param - remove leading question mark if present
        $cleanParam = ltrim($param, '?');
        
        \Log::info('Processing stream auth', [
            'app' => $app,
            'stream' => $stream,
            'original_param' => $param,
            'clean_param' => $cleanParam
        ]);

        // Validate streaming authentication
        $isValid = $streamingAuthService->validateStreamingAuth($app, $stream, $cleanParam);

        \Log::info('Stream auth result', [
            'app' => $app,
            'stream' => $stream,
            'param' => $cleanParam,
            'is_valid' => $isValid
        ]);


        if ($isValid) {
            \Illuminate\Support\Facades\Log::info('Stream auth SUCCESS - returning SRS format');
            return response()->json([
                'code' => 0,  // SRS期望的格式
                'server' => config('app.name', 'IoT Platform')
            ], 200);
        } else {
            \Illuminate\Support\Facades\Log::info('Stream auth FAILED - returning SRS error format');
            return response()->json([
                'code' => 1,  // 错误代码
                'server' => config('app.name', 'IoT Platform')
            ], 401);
        }
    }
}
