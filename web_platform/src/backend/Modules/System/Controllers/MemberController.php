<?php

namespace Modules\System\Controllers;

use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Scaffold\QueryBuilder\QueryBuilder;
use Scaffold\BaseController;
use Modules\System\Requests\Member\MemberUpdateRequest;
use Modules\System\Requests\Member\MemberListRequest;
use Modules\System\Requests\Member\MemberGetRequest;
use Modules\System\Requests\Member\MemberDeleteRequest;
use Modules\System\Requests\Member\MemberCreateRequest;
use Modules\System\Repositories\MemberRepository;
use Modules\System\Resources\MemberResource;
use Modules\System\Resources\MemberCollection;
use Modules\System\Models\Member;
use Modules\System\Models\Team;
use Modules\System\Models\TenantAdmin;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
class MemberController extends BaseController
{
    public function __construct(MemberRepository $repository)
    {
        $this->repository = $repository;
    }
    /**
     * @OA\Get(
     *      path="/system/member",
     *      operationId="MemberController::index",
     *      tags={"System.Member"},
     *      summary="Get list of Members",
     *      description="Returns list of Members",
     *      security={{"api_http_auth": {}}},
     *      @OA\Parameter(ref="#/components/parameters/IndexQuery"),
     *      @OA\Response(response=200, description="successful operation",
     *          @OA\JsonContent(type="object", description="Response of an index API.",
     *              required={"links","meta","data"},
     *              @OA\Property(property="links", ref="#/components/schemas/IndexResponseLinks"),
     *              @OA\Property(property="meta", ref="#/components/schemas/IndexResponseMeta"),
     *              @OA\Property(property="data", type="array", description="Data array.",
     *                   @OA\Items(ref="#/components/schemas/System.MemberResource")
     *              ),
     *          ),
     *      ),
     *      @OA\Response(response=400, description="Bad request")
     *  )
     *
     * @param MemberListRequest $request
     * @return MemberCollection
     * @throws \Illuminate\Auth\Access\AuthorizationException
     */
    public function index(MemberListRequest $request)
    {
        $this->authorize('viewAny', Member::class);
        $query = QueryBuilder::for(Member::class)->getQuery();
        $paginated = QueryBuilder::paginate($query);
        $data = new MemberCollection($paginated);
        return $data;
    }
    /**
     * @OA\Post(
     *      path="/system/member",
     *      operationId="MemberController::store",
     *      tags={"System.Member"},
     *      summary="Store a newly created Member in storage.",
     *      description="Store a new Member",
     *      security={{"api_http_auth": {}}},
     *      @OA\RequestBody(
     *          description="Payload to create Member",
     *          required=true,
     *          @OA\JsonContent(ref="#/components/schemas/System.Member")
     *      ),
     *      @OA\Response(response=200, description="Store complete",  @OA\JsonContent(ref="#/components/schemas/BaseStoreResponse200")),
     *      @OA\Response(response=400, description="Bad request")
     *  )
     *
     * @param MemberCreateRequest $request
     * @return JsonResponse
     * @throws \Illuminate\Auth\Access\AuthorizationException
     */
    public function store(MemberCreateRequest $request)
    {
        $this->authorize('create', Member::class);
        $input = $request->all();
        /**
         * @var $item Member
         */
        $item = $this->repository->create($input);
        activity_log()->withObject($item)->add('创建 Member', $input);
        return $this->sendResponse($item->getKey(), 'Member saved successfully.');
    }
    /**
     * @OA\Get(
     *      path="/system/member/{id}",
     *      operationId="MemberController::show",
     *      tags={"System.Member"},
     *      summary="Display the specified Member",
     *      description="Get one Member by id",
     *      security={{"api_http_auth": {}}},
     *      @OA\Parameter(
     *          name="id",
     *          description="Member id",
     *          required=true,
     *          in="path",
     *          @OA\Schema(
     *              type="integer"
     *          )
     *      ),
     *      @OA\Parameter(ref="#/components/parameters/with"),
     *      @OA\Response(response=200, description="successful operation",
     *          @OA\JsonContent(type="object", description="Fetched record.",
     *              required={"data"},
     *              @OA\Property(property="data", ref="#/components/schemas/System.MemberResource")
     *          ),
     *      ),
     *      @OA\Response(response=400, description="Bad request")
     *  )
     *
     * @param int $id
     *
     * @param MemberGetRequest $request
     * @return JsonResponse|MemberResource
     * @throws \Illuminate\Auth\Access\AuthorizationException
     */
    public function show($id, MemberGetRequest $request)
    {
        /**
         * @var $item Member
         */
        $item = $this->repository->findWithoutFail($id);
        if (empty($item)) {
            return $this->sendError('Member not found');
        }
        $this->authorize('view', $item);
        $item->load($request->getWith());
        return new MemberResource($item);
    }
    /**
     * @OA\Put(
     *      path="/system/member/{id}",
     *      operationId="MemberController::update",
     *      tags={"System.Member"},
     *      summary="Update the specified Member in storage.",
     *      description="Update one Member by id",
     *      security={{"api_http_auth": {}}},
     *      @OA\Parameter(
     *          name="id",
     *          description="Member id",
     *          required=true,
     *          in="path",
     *          @OA\Schema(
     *              type="integer"
     *          )
     *      ),
     *      @OA\RequestBody(
     *          description="Payload to update Member",
     *          required=true,
     *          @OA\JsonContent(ref="#/components/schemas/System.Member")
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation"
     *      ),
     *      @OA\Response(response=400, description="Bad request")
     *  )
     *
     * @param int $id
     * @param MemberUpdateRequest $request
     *
     * @return JsonResponse
     * @throws \Illuminate\Auth\Access\AuthorizationException
     */
    public function update($id, MemberUpdateRequest $request)
    {
        /**
         * @var $item Member
         */
        $item = $this->repository->findWithoutFail($id);
        if (empty($item)) {
            return $this->sendError('Member not found');
        }
        $this->authorize('update', $item);
        $input = $request->all();
        $this->repository->update($input, $id);
        activity_log()->withObject($item)->add('修改 Member', $input);
        return $this->sendResponse(null, 'Member updated successfully.');
    }
    /**
     * @OA\Delete(
     *      path="/system/member/{id}",
     *      operationId="MemberController::destroy",
     *      tags={"System.Member"},
     *      summary="Remove the specified Member from storage.",
     *      description="Delete one Member by id",
     *      security={{"api_http_auth": {}}},
     *      @OA\Parameter(
     *          name="id",
     *          description="Member id",
     *          required=true,
     *          in="path",
     *          @OA\Schema(
     *              type="integer"
     *          )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation"
     *      ),
     *      @OA\Response(response=400, description="Bad request")
     *  )
     *
     * @param int $id
     *
     * @param MemberDeleteRequest $request
     * @return JsonResponse
     * @throws \Illuminate\Auth\Access\AuthorizationException
     */
    public function destroy($id, MemberDeleteRequest $request)
    {
        /**
         * @var $item Member
         */
        $item = $this->repository->findWithoutFail($id);
        if (empty($item)) {
            return $this->sendError('Member not found');
        }
        $this->authorize('delete', $item);
        $this->repository->delete($id);
        activity_log()->withObject($item)->add('删除 Member', $item->toArray());
        return $this->sendResponse(null, 'Member deleted successfully.');
    }

    /**
     * @OA\Post(
     *      path="/system/member/batch_delete",
     *      operationId="MemberController::batchDestroy",
     *      tags={"System.Member"},
     *      summary="Batch delete specified Members",
     *      description="Delete multiple members by ID array",
     *      security={{"api_http_auth": {}}},
     *      @OA\RequestBody(
     *          description="Member IDs to delete",
     *          required=true,
     *          @OA\JsonContent(
     *              type="object",
     *              @OA\Property(property="ids", type="array", description="Array of Member IDs to delete", @OA\Items(type="integer"))
     *          )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="Members deleted successfully"
     *      ),
     *      @OA\Response(response=400, description="Bad request")
     *  )
     *
     * @param MemberListRequest $request
     * @return JsonResponse
     * @throws \Illuminate\Auth\Access\AuthorizationException
     */
    public function batchDestroy(MemberListRequest $request)
    {
        $ids = $request->input('ids');
        if (empty($ids) || !is_array($ids)) {
            return $this->sendError('Invalid input: ids array is required');
        }
        $items = $this->repository->findWhereInIds($ids);
        if ($items->isEmpty()) {
            return $this->sendError('No Members found');
        }
        foreach ($items as $item) {
            $this->authorize('delete', $item);
        }
        $deletedCount = $this->repository->deleteWhereIn($request->ids);
        activity_log()->add('批量删除 Member', $request->ids);
        return $this->sendResponse($deletedCount, 'Members deleted successfully.');
    }

    /**
     * @OA\Post(
     *      path="/system/member/batch_assign_team",
     *      operationId="MemberController::batchAssignTeam",
     *      tags={"System.Member"},
     *      summary="Batch assign members to a team",
     *      description="Assign multiple members to a specific team. Validates that members and team belong to the same tenant as current user.",
     *      security={{"api_http_auth": {}}},
     *      @OA\RequestBody(
     *          description="Member IDs and Team ID for batch assignment",
     *          required=true,
     *          @OA\JsonContent(
     *              type="object",
     *              required={"member_ids", "team_id"},
     *              @OA\Property(property="member_ids", type="array", description="Array of Member IDs to assign", @OA\Items(type="integer")),
     *              @OA\Property(property="team_id", type="integer", description="Team ID to assign members to")
     *          )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="Members assigned to team successfully",
     *          @OA\JsonContent(
     *              type="object",
     *              @OA\Property(property="success", type="boolean", example=true),
     *              @OA\Property(property="data", type="object",
     *                  @OA\Property(property="assigned_count", type="integer", description="Number of members assigned"),
     *                  @OA\Property(property="team_name", type="string", description="Name of the target team")
     *              ),
     *              @OA\Property(property="message", type="string", example="Members assigned to team successfully.")
     *          )
     *      ),
     *      @OA\Response(
     *          response=400,
     *          description="Invalid input or tenant mismatch",
     *          @OA\JsonContent(
     *              type="object",
     *              @OA\Property(property="success", type="boolean", example=false),
     *              @OA\Property(property="message", type="string", example="Invalid input or unauthorized access")
     *          )
     *      ),
     *      @OA\Response(response=403, description="Forbidden - insufficient permissions"),
     *      @OA\Response(response=404, description="Team or members not found")
     *  )
     *
     * @param MemberListRequest $request
     * @return JsonResponse
     * @throws \Illuminate\Auth\Access\AuthorizationException
     */
    public function batchAssignTeam(MemberListRequest $request)
    {
        $memberIds = $request->input('member_ids');
        $teamId = $request->input('team_id');
        
        // Validate input
        if (empty($memberIds) || !is_array($memberIds)) {
            return $this->sendError('Invalid input: member_ids array is required');
        }
        
        if (empty($teamId) || !is_numeric($teamId)) {
            return $this->sendError('Invalid input: team_id is required');
        }

        // Get current user and tenant info
        $currentUser = auth()->user();
        if (!$currentUser) {
            return $this->sendError('Unauthorized: User not authenticated');
        }
        
        // Check if user is tenant admin
        if (!$currentUser->accountable || !is_a($currentUser->accountable, TenantAdmin::class)) {
            return $this->sendError('Unauthorized: Only tenant admin can perform this operation');
        }
        
        $currentTenantId = $currentUser->accountable->tenant_id;

        // Find and validate team
        $team = Team::find($teamId);
        if (!$team) {
            return $this->sendError('Team not found');
        }
        
        // Check team tenant authorization
        $this->authorize('view', $team);
        
        if ($team->tenant_id !== $currentTenantId) {
            return $this->sendError('Unauthorized: Team does not belong to your tenant');
        }

        // Find and validate members
        $members = $this->repository->findWhereInIds($memberIds);
        if ($members->isEmpty()) {
            return $this->sendError('No Members found');
        }

        // Check authorization for each member and validate tenant
        $invalidMembers = [];
        foreach ($members as $member) {
            $this->authorize('update', $member);
            
            if ($member->tenant_id !== $currentTenantId) {
                $invalidMembers[] = [
                    'id' => $member->id,
                    'name' => $member->name,
                    'tenant_id' => $member->tenant_id
                ];
            }
        }

        // If any member doesn't belong to current tenant, return error
        if (!empty($invalidMembers)) {
            return $this->sendError(
                'Unauthorized: Some members do not belong to your tenant',
                ['invalid_members' => $invalidMembers]
            );
        }

        // Perform batch assignment in transaction
        $assignedCount = 0;
        DB::transaction(function () use ($memberIds, $teamId, &$assignedCount) {
            $assignedCount = Member::whereIn('id', $memberIds)->update(['team_id' => $teamId]);
        });

        // Log the activity
        activity_log()->withObject($team)->add(
            "批量分配 Member 到 Team: {$team->name}",
            [
                'member_ids' => $memberIds,
                'team_id' => $teamId,
                'assigned_count' => $assignedCount
            ]
        );

        return $this->sendResponse([
            'assigned_count' => $assignedCount,
            'team_name' => $team->name
        ], 'Members assigned to team successfully.');
    }
}