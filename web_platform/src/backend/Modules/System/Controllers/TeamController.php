<?php

namespace Modules\System\Controllers;

use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Scaffold\QueryBuilder\QueryBuilder;
use Scaffold\BaseController;
use Modules\System\Requests\Team\TeamUpdateRequest;
use Modules\System\Requests\Team\TeamListRequest;
use Modules\System\Requests\Team\TeamGetRequest;
use Modules\System\Requests\Team\TeamDeleteRequest;
use Modules\System\Requests\Team\TeamCreateRequest;
use Modules\System\Repositories\TeamRepository;
use Modules\System\Resources\TeamResource;
use Modules\System\Resources\TeamCollection;
use Modules\System\Models\Team;
use Modules\System\Models\Member;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
class TeamController extends BaseController
{
    public function __construct(TeamRepository $repository)
    {
        $this->repository = $repository;
    }
    /**
     * @OA\Get(
     *      path="/system/team",
     *      operationId="TeamController::index",
     *      tags={"System.Team"},
     *      summary="Get list of Teams",
     *      description="Returns list of Teams",
     *      security={{"api_http_auth": {}}},
     *      @OA\Parameter(ref="#/components/parameters/IndexQuery"),
     *      @OA\Response(response=200, description="successful operation",
     *          @OA\JsonContent(type="object", description="Response of an index API.",
     *              required={"links","meta","data"},
     *              @OA\Property(property="links", ref="#/components/schemas/IndexResponseLinks"),
     *              @OA\Property(property="meta", ref="#/components/schemas/IndexResponseMeta"),
     *              @OA\Property(property="data", type="array", description="Data array.",
     *                   @OA\Items(ref="#/components/schemas/System.TeamResource")
     *              ),
     *          ),
     *      ),
     *      @OA\Response(response=400, description="Bad request")
     *  )
     *
     * @param TeamListRequest $request
     * @return TeamCollection
     * @throws \Illuminate\Auth\Access\AuthorizationException
     */
    public function index(TeamListRequest $request)
    {
        $this->authorize('viewAny', Team::class);
        $query = QueryBuilder::for(Team::class)->getQuery();
        $paginated = QueryBuilder::paginate($query);
        $data = new TeamCollection($paginated);
        return $data;
    }
    /**
     * @OA\Post(
     *      path="/system/team",
     *      operationId="TeamController::store",
     *      tags={"System.Team"},
     *      summary="Store a newly created Team in storage.",
     *      description="Store a new Team",
     *      security={{"api_http_auth": {}}},
     *      @OA\RequestBody(
     *          description="Payload to create Team",
     *          required=true,
     *          @OA\JsonContent(ref="#/components/schemas/System.Team")
     *      ),
     *      @OA\Response(response=200, description="Store complete",  @OA\JsonContent(ref="#/components/schemas/BaseStoreResponse200")),
     *      @OA\Response(response=400, description="Bad request")
     *  )
     *
     * @param TeamCreateRequest $request
     * @return JsonResponse
     * @throws \Illuminate\Auth\Access\AuthorizationException
     */
    public function store(TeamCreateRequest $request)
    {
        $this->authorize('create', Team::class);
        $input = $request->all();
        /**
         * @var $item Team
         */
        $item = $this->repository->create($input);
        activity_log()->withObject($item)->add('创建 Team', $input);
        return $this->sendResponse($item->getKey(), 'Team saved successfully.');
    }
    /**
     * @OA\Get(
     *      path="/system/team/{id}",
     *      operationId="TeamController::show",
     *      tags={"System.Team"},
     *      summary="Display the specified Team",
     *      description="Get one Team by id",
     *      security={{"api_http_auth": {}}},
     *      @OA\Parameter(
     *          name="id",
     *          description="Team id",
     *          required=true,
     *          in="path",
     *          @OA\Schema(
     *              type="integer"
     *          )
     *      ),
     *      @OA\Parameter(ref="#/components/parameters/with"),
     *      @OA\Response(response=200, description="successful operation",
     *          @OA\JsonContent(type="object", description="Fetched record.",
     *              required={"data"},
     *              @OA\Property(property="data", ref="#/components/schemas/System.TeamResource")
     *          ),
     *      ),
     *      @OA\Response(response=400, description="Bad request")
     *  )
     *
     * @param int $id
     *
     * @param TeamGetRequest $request
     * @return JsonResponse|TeamResource
     * @throws \Illuminate\Auth\Access\AuthorizationException
     */
    public function show($id, TeamGetRequest $request)
    {
        /**
         * @var $item Team
         */
        $item = $this->repository->findWithoutFail($id);
        if (empty($item)) {
            return $this->sendError('Team not found');
        }
        $this->authorize('view', $item);
        $item->load($request->getWith());
        return new TeamResource($item);
    }
    /**
     * @OA\Put(
     *      path="/system/team/{id}",
     *      operationId="TeamController::update",
     *      tags={"System.Team"},
     *      summary="Update the specified Team in storage.",
     *      description="Update one Team by id",
     *      security={{"api_http_auth": {}}},
     *      @OA\Parameter(
     *          name="id",
     *          description="Team id",
     *          required=true,
     *          in="path",
     *          @OA\Schema(
     *              type="integer"
     *          )
     *      ),
     *      @OA\RequestBody(
     *          description="Payload to update Team and batch manage members",
     *          required=true,
     *          @OA\JsonContent(
     *              allOf={
     *                  @OA\Schema(ref="#/components/schemas/System.Team"),
     *                  @OA\Schema(
     *                      type="object",
     *                      @OA\Property(
     *                          property="members", 
     *                          type="array", 
     *                          description="Complete replacement mode: Array of member objects to create/update/associate with this team. Only members in this array will remain associated with the team. Members not in this array will have their team association removed. Pass empty array [] to remove all member associations. Omit this field to leave member associations unchanged.",
     *                          @OA\Items(
     *                              type="object",
     *                              @OA\Property(property="id", type="integer", description="Member ID for updating existing member. Omit for creating new member."),
     *                              @OA\Property(property="card_id", type="string", description="Card ID", maxLength=100),
     *                              @OA\Property(property="name", type="string", description="Member name (required for new members)", maxLength=100),
     *                              @OA\Property(property="phone", type="string", description="Phone number", maxLength=20),
     *                              @OA\Property(property="email", type="string", format="email", description="Email address", maxLength=100),
     *                              @OA\Property(property="department", type="string", description="Department", maxLength=100),
     *                              @OA\Property(property="position", type="string", description="Job position", maxLength=100),
     *                              @OA\Property(property="status", type="integer", enum={0, 1}, description="Status: 1=active, 0=inactive"),
     *                              @OA\Property(property="metadata", type="object", description="Additional metadata")
     *                          ),
     *                          example={
     *                              {
     *                                  "id": 1,
     *                                  "name": "张三",
     *                                  "phone": "13800138000",
     *                                  "department": "技术部"
     *                              },
     *                              {
     *                                  "name": "李四",
     *                                  "card_id": "EMP002",
     *                                  "email": "<EMAIL>"
     *                              }
     *                          }
     *                      ),
     *                      @OA\Property(
     *                          property="member_ids", 
     *                          type="array", 
     *                          description="Legacy support: Simple array of member IDs to associate with this team. Uses complete replacement mode - only these members will remain associated. Cannot be used together with 'members' array.",
     *                          @OA\Items(type="integer"),
     *                          example={1, 2, 3}
     *                      )
     *                  )
     *              }
     *          )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation"
     *      ),
     *      @OA\Response(response=400, description="Bad request")
     *  )
     *
     * @param int $id
     * @param TeamUpdateRequest $request
     *
     * @return JsonResponse
     * @throws \Illuminate\Auth\Access\AuthorizationException
     */
    public function update($id, TeamUpdateRequest $request)
    {
        /**
         * @var $item Team
         */
        $item = $this->repository->findWithoutFail($id);
        if (empty($item)) {
            return $this->sendError('Team not found');
        }
        $this->authorize('update', $item);
        
        $input = $request->all();
        $members = $input['members'] ?? null;
        $memberIds = $input['member_ids'] ?? null; // Legacy support
        
        // Remove members and member_ids from input as they're not Team fields
        unset($input['members'], $input['member_ids']);
        
        $memberOperations = [];
        
        DB::transaction(function () use ($input, $id, $members, $memberIds, $item, &$memberOperations) {
            // Update team basic information
            $this->repository->update($input, $id);
            
            // Handle complex member operations if members array is provided
            if ($members !== null) {
                $memberOperations = $this->processMembersBatchOperations($id, $members, $item->tenant_id);
            }
            // Handle simple member association if member_ids is provided (legacy support)
            elseif ($memberIds !== null) {
                // First, remove current team association for all existing members
                Member::where('team_id', $id)->update(['team_id' => null]);
                
                // Then, assign new members to this team
                if (!empty($memberIds)) {
                    Member::whereIn('id', $memberIds)->update(['team_id' => $id]);
                }
                $memberOperations['associated'] = $memberIds;
            }
        });
        
        // Log the activity with member information if provided
        $logData = $input;
        if ($members !== null) {
            $logData['member_operations'] = $memberOperations;
        } elseif ($memberIds !== null) {
            $logData['member_ids'] = $memberIds;
        }
        activity_log()->withObject($item)->add('修改 Team', $logData);
        
        return $this->sendResponse(null, 'Team updated successfully.');
    }
    
    /**
     * Process batch member operations (create, update, associate)
     *
     * @param int $teamId
     * @param array $members
     * @param int $tenantId
     * @return array
     */
    private function processMembersBatchOperations($teamId, $members, $tenantId)
    {
        $operations = [
            'created' => [],
            'updated' => [],
            'associated' => []
        ];
        
        $currentMemberIds = [];
        
        foreach ($members as $memberData) {
            if (isset($memberData['id']) && $memberData['id']) {
                // Update existing member
                $memberId = $memberData['id'];
                
                // Extract only valid Member fields
                $updateData = [];
                $validFields = ['card_id', 'name', 'phone', 'email', 'department', 'position', 'status', 'metadata'];
                
                foreach ($validFields as $field) {
                    if (array_key_exists($field, $memberData)) {
                        $updateData[$field] = $memberData[$field];
                    }
                }
                
                if (!empty($updateData)) {
                    $updated = Member::where('id', $memberId)->update($updateData);
                    if ($updated) {
                        $operations['updated'][] = $memberId;
                    }
                }
                
                // Ensure member is associated with this team
                Member::where('id', $memberId)->update(['team_id' => $teamId]);
                $currentMemberIds[] = $memberId;
                
            } else {
                // Create new member
                $createData = [];
                $validFields = ['card_id', 'name', 'phone', 'email', 'department', 'position', 'status', 'metadata'];
                
                foreach ($validFields as $field) {
                    if (array_key_exists($field, $memberData)) {
                        $createData[$field] = $memberData[$field];
                    }
                }
                
                // Set required fields
                $createData['tenant_id'] = $tenantId;
                $createData['team_id'] = $teamId;
                $createData['status'] = $createData['status'] ?? 1; // Default active
                
                if (!empty($createData['name'])) { // Ensure name is provided for new members
                    $newMember = Member::create($createData);
                    $operations['created'][] = $newMember->id;
                    $currentMemberIds[] = $newMember->id;
                }
            }
        }
        
        // Remove team association for members not in the current list
        Member::where('team_id', $teamId)
            ->whereNotIn('id', $currentMemberIds)
            ->update(['team_id' => null]);
        
        $operations['associated'] = $currentMemberIds;
        
        return $operations;
    }
    /**
     * @OA\Delete(
     *      path="/system/team/{id}",
     *      operationId="TeamController::destroy",
     *      tags={"System.Team"},
     *      summary="Remove the specified Team from storage.",
     *      description="Delete one Team by id",
     *      security={{"api_http_auth": {}}},
     *      @OA\Parameter(
     *          name="id",
     *          description="Team id",
     *          required=true,
     *          in="path",
     *          @OA\Schema(
     *              type="integer"
     *          )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation"
     *      ),
     *      @OA\Response(response=400, description="Bad request")
     *  )
     *
     * @param int $id
     *
     * @param TeamDeleteRequest $request
     * @return JsonResponse
     * @throws \Illuminate\Auth\Access\AuthorizationException
     */
    public function destroy($id, TeamDeleteRequest $request)
    {
        /**
         * @var $item Team
         */
        $item = $this->repository->findWithoutFail($id);
        if (empty($item)) {
            return $this->sendError('Team not found');
        }
        $this->authorize('delete', $item);
        $this->repository->delete($id);
        activity_log()->withObject($item)->add('删除 Team', $item->toArray());
        return $this->sendResponse(null, 'Team deleted successfully.');
    }

    /**
     * @OA\Post(
     *      path="/system/team/batch_delete",
     *      operationId="TeamController::batchDestroy",
     *      tags={"System.Team"},
     *      summary="Batch delete specified Teams",
     *      description="Delete multiple teams by ID array. Teams with associated members cannot be deleted unless force=true",
     *      security={{"api_http_auth": {}}},
     *      @OA\RequestBody(
     *          description="Team IDs to delete and optional force flag",
     *          required=true,
     *          @OA\JsonContent(
     *              type="object",
     *              required={"ids"},
     *              @OA\Property(property="ids", type="array", description="Array of Team IDs to delete", @OA\Items(type="integer")),
     *              @OA\Property(property="force", type="boolean", description="Force delete teams with associated members (will remove member associations)", default=false)
     *          )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="Teams deleted successfully",
     *          @OA\JsonContent(
     *              type="object",
     *              @OA\Property(property="success", type="boolean", example=true),
     *              @OA\Property(property="data", type="integer", description="Number of teams deleted"),
     *              @OA\Property(property="message", type="string", example="Teams deleted successfully.")
     *          )
     *      ),
     *      @OA\Response(
     *          response=400,
     *          description="Cannot delete teams with associated members",
     *          @OA\JsonContent(
     *              type="object",
     *              @OA\Property(property="success", type="boolean", example=false),
     *              @OA\Property(property="message", type="string", example="Cannot delete teams with associated members"),
     *              @OA\Property(property="data", type="object",
     *                  @OA\Property(property="teams_with_members", type="array", description="Teams that have associated members",
     *                      @OA\Items(type="object",
     *                          @OA\Property(property="id", type="integer"),
     *                          @OA\Property(property="name", type="string"),
     *                          @OA\Property(property="member_count", type="integer")
     *                      )
     *                  )
     *              )
     *          )
     *      )
     *  )
     *
     * @param TeamListRequest $request
     * @return JsonResponse
     * @throws \Illuminate\Auth\Access\AuthorizationException
     */
    public function batchDestroy(TeamListRequest $request)
    {
        $ids = $request->input('ids');
        $force = $request->input('force', false);
        
        if (empty($ids) || !is_array($ids)) {
            return $this->sendError('Invalid input: ids array is required');
        }
        
        $items = $this->repository->findWhereInIds($ids);
        if ($items->isEmpty()) {
            return $this->sendError('No Teams found');
        }

        // Check authorization for each team
        foreach ($items as $item) {
            $this->authorize('delete', $item);
        }

        // Check for teams with associated members
        $teamsWithMembers = [];
        foreach ($items as $item) {
            $memberCount = $item->members()->count();
            if ($memberCount > 0) {
                $teamsWithMembers[] = [
                    'id' => $item->id,
                    'name' => $item->name,
                    'member_count' => $memberCount
                ];
            }
        }

        // If there are teams with members and force is not set, return error
        if (!empty($teamsWithMembers) && !$force) {
            return $this->sendError(
                'Cannot delete teams with associated members. Use force=true to delete teams and remove member associations.',
                [
                    'teams_with_members' => $teamsWithMembers
                ]
            );
        }

        DB::transaction(function () use ($ids, $items) {
            // If force delete, first remove member associations
            Member::whereIn('team_id', $ids)->update(['team_id' => null]);
            
            // Then delete the teams
            $this->repository->deleteWhereIn($ids);
        });
        
        $deletedCount = count($items);
        activity_log()->add("批量删除 Team (".implode(',', $ids).")");

        return $this->sendResponse($deletedCount, 'Teams deleted successfully.');
    }
}