<?php
/**
 * Please refer to:<br>
 * https://github.com/DarkaOnLine/L5-Swagger/blob/master/tests/storage/annotations/OpenApi/Anotations.php
 * https://swagger.io/specification/
 *
 * @OA\Info(
 *      version="1.0.0",
 *      title="IoT Platform Backend API",
 *      description="Please refer to: <br>
<a href='https://github.com/DarkaOnLine/L5-Swagger/blob/master/tests/storage/annotations/OpenApi/Anotations.php' target='_blank'>https://github.com/DarkaOnLine/L5-Swagger/blob/master/tests/storage/annotations/OpenApi/Anotations.php</a><br>
<a href='https://swagger.io/spbaecification/' target='_blank'>https://swagger.io/specification/</a>",
 *      @OA\Contact(
 *          email="<EMAIL>"
 *      )
 * )
 * @OA\ExternalDocumentation(
 *     description="Find out more about Swagger",
 *     url="http://swagger.io"
 * )
 *
 * @OA\SecurityScheme(
 *     securityScheme="api_http_auth",
 *     type="http",
 *     in="header",
 *     scheme="bearer",
 *     bearerFormat="JWT",
 * )
 *
 * @OA\Server(
 *     url=L5_SWAGGER_CONST_LOCALHOST,
 *     description="The Localhost Development API Server",
 * )
 *
 */

/**
 * *************************************
 * PARAMETERS
 * *************************************
 *
 * @OA\Parameter(parameter="_includeSubTenants", name="_includeSubTenants", description="Include sub tenants", required=false, in="query",
 *      @OA\Schema(type="boolean")
 * ),
 * @OA\Parameter(parameter="_includeSubWatchPoints", name="_includeSubWatchPoints", description="Include sub watchpoints", required=false, in="query",
 *      @OA\Schema(type="boolean")
 * ),
 *
 * @OA\Parameter(name="IndexQuery", description="Index query parameters. Please check IndexQuerySchema.", required=false,
 *     in="query", style="form", explode="false",
 *    @OA\Schema(ref="#/components/schemas/IndexQuerySchema"),
 * ),
 *
 * @OA\Schema(schema="IndexQuerySchema", type="object", example={
 *      "search": "",
 *      "filter": {"age:gt:18", "status:in:pending|active", "created_at:gte:2025-09-11 16:00:00", "created_at:lte:2025-09-19 16:00:00"},
 *      "sort": {"-id"},
 *      "_with": {},
 *      "with_count": {},
 *      "page": 1,
 *      "size": 15,
 *     },
 *     description="!!! When with relationships, use camelCase, NOT snake_case!!!",
 *      @OA\Property(property="search", type="string",  description="Global search term. Use `%` to wildcard search.",),
 *      @OA\Property(property="filter", type="array",
 *          description="Supports two filter formats:<br>
 *      1. Array format: `filter[]=field:operator:value` (multiple parameters)<br>
 *      2. Comma-separated format: `filter=field1:operator:value1,field2:operator:value2` (single parameter)<br>
 *      Available operators: `gt` `gte` `lt` `lte` `eq` `ne` `neq` `like` `in` `nin` `null` `not-null` `true` `false`<br>
 *      Examples:<br>
 *      - Array: `filter[]=age:gt:18&filter[]=status:in:pending|active`<br>
 *      - Comma-separated: `filter=age:gt:18,status:in:pending|active,created_at:gte:2025-09-11 16:00:00,created_at:lte:2025-09-19 16:00:00`",
 *          @OA\Items(type="string", pattern="^.*:(gt|gte|lt|lte|eq|ne|neq|like|in|nin|null|not\-null|true|false):.*$"),
 *      ),
 *      @OA\Property(property="sort", type="array", default={"-id"}, description="Sorting fields. Leading minus as DESC sorting.",
 *          @OA\Items(type="string"),
 *      ),
 *      @OA\Property(property="_with",  type="array",  description="Get relationships.",
 *          @OA\Items(type="string"),
 *      ),
 *      @OA\Property(property="with_count", type="array",  description="Get the count number of relationships.",
 *          @OA\Items(type="string"),
 *      ),
 *      @OA\Property(property="page",type="integer", default=1 , description="Page number. Start from 1.",),
 *      @OA\Property(property="size", type="integer", default=15 ,description="Items per page. ",),
 * ),
 *
 * @OA\Parameter(parameter="with", name="with", required=false, in="query", explode=false, description="Get relationships.",
 *      @OA\Schema(type="array", @OA\Items(type="string") ),
 * ),
 *
 */

/**
 * *********
 * SCHEMAS
 * *********
 *
 * @OA\Schema(schema="id", type="integer", description="The ID.",),
 * @OA\Schema(schema="deleted_at", type="string", description="The datetime when this record is deleted. Null represents not deleted.",),
 * @OA\Schema(schema="created_at", type="string", description="The datetime when this record is created.",),
 * @OA\Schema(schema="updated_at", type="string", description="The datetime when this record is updated. Null represents not updated.",),
 * @OA\Schema(schema="value_type", enum={"integer","float","string","bool","coordinate"}, type="string", description="`integer` `float` `string` `bool` `coordinate`"),
 */

/**
 * ********
 * RESPONSE
 * ********
 *
 * @OA\Schema(schema="IndexResponseLinks", type="object", description="Quick links for pagination.",
 *     required={"first","last","prev","next"},
 *     @OA\Property(property="first", type="string", description="The link to the first page."),
 *     @OA\Property(property="last", type="string", description="The link to the last page."),
 *     @OA\Property(property="prev", type="string", description="The link to the previous page."),
 *     @OA\Property(property="next", type="string", description="The link to the next page."),
 * ),
 * @OA\Schema(schema="IndexResponseMeta", type="object", description="Meta data for pagination.",
 *     required={"current_page","from","last_page","path","per_page","to","total"},
 *     @OA\Property(property="current_page", type="integer", description="Current page number."),
 *     @OA\Property(property="from", type="integer", description="The first record index of current page scope."),
 *     @OA\Property(property="last_page", type="integer", description="The last page number."),
 *     @OA\Property(property="path", type="string", description="This API path."),
 *     @OA\Property(property="per_page", type="integer", description="How many records per page."),
 *     @OA\Property(property="to", type="integer", description="The last record index of current page scope."),
 *     @OA\Property(property="total", type="integer", description="Total matched records."),
 * ),
 *
 * @OA\Schema(schema="BaseStoreResponse200", type="object", description="Response of an store API.",
 *     required={"success","data","message"},
 *     @OA\Property(property="success", type="boolean", description="Did the request success or not.",),
 *     @OA\Property(property="data", type="integer", description="Primary Key of created record.",),
 *     @OA\Property(property="message", type="string", description="Response message.",),
 * ),
 *
 * @OA\Response(response="Export200", description="Response of an export API.",
 *     @OA\MediaType(mediaType="application/octet-stream",
 *        @OA\Schema(type="string", format="binary" )
 *    )
 * ),
 *
 */
