APP_NAME="IoT Platform"
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://localhost:8080
APP_FRONTEND_URL=http://localhost:4200
APP_DASHBOARD_URL=
APP_TRUST_PROXIES=
API_SIGN_SECRET=zediot9988
API_SIGN_SECRET_THIRD_PARTY=123456

LOG_CHANNEL=stack
LOG_LEVEL=debug

DB_CONNECTION=pgsql
DB_HOST=database
DB_PORT=5432
DB_DATABASE=postgres
DB_USERNAME=postgres
DB_PASSWORD=password

BROADCAST_DRIVER=log
CACHE_DRIVER=file
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120
CACHE_PREFIX=zediot

REDIS_CLIENT=predis
REDIS_PREFIX=laravel_database_
REDIS_HOST=redis
REDIS_PASSWORD=ReplaceMeWhenProduction
REDIS_PORT=6379

DEVICE_DATA_ROUTER_REDIS_HOST=redis
DEVICE_DATA_ROUTER_REDIS_PORT=6379  
DEVICE_DATA_ROUTER_REDIS_PASSWORD=ReplaceMeWhenProduction
DEVICE_DATA_ROUTER_REDIS_DB=7

AMQP_HOST=
AMQP_PORT=
AMQP_USERNAME=
AMQP_PASSWORD=
AMQP_VHOST=
AMQP_SEND_COMMAND_EXCHANGE_NAME='fgds.action_assign'
AMQP_SEND_COMMAND_EXCHANGE_TYPE=fanout
AMQP_SEND_COMMAND_ROUTING='fgds_action_assign'
AMQP_WEB_BROADCAST_EXCHANGE_NAME=fgds.mapped_data
AMQP_WEB_BROADCAST_EXCHANGE_TYPE=fanout
AMQP_WEB_BROADCAST_QUEUE_NAME=fgds.mapped_data.web_broadcast

MQTT_HOST=emqx
MQTT_PORT=1883
MQTT_USERNAME=mqttuser
MQTT_PASSWORD=password

MAIL_DRIVER=smtp
MAIL_HOST=
MAIL_PORT=25
MAIL_USERNAME=
MAIL_PASSWORD=
MAIL_ENCRYPTION=null

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_APP_CLUSTER=mt1

MIX_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
MIX_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

JWT_SECRET=SOME_JWT_SECRET
JWT_TTL=1440
JWT_REFRESH_TTL=20160

CLOCKWORK_ENABLE=false
CLOCKWORK_CACHE_ENABLED=true
CLOCKWORK_CACHE_QUERIES=false
CLOCKWORK_DATABASE_ENABLED=true
CLOCKWORK_DATABASE_COLLECT_QUERIES=true
CLOCKWORK_DATABASE_SLOW_THRESHOLD=1000
CLOCKWORK_DATABASE_SLOW_ONLY=false
CLOCKWORK_DATABASE_DETECT_DUPLICATE_QUERIES=false
CLOCKWORK_EMAILS_ENABLED=true
CLOCKWORK_EVENTS_ENABLED=true
CLOCKWORK_LOG_ENABLED=true
CLOCKWORK_QUEUE_ENABLED=true
CLOCKWORK_REDIS_ENABLED=true
CLOCKWORK_ROUTES_ENABLED=true
CLOCKWORK_VIEWS_ENABLED=false
CLOCKWORK_WEB=false
CLOCKWORK_WEB_DARK_THEME=false
CLOCKWORK_COLLECT_DATA_ALWAYS=false
CLOCKWORK_STORAGE=files
CLOCKWORK_STORAGE_FILES_COMPRESS=false
CLOCKWORK_STORAGE_SQL_TABLE=clockwork
CLOCKWORK_STORAGE_EXPIRATION=60
CLOCKWORK_AUTHENTICATION=false
CLOCKWORK_AUTHENTICATION_PASSWORD=iotClockwork
CLOCKWORK_STACK_TRACES_ENABLED=true
CLOCKWORK_STACK_TRACES_LIMIT=10
CLOCKWORK_SERIALIZATION_DEPTH=10
CLOCKWORK_REGISTER_HELPERS=true
CLOCKWORK_SERVER_TIMING=10

ALIBABA_CLOUD_ACCESS_KEY_ID=
ALIBABA_CLOUD_ACCESS_KEY_SECRET=
ALIBABA_CLOUD_SIGN_NAME=
ALIBABA_CLOUD_TEMPLATE_CODE=
