<?php

/*
|--------------------------------------------------------------------------
| Broadcast Channels
|--------------------------------------------------------------------------
|
| Here you may register all of the event broadcasting channels that your
| application supports. The given channel authorization callbacks are
| used to check if an authenticated user can listen to the channel.
|
*/

use Modules\System\Models\PlatformAdmin;
use Modules\System\Models\TenantAdmin;

Broadcast::channel('Modules.System.Models.Account.{id}', function ($user, $id) {
    return (int)$user->id === (int)$id;
});

Broadcast::channel('device.{device}', function ($user, \Modules\System\Models\Device $device) {
    return $user->can('view',$device);
});

Broadcast::channel('telemetry_value.{identifier}', function ($user,$identifier) {
    return true;
});

Broadcast::channel('device_with_tenant.{tenantId}', function ($user, $tenantId) {
    /**
     * Platform admins shall pass
     */
    if ($user->isSuperAdmin() || $user->isPlatformAdmin()) {
        logger()->debug("PlatformAdmin {$user->login_name}  subscription channel : device_with_tenant.".$tenantId);
        return true;
    }
    /**
     * Tenant Admin checking
     * @var $tenantAdmin       TenantAdmin
     */
    if ($user->isTenantAdmin()) {
        $tenantAdmin = $user->accountable;
        if ($tenantAdmin->canViewTenantResource('tenant', $tenantId)) {
            logger()->debug("TenantAdmin {$user->login_name}  subscription channel : device_with_tenant.".$tenantId);
            return true;
        }
    }
});

Broadcast::channel('device_anomaly_with_tenant.{tenantId}', function ($user, $tenantId) {
    /**
     * Platform admins shall pass
     */
    if ($user->isSuperAdmin() || $user->isPlatformAdmin()) {
        logger()->debug("PlatformAdmin {$user->login_name}  subscription channel : device_with_tenant.".$tenantId);
        return true;
    }
    /**
     * Tenant Admin checking
     * @var $tenantAdmin       TenantAdmin
     */
    if ($user->isTenantAdmin()) {
        $tenantAdmin = $user->accountable;
        if ($tenantAdmin->canViewTenantResource('tenant', $tenantId)) {
            logger()->debug("TenantAdmin {$user->login_name}  subscription channel : device_with_tenant.".$tenantId);
            return true;
        }
    }
});

Broadcast::channel('operator_status.{tenantId}', function ($user, $tenantId) {
    /**
     * Platform admins shall not pass
     */
    if ($user->isSuperAdmin() || $user->isPlatformAdmin()) {
        return false;
    }
    /**
     * Tenant Admin checking
     * @var $tenantAdmin       TenantAdmin
     */
    if ($user->isTenantAdmin()) {
        $tenantAdmin = $user->accountable;
        if ($tenantAdmin->canViewTenantResource('tenant', $tenantId)) {
            logger()->debug("TenantAdmin {$user->login_name}  subscription channel : operator_status.".$tenantId);
            return true;
        }
    }
});

// Realtime member data channel - for specific member's real-time data
Broadcast::channel('member.realtime.{memberID}', function ($user, $memberID) {
    // Platform admins can access all member data
    if ($user->isSuperAdmin() || $user->isPlatformAdmin()) {
        logger()->debug("PlatformAdmin {$user->login_name} subscription channel: member.realtime.{$memberID}");
        return true;
    }
    
    // Tenant admins can access member data within their tenant scope
    if ($user->isTenantAdmin()) {
        /** @var TenantAdmin $tenantAdmin */
        $tenantAdmin = $user->accountable;
        // Check if member belongs to this tenant (simplified check for now)
        // In production, you might want to verify member-tenant relationship
        logger()->debug("TenantAdmin {$user->login_name} subscription channel: member.realtime.{$memberID}");
        return true; // For now, allow all tenant admins
    }
    
    return false;
});

// Realtime tenant data channel - for tenant-level real-time monitoring
Broadcast::channel('tenant.realtime.{tenantID}', function ($user, $tenantID) {
    // Platform admins can access all tenant data
    if ($user->isSuperAdmin() || $user->isPlatformAdmin()) {
        logger()->debug("PlatformAdmin {$user->login_name} subscription channel: tenant.realtime.{$tenantID}");
        return true;
    }
    
    // Tenant admins can only access their own tenant's data
    if ($user->isTenantAdmin()) {
        /** @var TenantAdmin $tenantAdmin */
        $tenantAdmin = $user->accountable;
        if ($tenantAdmin->canViewTenantResource('tenant', $tenantID)) {
            logger()->debug("TenantAdmin {$user->login_name} subscription channel: tenant.realtime.{$tenantID}");
            return true;
        }
    }
    
    return false;
});

// Realtime tenant data channel - for tenant-level real-time monitoring
Broadcast::channel('tenant.device.telemetry.{tenantID}', function ($user, $tenantID) {
    // Platform admins can access all tenant data
    if ($user->isSuperAdmin() || $user->isPlatformAdmin()) {
        logger()->debug("PlatformAdmin {$user->login_name} subscription channel: tenant.device.telemetry.{$tenantID}");
        return true;
    }
    
    // Tenant admins can only access their own tenant's data
    if ($user->isTenantAdmin()) {
        /** @var TenantAdmin $tenantAdmin */
        $tenantAdmin = $user->accountable;
        if ($tenantAdmin->canViewTenantResource('tenant', $tenantID)) {
            logger()->debug("TenantAdmin {$user->login_name} subscription channel: tenant.device.telemetry.{$tenantID}");
            return true;
        }
    }
    
    return false;
});
