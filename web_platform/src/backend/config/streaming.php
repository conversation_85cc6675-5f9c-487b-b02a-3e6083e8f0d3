<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Streaming Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for streaming media integration with Oryx/SRS
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Streaming Base URL
    |--------------------------------------------------------------------------
    |
    | The base URL for the streaming server (Oryx/SRS)
    | Used to generate streaming URLs with authentication tokens
    |
    */
    'base_url' => env('STREAMING_BASE_URL', 'http://k8s.draegersafety.com.cn:31146'),

    /*
    |--------------------------------------------------------------------------
    | Streaming Secret Key
    |--------------------------------------------------------------------------
    |
    | Secret key used for HMAC token generation and validation
    | Make sure this is kept secret and consistent across environments
    |
    */
    'secret' => env('STREAMING_SECRET', 'default-streaming-secret-key'),

    /*
    |--------------------------------------------------------------------------
    | Default Token Expiry (minutes)
    |--------------------------------------------------------------------------
    |
    | Default expiration time for streaming authentication tokens in minutes
    |
    */
    'token_expiry_minutes' => env('STREAMING_TOKEN_EXPIRY_MINUTES', 60),
];
