<?php

namespace Scaffold\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Str;
use Modules\System\Extensions\SettingHelper;
use Modules\System\Models\Account;
use Modules\System\Models\Permission;
use Modules\System\Models\Role;
use Modules\System\Models\Version;

class PreFill extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'scaffold:pre-fill';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Pre-fill initialization data.';

    /**
     * @var Role
     */
    private $superAdminRole = null;
    /**
     * @var Role
     */
    private $platformAdminSampleRole = null;
    /**
     * @var Role
     */
    private $platformAdminOperatorRole = null;
    /**
     * @var Role
     */
    private $tenantAdminSampleRole = null;
    /**
     * @var Role
     */
    private $defaultPlatformAdminRole = null;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        // Roles
        $this->preFillRoles();
        // Permissions
        $this->preFillPermissions();
        // Super admin account
        $this->preFillSuperAdminAccount();
        // Draeger device molds
        $this->preFillDraegerDeviceMolds();
        // Versions
        $this->preFillVersions();
    }

    private function preFillRoles()
    {
        $this->superAdminRole = Role::where('name', Role::SUPER_ADMIN)->first();
        if (!$this->superAdminRole) {
            $this->superAdminRole = factory(Role::class)->state(Role::SUPER_ADMIN)->create([
                'translation' => ["en" => "Super Admin", "zh_CN" => "超级管理员"],
            ]);
        }

        $this->platformAdminSampleRole = Role::where('name', Role::PLATFORM_ADMIN_SAMPLE)->first();
        if (!$this->platformAdminSampleRole) {
            $this->platformAdminSampleRole = factory(Role::class)->state(Role::PLATFORM_ADMIN_SAMPLE)->create([
                'translation' => ["en" => "Platform Admin Sample", "zh_CN" => "平台管理员模板"],
            ]);
        }

        $this->platformAdminOperatorRole = Role::where('name', Role::PLATFORM_OPERATOR)->first();
        if (!$this->platformAdminOperatorRole) {
            $this->platformAdminOperatorRole = factory(Role::class)->state(Role::PLATFORM_OPERATOR)->create([
                'translation' => ["en" => "Platform Operator", "zh_CN" => "平台操作员"],
            ]);
        }

        $this->tenantAdminSampleRole = Role::where('name', Role::TENANT_ADMIN_SAMPLE)->first();
        if (!$this->tenantAdminSampleRole) {
            $this->tenantAdminSampleRole = factory(Role::class)->state(Role::TENANT_ADMIN_SAMPLE)->create([
                'translation' => ["en" => "Tenant Admin Sample", "zh_CN" => "租户管理员模板"],
            ]);
        }

        $this->defaultPlatformAdminRole = Role::where('name', Role::PLATFORM_DEFAULT_ADMIN)->first();
        if (!$this->defaultPlatformAdminRole) {
            $this->defaultPlatformAdminRole = factory(Role::class)->state(Role::PLATFORM_ADMIN_SAMPLE)->create([
                'name' => Role::PLATFORM_DEFAULT_ADMIN,
                'translation' => ["en" => "Platform Default Admin", "zh_CN" => "平台默认管理员"],
            ]);
        }

        $this->line('Roles filled up.');
    }

    private function preFillPermissions()
    {
        $this->call('system:permission-refresh', [
            '--remove-unused' => 1,
        ]);

        $permissions = Permission::all();
        $this->platformAdminSampleRole->syncPermissions($permissions->filter(function (Permission $permission) {
            return $permission->isPlatformPermission();
        }));

        $this->platformAdminOperatorRole->syncPermissions($permissions->filter(function (Permission $permission) {
            return $permission->isPlatformOperatorPermission();
        }));

        $this->tenantAdminSampleRole->syncPermissions($permissions->filter(function (Permission $permission) {
            return $permission->isTenantPermission();
        }));

        $this->defaultPlatformAdminRole->syncPermissions($this->platformAdminSampleRole->permissions);

        $this->line('Roles filled up.');
    }

    private function preFillSuperAdminAccount()
    {
        $username = 'superadmin';
        $password = Str::random();
        $superAdminAccount = Account::where('login_name', $username)->first();
        if (!$superAdminAccount) {
            $superAdminAccount = factory(Account::class)->state(Role::SUPER_ADMIN)->create([
                'is_active' => true,
                'login_name' => $username,
                'password' => $password,
                'last_login_at' => null,
                'login_times' => 0,
                'deleted_at' => null,
            ]);
            $email = $this->ask("What's the email address of the super admin account?");
            $email = $email ?: '<EMAIL>';

            SettingHelper::getInstance()->setDefault('superadmin.email', $email);

            $this->line("Super admin account created:");
            $this->table(['item', 'value'], [
                ['item' => 'Username', 'value' => $username],
                ['item' => 'Password', 'value' => $password],
                ['item' => 'Email', 'value' => $email],
            ]);
        }
    }

    private function preFillDraegerDeviceMolds()
    {
        $this->call('db:seed', [
            '--class' => 'DraegerDeviceMoldsSeeder'
        ]);
    }

    private function preFillVersions()
    {
        $androidVersion = Version::query()->where('type', '=', Version::TYPE_ANDROID)->first();
        if (!$androidVersion) {
            Version::query()->create([
                'version_num' => '0.0.1',
                'type' => Version::TYPE_ANDROID
            ]);
        }
        $iosVersion = Version::query()->where('type', '=', Version::TYPE_IOS)->first();
        if (!$iosVersion) {
            Version::query()->create([
                'version_num' => '0.0.1',
                'type' => Version::TYPE_IOS
            ]);
        }
        $webVersion = Version::query()->where('type', '=', Version::TYPE_WEB)->first();
        if (!$webVersion) {
            Version::query()->create([
                'version_num' => '0.0.1',
                'type' => Version::TYPE_WEB
            ]);
        }
        $controllerVersion = Version::query()->where('type', '=', Version::TYPE_CONTROLLER)->first();
        if (!$controllerVersion) {
            Version::query()->create([
                'version_num' => 100001,
                'type' => Version::TYPE_CONTROLLER
            ]);
        }
        $this->line('Versions filled up.');
    }
}
