<?php


namespace Scaffold\QueryBuilder;

use Exception;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasOneOrMany;
use Illuminate\Database\Eloquent\Relations\HasOneThrough;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Support\Str;

/**
 * Supports two filter formats:
 * 1. Array format: /api/account?filter[]=age:gt:18&filter[]=status:in:pending|active&filter[]=created_at:gte:2025-09-11 16:00:00&filter[]=created_at:lte:2025-09-19 16:00:00&sort=-age,name&search=张%25&page=1&size=10
 * 2. Comma-separated format: /api/account?filter=age:gt:18,status:in:pending|active,created_at:gte:2025-09-11 16:00:00,created_at:lte:2025-09-19 16:00:00&sort=-age,name&search=张%25&page=1&size=10
 * 
 * Multiple filters formats:
 * - Array: filter[]=field1:operator:value1&filter[]=field2:operator:value2
 * - Comma-separated: filter=field1:operator:value1,field2:operator:value2
 * Supported operators: gt, gte, lt, lte, eq, ne, like, in, nin, null, not-null, true, false
 * Class QueryBuilder
 * @package Scaffold\QueryBuilder
 */
class QueryBuilder
{
    const QUERY_KEY_SEARCH = 'search';
    const QUERY_KEY_FILTER = 'filter';
    const QUERY_KEY_SORT = 'sort';
    const QUERY_KEY_APPEND = 'append';
    const QUERY_KEY_WITH = '_with';
    const QUERY_KEY_WITH_COUNT = 'with_count';
    const QUERY_KEY_PAGE = 'page';
    const QUERY_KEY_PAGE_SIZE = 'size';

    /**
     * @param $modelClassOrQueryBuilder Model|Builder|string
     * @return QueryBuilder
     */
    public static function for($modelClassOrQueryBuilder)
    {
        $builder = new self($modelClassOrQueryBuilder);
        return $builder;
    }

    /**
     * @var string
     */
    private $modelClass;

    /**
     * @var Builder | LengthAwarePaginator
     */
    private $query = null;

    /**
     * @var \Illuminate\Http\Request
     */
    private $request = null;

    private $globalFilterNeeded = true;
    private $columnFilterNeeded = true;
    private $sortingNeeded = true;
    private $appendNeeded = true;
    private $withNeeded = true;
    private $withCountNeeded = true;
    private $extendingNeeded = true;

    public function __construct($modelClassOrQueryBuilder)
    {
        if (is_string($modelClassOrQueryBuilder)) {
            $this->modelClass = $modelClassOrQueryBuilder;
        } elseif (is_a($modelClassOrQueryBuilder, Builder::class)) {
            $this->query = $modelClassOrQueryBuilder;
        } elseif(is_a($modelClassOrQueryBuilder,Relation::class)){
            $this->query = $modelClassOrQueryBuilder;
        }
    }

    /**
     * @param Builder|Relation $query
     * @param null $page
     * @param null $size
     * @return LengthAwarePaginator
     */
    public static function paginate($query, $page = null, $size = null)
    {
        if ($size === null) {
            $size = self::GET_REQUEST()->get(self::QUERY_KEY_PAGE_SIZE, 15);
        }
        if ($page === null) {
            $page = self::GET_REQUEST()->get(self::QUERY_KEY_PAGE, 1);
        }
        $paginator = $query->paginate($size, ['*'], 'page', $page);
        $paginator->appends(self::GENERATE_PAGINATOR_APPENDS());
        return $paginator;
    }

    /**
     * @return bool
     */
    private function isGlobalFilterNeeded(): bool
    {
        return $this->globalFilterNeeded;
    }

    /**
     * @param bool $globalFilterNeeded
     */
    public function ignoreGlobalFilter(): void
    {
        $this->globalFilterNeeded = false;
    }

    /**
     * @return bool
     */
    private function isColumnFilterNeeded(): bool
    {
        return $this->columnFilterNeeded;
    }

    /**
     * @param bool $columnFilterNeeded
     */
    public function ignoreColumnFilter(): void
    {
        $this->columnFilterNeeded = false;
    }

    /**
     * @return bool
     */
    private function isSortingNeeded(): bool
    {
        return $this->sortingNeeded;
    }

    /**
     * @param bool $orderingNeeded
     */
    public function ignoreSorting(): void
    {
        $this->sortingNeeded = false;
    }

    /**
     * @return bool
     */
    private function isAppendNeeded(): bool
    {
        return $this->appendNeeded;
    }

    /**
     * @param bool $appendNeeded
     */
    public function ignoreAppend(): void
    {
        $this->appendNeeded = false;
    }

    /**
     * @return bool
     */
    private function isWithNeeded(): bool
    {
        return $this->withNeeded;
    }

    /**
     * @param bool $withNeeded
     */
    public function ignoreWith(): void
    {
        $this->withNeeded = false;
    }

    /**
     * @return bool
     */
    private function isWithCountNeeded(): bool
    {
        return $this->withCountNeeded;
    }

    /**
     * @param bool $withCountNeeded
     */
    public function ignoreWithCount(): void
    {
        $this->withCountNeeded = false;
    }

    /**
     * @return bool
     */
    private function isExtendingNeeded(): bool
    {
        return $this->extendingNeeded;
    }

    /**
     * @param bool $extendingNeeded
     */
    public function ignoreExtending(): void
    {
        $this->extendingNeeded = false;
    }

    /**
     * @return Builder
     */
    public function getQuery()
    {
        $this->query = $this->initQuery();
        $this->query->select($this->query->getModel()->getTable() . '.*');
        $this->request = $this->getRequest();
        $this->decorateQuery();
        return $this->query;
    }

    /**
     * @return \Illuminate\Http\Request
     */
    private function getRequest()
    {
        return self::GET_REQUEST();
    }

    /**
     * @return \Illuminate\Http\Request
     */
    private static function GET_REQUEST()
    {
        return app('request');
    }

    private function decorateQuery()
    {
        if ($this->isGlobalFilterNeeded()) {
            // Global filter
            $this->applyGlobalFilter();
        }
        if ($this->isColumnFilterNeeded()) {
            // Column filter
            $this->applyColumnFilters();
        }
        if ($this->isSortingNeeded()) {
            // Ordering
            $this->applySortingItems();
        }
        if ($this->isAppendNeeded()) {
            // Append
            $this->applyAppends();
        }
        if ($this->isWithNeeded()) {
            // With
            $this->applyWithItems();
        }
        if ($this->isWithCountNeeded()) {
            // With count
            $this->applyWithCounts();
        }
        if ($this->isExtendingNeeded()) {
            // Extending
            $this->applyExtending();
        }
    }

    /**
     * Global filter
     */
    private function applyGlobalFilter()
    {
        $scopes = $this->getGlobalFilterScopes();

        if (sizeof($scopes) && is_iterable($scopes)) {
            $this->query->where(function (Builder $q) use ($scopes) {
                foreach ($scopes as $scope) {
                    $this->applyScope($scope, $q, 'or');
                }
            });
        }
    }

    /**
     * Column filter
     */
    private function applyColumnFilters()
    {
        $scopes = $this->getColumnFilterScopes();
        $this->applyScopes($scopes);
    }

    /**
     * Ordering
     */
    private function applySortingItems()
    {
        $sorts = $this->getSortingItems();
        $theQuery = null;
        if(method_exists($this->query,'getBaseQuery')){
            $theQuery = $this->query->getBaseQuery();
        }elseif(method_exists($this->query,'getQuery')){
            $theQuery = $this->query->getQuery();
        }
        $NotStartWith = $theQuery->from . '.';
        foreach ($sorts as $column => $direction) {
            if (false === Str::startsWith($column, $NotStartWith) && Str::contains($column, '.') >= 0) {
                // Relationship ordering
                $lastIndex = strrpos($column, '.');
                if ($lastIndex) {
                    $parts = [
                        substr($column, 0, $lastIndex),
                        substr($column, $lastIndex + 1),
                    ];
                } else {
                    throw new \Error('Bad sorting item: ' . $column . ' ' . $direction);
                }
                $column = $this->joinEagerLoadedColumn($parts[0], $parts[1]);
            }
            $this->customOrderBy($column, $direction, $this->query);
            continue;
        }
    }

    /**
     * for customOrderBy
     */
    public function customOrderBy($column, $direction, $theQuery)
    {
        $theQuery->orderBy($column, $direction);
    }

    /**
     * Copy from yajra/laravel-datatables
     * https://github.com/yajra/laravel-datatables/blob/69e4bfa34e42d0992c4a53e23fc4494e134164d5/src/EloquentDataTable.php#L139
     * Join eager loaded relation and get the related column name.
     *
     * @param string $relation
     * @param string $relationColumn
     * @return string
     * @throws Exception
     */
    private function joinEagerLoadedColumn($relation, $relationColumn)
    {
        $table = '';
        $lastQuery = $this->query;
        $mainTable = $this->query->getModel()->getTable();
        foreach (explode('.', $relation) as $eachRelation) {
            $model = $lastQuery->getRelation($eachRelation);
            switch (true) {
                case $model instanceof BelongsToMany:
                    $pivot = $model->getTable();
                    $pivotPK = $model->getExistenceCompareKey();
                    $pivotFK = $model->getQualifiedParentKeyName();
                    $this->performJoin($pivot, $pivotPK, $pivotFK);

                    $related = $model->getRelated();
                    $table = $related->getTable();
                    $tablePK = $related->getForeignKey();
                    $foreign = $pivot . '.' . $tablePK;
                    $other = $related->getQualifiedKeyName();

                    $lastQuery->addSelect($table . '.' . $relationColumn);
                    $this->performJoin($table, $foreign, $other);

                    break;

                case $model instanceof HasOneThrough:
                    $pivot = explode('.', $model->getQualifiedParentKeyName())[0]; // extract pivot table from key
                    $pivotPK = $pivot . '.' . $model->getLocalKeyName();
                    $pivotFK = $model->getQualifiedLocalKeyName();
                    $this->performJoin($pivot, $pivotPK, $pivotFK);

                    $related = $model->getRelated();
                    $table = $related->getTable();
                    $tablePK = $related->getForeignKey();
                    $foreign = $pivot . '.' . $tablePK;
                    $other = $related->getQualifiedKeyName();

                    break;

                case $model instanceof MorphOne:
                    $table = $model->getRelated()->getTable();
                    $foreign = $model->getQualifiedForeignKeyName();
                    $localId = $model->getParentKey();
                    $foreignType = $model->getQualifiedMorphType();
                    $localType = $model->getMorphClass();
                    $foreign = function (\Illuminate\Database\Query\Builder $q) use ($foreign, $localId, $foreignType, $localType) {
                        $q
                            ->where($foreign, '=', $localId)
                            ->where($foreignType, '=', $localType);
                    };
                    $other = null;
                    break;

                case $model instanceof HasOneOrMany:
                    $table = $model->getRelated()->getTable();
                    $foreign = $model->getQualifiedForeignKeyName();
                    $other = $model->getQualifiedParentKeyName();
                    break;

                case $model instanceof BelongsTo:
                    $table = $model->getRelated()->getTable();
                    $foreign = $model->getQualifiedForeignKeyName();
                    $other = $model->getQualifiedOwnerKeyName();
                    break;

                default:
                    throw new Exception('Relation ' . get_class($model) . ' is not yet supported.');
            }
            $relationName = $model->getRelationName();
            list($tableForOrder, $foreign) = $this->customJoinInOrderBy($relationName, $mainTable, $table, $foreign);

            $this->performJoin($tableForOrder, $foreign, $other);
            $lastQuery = $model->getQuery();
        }

        return $table . '.' . $relationColumn;
    }

    /**
     * !!不要删除 $relationName, $mainTable, 用于外部自定义扩展该方法
     * @var string $relationName
     * @var string $mainTable
     * @var string $table
     * @var string $foreign
     */
    public function customJoinInOrderBy($relationName, $mainTable, $table, $foreign)
    {
        return [$table, $foreign];
    }

    /**
     * Perform join query.
     *
     * @param string $table
     * @param string $foreign
     * @param string $other
     * @param string $type
     */
    private function performJoin($table, $foreign, $other, $type = 'left')
    {
        $joins = [];
        foreach ((array)$this->query->getQuery()->joins as $key => $join) {
            $joins[] = $join->table;
        }

        if (!in_array($table, $joins)) {
            $this->query->join($table, $foreign, '=', $other, $type);
        }
    }

    /**
     * FIXME Append
     */
    private function applyAppends()
    {
        $appendsArray = $this->getAppendsArray();
        if (sizeof($appendsArray)) {
            foreach ($appendsArray as $append) {
                $this->query->append($append);
            }
        }
    }

    /**
     * With
     */
    private function applyWithItems()
    {
        $withArray = $this->getWithArray();
        if (sizeof($withArray)) {
            $this->query->with($withArray);
        }
    }

    /**
     * With count
     */
    private function applyWithCounts()
    {
        $withCountsArray = $this->getWithCountsArray();
        if (sizeof($withCountsArray)) {
            $this->query->withCount($withCountsArray);
        }
    }

    /**
     * Extending
     */
    private function applyExtending()
    {
        $scopes = $this->getExtendingScopes();
        $this->applyScopes($scopes);
    }

    /**
     * @param $scopes
     */
    private function applyScopes(?array $scopes = [], $or = false)
    {
        if (is_iterable($scopes)) {
            foreach ($scopes as $scope) {
                if ($or) {
                    $this->applyScope($scope, null, 'or');
                } else {
                    $this->applyScope($scope);
                }
            }
        }
    }

    /**
     * @param ScopeInterface $scope
     */
    private function applyScope(ScopeInterface $scope, ?Builder $query = null, $boolean = 'and')
    {
        $scope->apply($query ?: $this->query, $boolean);
    }

    /**
     * @return array
     */
    private function getGlobalFilterScopes()
    {
        $searchTerm = $this->getRequest()->get(self::QUERY_KEY_SEARCH);
        $scopes = [];
        if ($searchTerm) {
            $model = $this->query->getModel();
            $searchable = object_get($model, 'searchable', []);
            foreach ($searchable as $field) {
                // The searchable field is a relationship method. Skip.
                if (method_exists($model, $field)) {
                    continue;
                }
                $scopes[] = new ColumnFilterScope($field, "like", [$searchTerm]);
            }
        }
        return $scopes;
    }

    /**
     * @return array
     */
    private function getColumnFilterScopes()
    {
        $filters = $this->getFilterHttpParameters();
        $filters = $this->customFilterFilters($filters);
        $scopes = [];
        foreach ($filters as $filter) { //} $filterKey => $filterValue) {
            [
                $field,
                $operator,
                $filterValue,
            ] = $this->parseFilterElement($filter);
            $filterValue = trim($filterValue);
            if (strpos($filterValue, '%') === 0 && strrpos($filterValue, '%') === strlen($filterValue) - 1) {
                // 字符串的前后都有百分号
                $filterValue = '%' . trim(trim($filterValue, "%")) . '%';
            }
            // Smart separator detection: use comma for values containing commas but no pipes, otherwise use pipe
            if (strpos($filterValue, ',') !== false && strpos($filterValue, '|') === false) {
                $values = $this->arrayifyByComma($filterValue);
            } else {
                $values = $this->arrayifyByPipe($filterValue);
            }
            $scopes[] = new ColumnFilterScope($field, $operator, $values);
        }
        return $scopes;
    }

    /**
     * 用于外部继承修改
     * @param $filters
     * @return array
     */
    public function customFilterFilters($filters)
    {
        return $filters;
    }

    /**
     * @return array
     */
    private function getSortingItems()
    {
        $result = [];
        $sorts = $this->getCommaSeparatedHttpParameters(self::QUERY_KEY_SORT);
        $model = $this->query->getModel();
        foreach ($sorts as $sort) {
            [
                $column,
                $direction,
            ] = $this->parseSortItem($sort);
            $qualifiedColumn = $model->qualifyColumn($column);
            $result[$qualifiedColumn] = $direction;
        }
        return $result;
    }

    /**
     * @return array|array[]|false|string|string[]|null
     */
    private function getAppendsArray()
    {
        return $this->getCommaSeparatedHttpParameters(self::QUERY_KEY_APPEND);
    }

    /**
     * @return array|array[]|false|string|string[]|null
     */
    private function getWithArray()
    {
        return $this->getCommaSeparatedHttpParameters(self::QUERY_KEY_WITH);
    }

    /**
     * @return array|array[]|false|string|string[]|null
     */
    private function getWithCountsArray()
    {
        return $this->getCommaSeparatedHttpParameters(self::QUERY_KEY_WITH_COUNT);
    }

    /**
     * Special handling for filter parameters to support both formats:
     * 1. filter[]=field1:op:value&filter[]=field2:op:value (array format)
     * 2. filter=field1:op:value,field2:op:value (comma-separated format)
     * @return array|array[]|false|string|string[]|null
     */
    private function getFilterHttpParameters()
    {
        $rawData = $this->getRequestQuery(self::QUERY_KEY_FILTER);
        
        if (is_string($rawData) && strlen($rawData)) {
            // Check if this string contains multiple filters separated by commas
            if (strpos($rawData, ',') !== false) {
                $splitFilters = $this->smartSplitFilters($rawData);
                if (count($splitFilters) > 1) {
                    return $splitFilters;
                }
            }
            // Treat as single filter
            return [$rawData];
        } elseif (is_array($rawData)) {
            return $rawData;
        }
        return [];
    }

    /**
     * Smart split filters by comma, distinguishing between multiple filters and comma-separated values
     * @param string $filterString
     * @return array
     */
    private function smartSplitFilters(string $filterString): array
    {
        // Split by comma first
        $parts = preg_split('/\s*,\s*/', $filterString);
        
        // Check if each part looks like a complete filter (field:operator:value)
        $validOperators = 'gt|gte|lt|lte|eq|ne|neq|like|in|nin|null|not-null|true|false';
        $filterPattern = '/^[^:]+:(' . $validOperators . '):.+$/';
        
        $allPartsAreFilters = true;
        foreach ($parts as $part) {
            if (!preg_match($filterPattern, trim($part))) {
                $allPartsAreFilters = false;
                break;
            }
        }
        
        // If all parts look like complete filters, return them as separate filters
        if ($allPartsAreFilters && count($parts) > 1) {
            return array_map('trim', $parts);
        }
        
        // Otherwise, treat as a single filter (comma might be part of the value)
        return [$filterString];
    }

    /**
     * @param $key
     * @return array|array[]|false|string|string[]|null
     */
    private function getCommaSeparatedHttpParameters($key)
    {
        $rawData = $this->getRequestQuery($key);
        return $this->arrayifyByComma($rawData);
    }

    /**
     * @param $rawData
     * @return array|array[]|false|string|string[]
     */
    private function arrayifyByComma($rawData)
    {
        return self::ArrayifyBySeparator($rawData, '/\s*,\s*/');
    }

    /**
     * @param $rawData
     * @return array|array[]|false|string|string[]
     */
    private function arrayifyByPipe($rawData)
    {
        return self::ArrayifyBySeparator($rawData, '/\s*\|\s*/');
    }


    /**
     * @param $rawData
     * @param string $separator
     * @return array|array[]|false|string|string[]
     */
    public static function ArrayifyBySeparator($rawData, $separator = '/,/')
    {
        $result = [];
        if (is_string($rawData) && strlen($rawData)) {
            $result = preg_split($separator, $rawData);
        } elseif (is_array($rawData)) {
            $result = $rawData;
        }
        return $result;
    }

    /**
     * TODO getExtendingScopes
     */
    private function getExtendingScopes()
    {
        return [];
    }

    /**
     * @param $queryKey
     * @return array|string|null
     */
    private function getRequestQuery($queryKey)
    {
        $request = $this->getRequest();
        return $request->input($queryKey);
    }

    /**
     * @param string $sort
     * @return array
     */
    private function parseSortItem(string $sort)
    {
        $column = trim($sort);
        $direction = 'asc';
        if (Str::startsWith($column, '-')) {
            $direction = 'desc';
            $column = Str::after($column, '-');
        }
        return [
            $column,
            $direction,
        ];
    }

    /**
     * @param string $filterElement Example: <pre>age:gt:18</pre><pre>status:in:pending|in_progress</pre>"
     * @return array [field, operator, value]
     */
    private function parseFilterElement(string $filterElement)
    {
        $parts = preg_split('/:/', trim($filterElement));
        $field = $parts[0];
        $operator = $this->mapOperator($parts[1] ?? 'eq');
        $value = join(':', array_slice($parts, 2));
        return [
            $field,
            $operator,
            $value,
        ];
    }

    private function mapOperator($operator)
    {
        $map = [
            'gt' => '>',
            'gte' => '>=',
            'lt' => '<',
            'lte' => '<=',
            'eq' => '=',
            'ne' => '!=',
            'neq' => '!=',
            'like' => 'like',
            'in' => '=',
            'nin' => '!=',
            'null' => 'IS NULL',
            'not-null' => 'NOT NULL',
            'true' => 'IS TRUE',
            'false' => 'IS FALSE',
        ];
        return isset($map[$operator]) ? $map[$operator] : null;
    }

    private static function GENERATE_PAGINATOR_APPENDS()
    {
        $data = self::GET_REQUEST()->all();
        unset($data['page']);
        return $data;
    }

    private function initQuery()
    {
        if ($this->query) {
            return $this->query;
        }
        return $this->modelClass::query();;
    }
}
